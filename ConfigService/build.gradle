apply from: "../base_service.gradle"

android {
    String[] javaDir = ['src/main/java', 'src/main/java-cn']
    if (rootProject.ext.buildArea == rootProject.ext.china) {
        javaDir = ['src/main/java', 'src/main/java-cn']
    } else if (rootProject.ext.buildArea == rootProject.ext.overseas) {
        javaDir = ['src/main/java', 'src/main/java-os']
    } else {
        javaDir = ['src/main/java', 'src/main/java-cn']
    }

    sourceSets {
        main {
            java.srcDirs javaDir
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    api project(":service:BaseService")
    implementation project(":lib:baseutil")
    implementation libs.skynet.apm
    implementation project(":lib:hwconstants")
    implementation project(":lib:libhttp")
    implementation project(":lib:store")
    implementation project(":lib:liblog")
    implementation project(":lib:api-plugin:voice-api")

    implementation libs.kotlinx.coroutines.android
    implementation libs.androidx.lifecycle.livedata
    implementation libs.eventBus
}