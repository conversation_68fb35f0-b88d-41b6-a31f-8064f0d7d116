package com.huiwan.configservice.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by three on 15/10/14.
 */
public class StuffInfo {
    public static final int TYPE_RED_PACKET = 1;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("stuff_id")
    public int stuff_id;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("name")
    public String name;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("desc")
    public String desc;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("type")
    public int type;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("detail")
    public WespyGoods detail;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("can_send_barrage")
    public boolean can_send_barrage;

    public WespyGoods getGoodInfo() {
        if (detail == null) {
            detail = new WespyGoods();
        }
        return detail;
    }

    public String getGoodsPrice() {
        getGoodInfo();
        return detail.goods_price;
    }

    public String getGoodsCoin() {
        getGoodInfo();
        return detail.goods_coin;
    }
}
