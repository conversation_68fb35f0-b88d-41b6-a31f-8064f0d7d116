package com.huiwan.configservice.modelAbTest;

import android.os.Build;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.common.ChunkConfig;
import com.huiwan.store.PrefUserUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class AbTest {
    @SerializedName("gift_fly_up")
    public int gift_fly_up = 1;// =1客户端礼物从下飞出；=0客户端只第一次送时飞出，后续连击时不再飞出
    @SerializedName("spy_new_end_show")
    public int spy_new_end_show = 0;
    public static final int MusicHumACR = 0;
    public static final int MusicHumAAC = 1;

    /**
     * 位开关。
     * 1. 对应时新用户默认广场，并记录上次玩友圈时的 tab
     * 2. 不加好友可查看玩友圈
     */
    @SerializedName("circle_friend_test")
    public int circle_friend_test = 3;

    @SerializedName("voice_room_list_style")
    public int voiceRoomListStyle = 0;

    @SerializedName("use_new_text")
    public int useNewText = 0;

    @SerializedName("show_sayguess_new")
    public int showSayGuessNew = 0;

    @SerializedName("rec_version")
    public int rec_version = 0;

    @SerializedName("music_hum_recognize_category")
    public int music_hum_recognize_category = MusicHumAAC;

    @SerializedName("game_type_test")
    public List<Integer> useCocos244List = new ArrayList<>();

    @SerializedName("music_hum_guide_pop")
    public int musicHumGuidePop = 0;

    /**
     * 待确认字段细节使用方式等
     */
    @SerializedName("enc_url")
    public List<String> encUrlList = new ArrayList<>();

    @SerializedName("avatar_wolf_switch")
    public int avatarWolfSwitch = 0;

    @SerializedName("avatar_spy_switch")
    public int avatarSpySwitch = 1;

    @SerializedName("use_quic_switch")
    public int quicSwitch = 1;

    @SerializedName("android_user_vap")
    public int useVap = 1;

    @SerializedName("fragment_download")
    public ChunkConfig chunkConfig = new ChunkConfig();

    @SerializedName("upload_image_webp")
    public boolean useWebp = false; //上传是否走webp上传(全局)

    @SerializedName("upload_head_image_webp")
    public boolean headUseWebp = false; //上传用户头像是否走webp上传

    @SerializedName("zoom_image_size")
    public boolean zoomImage = true; //图片是否拼缩放参数

    @SerializedName("size_for_send_compress")
    public int compressLimitSize = 1024; //二次压缩的大小，单位为kb，配置为0时表示不二次压缩

    @SerializedName("proxy_h5_request")
    public boolean proxyH5Request = false; // 客户端代理h5的http请求

    @SerializedName("cocos_connector")
    public List<Integer> proxyCocosWebSocket = Collections.emptyList(); // 客户端代理cocos的WebSocket

    @SerializedName("disable_res_pre_download")
    public boolean disable_res_pre_download = false;
    /**
     * 22-4sp1新增，客户端本地段数，0表示不录音，1段1分钟
     * WePlay 日韩服 3.2.7 版本新增
     */
    @SerializedName("store_count")
    public int audioRecordCount = 0;

    /**
     * 录音时间有效期，默认3分钟有效期
     */
    @SerializedName("risk_store_audio_valid_sec")
    public int audioValidSec = 180;

    /**
     * 备用链路配置参数
     */
    @SerializedName("backup_addr_config")
    public BackupAddrConfig backupAddrConfig = new BackupAddrConfig();

    @SerializedName("open_voice_room_age_window")
    public boolean openVoiceAge = false; //语音房年龄弹窗

    @SerializedName("is_pop_age_window")
    public boolean openHomeAge = false; //JK首页年龄弹窗

    @SerializedName("is_use_httpdns")
    public boolean isUseHttpDns = true;

    @SerializedName("music_hum_con_recognize_category")
    public int musicHumConRecognizeCategory = MusicHumACR;

    @SerializedName("young_mode")
    public boolean openTeenMode = false;

    @SerializedName("qa_client")
    public QAClient qaClient = new QAClient();

    public static class QAClient {
        @SerializedName("byte_size")
        public int byteSize = 3200;

        @SerializedName("br")
        public boolean br = true;
    }

    @SerializedName("cocos_use_web")
    public boolean isCocosUseWeb = true;

    @SerializedName("is_dynamic_test")
    public boolean isDynamicTest = LibBaseUtil.buildDebug();

    @SerializedName("android_client_setting")
    public Map<String, String> clientSwitch = Collections.emptyMap();

    /**
     * 是否 强制使用增量更新 cocos inGameRes
     */
    @SerializedName("is_cocos_incr_update")
    private boolean forceUpdateByInc = true;

    @SerializedName("open_family_week_game")
    public boolean openFamilyWeekGame = LibBaseUtil.buildDebug();

    public boolean needEnc(String uri) {
        for (String p : encUrlList) {
            if (uri.contains(p)) {
                return true;
            }
        }
        return false;
    }

    public boolean isShowAnim() {
        return gift_fly_up == 1;
    }

    public boolean isDiscoverDefaultSquare() {
        return (circle_friend_test & 1) == 1;
    }

    public boolean canVisitNotFriend() {
        return (circle_friend_test & 2) == 2;
    }

    public boolean showNewSpyGameEnd() {
        return spy_new_end_show == 1;
    }

    public boolean useNewTextView() {
        return useNewText == 1;
    }

    public boolean showSayGuessTips() {
        return showSayGuessNew == 1;
    }

    public boolean showNewFriendList() {
        return rec_version == 1;
    }

    public boolean showAvatarGuideWhenEnter() {
        return musicHumGuidePop == 1;
    }

    public boolean isAvatarWolfActivated() {
        return avatarWolfSwitch == 1;
    }

    public boolean isAvatarSpyActivated() {
        return avatarSpySwitch == 1;
    }

    public boolean isUseQuic() {
        return quicSwitch == 1;
    }

    public boolean useVap() {
        if (PrefUserUtil.getInstance().containsKey(PrefUserUtil.USE_VAP)) {
            return PrefUserUtil.getInstance().getBoolean(PrefUserUtil.USE_VAP, false);
        }
        return useVap == 1;
    }

    public boolean isTeenMode() {
        return openTeenMode;
    }

    public boolean isForceUpdateByInc() {
        return forceUpdateByInc;
    }

    public int getRestartForChangeLanguage() {
        int defaultSwitch = LibBaseUtil.buildDebug() ? Build.VERSION_CODES.Q : Integer.MAX_VALUE;
        return getIntFromSwitch("restart_for_change_language", defaultSwitch);
    }

    public boolean getBooleanFromSwitch(String key) {
        return getBooleanFromSwitch(key, false);
    }

    public boolean getBooleanFromSwitch(String key, boolean defaultValue) {
        String s = clientSwitch.get(key);
        if (!TextUtils.isEmpty(s)) {
            try {
                return Boolean.parseBoolean(s);
            } catch (Exception e) {
                //ignore
            }
        }
        return defaultValue;
    }

    public int getIntFromSwitch(String key) {
        return getIntFromSwitch(key, 0);
    }

    public int getIntFromSwitch(String key, int defaultValue) {
        String s = clientSwitch.get(key);
        if (!TextUtils.isEmpty(s)) {
            try {
                return Integer.parseInt(s);
            } catch (Exception e) {
                //ignore
            }
        }
        return defaultValue;
    }

    public long getLongFromSwitch(String key) {
        return getLongFromSwitch(key, 0L);
    }

    public long getLongFromSwitch(String key, long defaultValue) {
        String s = clientSwitch.get(key);
        if (!TextUtils.isEmpty(s)) {
            try {
                return Long.parseLong(s);
            } catch (Exception e) {
                //ignore
            }
        }
        return defaultValue;
    }
}
