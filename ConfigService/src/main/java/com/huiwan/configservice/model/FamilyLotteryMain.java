package com.huiwan.configservice.model;

import com.google.gson.annotations.SerializedName;

/**
 * Created by <PERSON>wen on 2019-10-31.
 */
public class FamilyLotteryMain {

    public static final int LOTTERY_LEVEL_LOW = 1;
    public static final int LOTTERY_LEVEL_NORMAL = 2;
    public static final int LOTTERY_LEVEL_HIGH = 3;
    @SerializedName("coins")
    private long coins;
    @SerializedName("cost")
    private int cost;
    @SerializedName("level")
    private int level;//1. 白银  2. 黄金 3. 钻石
    @SerializedName("ok")
    private boolean ok;
    @SerializedName("wheel_count")
    private int wheel_count;
    @SerializedName("free")
    private boolean free;
    @SerializedName("left_count")
    private int leftCount;
    @SerializedName("right_count")
    private int rightCount;

    public int getLotteryLevel() {
        return level;
    }

    public long getFunds() {
        return coins;
    }

    public int getCost() {
        return cost;
    }

    public boolean isCanLottery() {
        return ok;
    }

    public int getLotteryCardCount() {
        return wheel_count;
    }

    public boolean isFree() {
        return free;
    }

    public int getLeftCount() {
        return leftCount;
    }

    public int getRightCount() {
        return rightCount;
    }
}
