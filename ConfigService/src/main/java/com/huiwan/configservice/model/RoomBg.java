package com.huiwan.configservice.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by three on 15/5/13.
 */
public class RoomBg {

    @Expose(serialize = true, deserialize = true)
    @SerializedName("name")
    public String name;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("desc")
    public String desc;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("url")
    public String url;

    public transient int androidRes = -1;
    public transient boolean add = false;

    public RoomBg() {
    }

    public RoomBg(String name, String desc, String url, int color) {
        this.name = name;
        this.desc = desc;
        this.url = url;
        this.color = color;
    }

    public RoomBg(String name, String desc, int androidRes, boolean add) {
        this.name = name;
        this.desc = desc;
        this.androidRes = androidRes;
        this.add = add;
    }

    @SerializedName("color")
    public int color;

    public static final String ADD_LOCAL_IMAGE_URL = "R.drawable.localUrl";

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public String getUrl() {
        return url;
    }

    public int getColor() { return color; }

}
