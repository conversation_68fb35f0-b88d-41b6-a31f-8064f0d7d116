package com.huiwan.configservice.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by three on 15/3/4.
 */
public class WespyGoods {

    public static final int TYPE_IN_APP = 1;
    public static final int TYPE_SUBSCRIBE = 2;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("goods_name")
    public String goods_name; //商品名称

    @Expose(serialize = true, deserialize = true)
    @SerializedName("goods_coin")
    public String goods_coin; //商品金币数

    @SerializedName("chip_coin")
    public String chipCoin; //游戏币数

    @Expose(serialize = true, deserialize = true)
    @SerializedName("goods_price")
    public String goods_price; //商品金额，精确范围0.01，取值范围：［0.01， 10000 0000.00］

    @Expose(serialize = true, deserialize = true)
    @SerializedName("goods_id")
    public int goods_id;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("goods_icon_url")
    public String iconUrl = "";

    @SerializedName("google_product_id")
    public String gpProductId = "";

    @SerializedName("currency")
    public String currency = "";

    /**
     * in app purchase 1, subscribe 2
     */
    @SerializedName("type")
    public int type = TYPE_IN_APP;

    /**
     * 打折信息，不在 config 中。
     * */
    @Expose(serialize = false, deserialize = false)
    private transient String discountUrl = "";

    /**
     * apple的商品id，不在config中，仅unity请求购买的时候会设置，购买成功后回调给unity做校验
     */
    @SerializedName("apple_product_id")
    public String apple_product_id = "";

    /**
     * 打折信息，不在 config 中。
     * CouponApi#getCoinDiscount
     * */
    @Expose(serialize = false, deserialize = false)
    private transient boolean inDiscount = false;

    /** 折扣信息 临时使用*/
    @Expose(serialize = false, deserialize = false)
    private transient String discountLabel = "";

    /**
     * 赠送vip信息，不在 config 中，临时获取
     */
    @Expose(serialize = false, deserialize = false)
    private transient int vipDonateReceiveUid = 0;

    /**
     * 赠送vip信息，不在 config 中，临时获取
     */
    @Expose(serialize = false, deserialize = false)
    private transient String vipDonateComment = "";

    /**
     * 该新用户礼包是否为首冲礼包,>0为是，不在 config 中，临时获取
     */
    @Expose(serialize = false, deserialize = false)
    private transient int firstCharge = 0;

    public String getSubject() {
        return goods_name;
    }

    public String getCoin() {
        return goods_coin;
    }

    public String getTotal_fee() {
        return goods_price;
    }

    public String getTotalFee(int num) {
        try {
            return String.valueOf(Float.parseFloat(goods_price) * num);
        } catch (Exception e) {
            return goods_price;
        }
    }

    public String getGoods_id() {
        return goods_id + "";
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setDiscountUrl(String discountUrl) {
        this.discountUrl = discountUrl;
    }

    public String getDiscountUrl() {
        return discountUrl;
    }

    public void setInDiscount(boolean inDiscount) {
        this.inDiscount = inDiscount;
    }

    public boolean isInDiscount() {
        return inDiscount;
    }

    public int getVipDonateReceiveUid() {
        return vipDonateReceiveUid;
    }

    public void setVipDonateReceiveUid(int vipDonateReceiveUid) {
        this.vipDonateReceiveUid = vipDonateReceiveUid;
    }

    public String getVipDonateComment() {
        return vipDonateComment;
    }

    public void setVipDonateComment(String vipDonateComment) {
        this.vipDonateComment = vipDonateComment;
    }

    public int getFirstCharge() {
        return firstCharge;
    }

    public void setFirstCharge(int firstCharge) {
        this.firstCharge = firstCharge;
    }


    public void setGpProductId(String gpProductId) {
        this.gpProductId = gpProductId;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getGpProductId() {
        return gpProductId;
    }

    public int getType() {
        return type;
    }

    public boolean isGpInApp() {
        return type == 1;
    }

    public String getDiscountLabel() {
        return discountLabel;
    }

    public void setDiscountLabel(String discountLabel) {
        this.discountLabel = discountLabel;
    }
}
