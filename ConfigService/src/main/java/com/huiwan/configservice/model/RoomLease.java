package com.huiwan.configservice.model;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.R;

import java.io.Serializable;

/**
 * Created by three on 15/5/11.
 */
public class RoomLease implements Serializable {

    private static final long serialVersionUID = 4578789187170165189L;

    @SerializedName("lease_type")
    private int lease_type;
    @SerializedName("lease_coin")
    private int lease_coin;
    @SerializedName("lease_time_desc")
    private String lease_time_desc;

    public int getLease_type() {
        return lease_type;
    }

    public int getLeasePrice() {
        return lease_coin;
    }

    public String getLease_time_desc() {
        return lease_time_desc;
    }

    public String getPriceUnit() {
        if (lease_coin > 0) return ResUtil.getStr(R.string.game_room_price_unit);
        return ResUtil.getStr(R.string.game_room_price_unit);
    }
}
