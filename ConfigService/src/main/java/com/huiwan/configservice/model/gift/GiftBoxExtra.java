package com.huiwan.configservice.model.gift;

import com.google.gson.annotations.SerializedName;

/**
 * date 2020/6/29
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class GiftBoxExtra {
    @SerializedName("preview_url")
    public String previewUrl = "";
    @SerializedName("btn_url_highlight")
    public String btnUrlDown = "";
    @SerializedName("btn_url_normal")
    public String btnUrlUp = "";
    @SerializedName("scene_help_url")
    public String helpUrl = "";
    @SerializedName("web_loading_url")
    public String webLoadingUrl = "";
    @SerializedName("is_h5_fullscreen")
    public int h5FullScreen = 0;
    @SerializedName("unlock_tip_id")
    public int unlockTipId = 0;

    @SerializedName("extra_prop_id")
    public int extraPropId = 0;

    @SerializedName("extra_prop_value")
    public int extraPropValue = 0;

}
