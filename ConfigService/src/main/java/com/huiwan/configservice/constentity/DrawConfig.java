package com.huiwan.configservice.constentity;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class DrawConfig {

    @SerializedName("draw_color")
    private List<DrawColor> drawColor = new ArrayList<>();
    @SerializedName("draw_help")
    private String drawHelp = "";
    @SerializedName("draw_help_h5")
    private String drawHelpH5 = "";
    @SerializedName("draw_retract_num")
    private int drawRetractNum = 10;//画猜撤回笔数配置

    public List<DrawColor> getDrawColor() {
        return new ArrayList<>(drawColor);
    }

    public int getMaxGrade() {
        int maxGrade = 0;
        for (DrawColor color:drawColor) {
            if (maxGrade < color.getGrade()) {
                maxGrade = color.getGrade();
            }
        }
        return maxGrade;
    }

    public String getDrawHelp() {
        return drawHelp;
    }

    public String getDrawHelpH5() {
        return drawHelpH5;
    }

    public int getDrawRetractNum() {
        return drawRetractNum;
    }

    public boolean containsLevel(int level) {
        for (DrawColor c:drawColor) {
            if (c.getGrade() == level) {
                return true;
            }
        }
        return false;
    }

    public List<DrawColor> getColorsByLevel(int level) {
        List<DrawColor> list = new ArrayList<>(2);
        for (DrawColor c:drawColor) {
            if (c.getGrade() == level) {
                list.add(c);
            }
        }
        return list;
    }
}