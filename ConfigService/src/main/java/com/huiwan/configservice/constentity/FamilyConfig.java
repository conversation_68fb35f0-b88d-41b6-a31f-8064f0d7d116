package com.huiwan.configservice.constentity;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.configservice.IConfig;
import com.huiwan.configservice.model.FamilyLotteryMain;
import com.huiwan.constants.ConfigId;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-10-23.
 */
public class FamilyConfig implements IConfig {

    @SerializedName("version_num")
    private int version;
    @SerializedName("md5_version")
    private String versionMD5 = "";
    @SerializedName("region")
    private String region = "";

    @SerializedName("config")
    private FamilyConfigDetail config = new FamilyConfigDetail();

    public int getModNameCoin() {
        return config.mod_name_coin;
    }

    public List<FamilyConfigDetail.TitleConfigInfo> getTitleList() {
        return config.titleList;
    }

    public FamilyConfigDetail.TitleConfigInfo getTitleConfigInfo(int hid) {
        return config.getFamilyHonorTitleInfo(hid);
    }

    public int getModAvatarCoin() {
        return config.mod_avatar_coin;
    }

    public int getCreateFamilyCoin() {
        return config.create_family_coin;
    }

    public String getFamilyHelpUrl() {
        return config.family_help_url;
    }

    public String getHonorTitleHelpUrl() {
        return config.honorTitleHelpUrl;
    }

    public String getFamilyRankHelpUrl() {
        return config.family_rank_help_url;
    }

    public int getCpc() {
        return config.wheel_card_coin;
    }

    @Override
    public int getConfigId() {
        return ConfigId.FAMILY_CONFIG_ID;
    }

    @Override
    public int getVersion() {
        return version;
    }

    @Override
    public String getVersionMD5() {
        return versionMD5;
    }

    @Override
    public String getRegion() {
        return region;
    }

    public boolean isAvailable() {
        if (!TextUtils.isEmpty(versionMD5)){
            return true;
        } else {
            return version > 0;
        }
    }

    public int getMinCreateFlower() {
        return config.create_family_min_flower;
    }

    public List<FamilyConfigDetail.LotteryReward> getLotteryReward(int level) {
        List<FamilyConfigDetail.LotteryReward> dataList = new ArrayList<>();
        if (level == FamilyLotteryMain .LOTTERY_LEVEL_LOW) {
            dataList.addAll(config.wheel_silver_lottery);
        } else if (level == FamilyLotteryMain.LOTTERY_LEVEL_NORMAL){
            dataList.addAll(config.wheel_gold_lottery);
        } else if (level == FamilyLotteryMain.LOTTERY_LEVEL_HIGH) {
            dataList.addAll(config.wheel_diamond_lottery);
        }
        return dataList;
    }

    public String getStealHelpUrl() {
        return config.steal_help_url;
    }

    public int getDonateCoin(int coin) {
        try {
            if (config.donate_coin_prop.size() > 1) {
                return coin * config.donate_coin_prop.get(1) / config.donate_coin_prop.get(0);
            }
        } catch (Exception e) {
            TimeLogger.err("");
        }
        return -1;
    }

    public int getDonateActive(int coin) {
        try {
            if (config.donate_active_prop.size() > 1) {
                return coin * config.donate_active_prop.get(1) / config.donate_active_prop.get(0);
            }
        } catch (Exception e) {
            TimeLogger.err("");
        }
        return -1;
    }

    public String getLevelUpTips(int currentLevel) {
        for (FamilyConfigDetail.LevelUpTips tips: config.level_up_tips) {
            if (currentLevel + 1 == tips.level) return tips.text;
        }
        return "";
    }

    public int getFamilyVoiceRoomMaintainCost() {
        return config.voice_room_maintain_cost;
    }

    public int getFamilyVoiceRoomMaintainCostByLevel(int level) {
        return config.getFamilyVoiceRoomMaintainCostByLevel(level);
    }

    public int getMaxRank() {
        return config.max_rank;
    }

    public String getFlowerNotEnoughTip() {
        return config.flower_not_enough_tip;
    }

    public FamilyConfigDetail.TreasureConfigInfo getTreasureByLevel(int level) {
        for (FamilyConfigDetail.TreasureConfigInfo treasure : config.treasureList) {
            if (treasure.level == level) {
                return treasure;
            }
        }
        return new FamilyConfigDetail.TreasureConfigInfo();
    }

    public FamilyConfigDetail.TreasureConfigInfo getTreasureByPropId(int propId) {
        for (FamilyConfigDetail.TreasureConfigInfo treasure : config.treasureList) {
            if (treasure.propId == propId) {
                return treasure;
            }
        }
        return new FamilyConfigDetail.TreasureConfigInfo();
    }

    public String getFamilyWelfareHelpUrl() {
        return config.getFamilyWelfareHelpUrl();
    }

    public FamilyConfigDetail.welfareConfigItem getWelfareConfigItem(int type) {
        return config.getWelfareConfigItem(type);
    }
}
