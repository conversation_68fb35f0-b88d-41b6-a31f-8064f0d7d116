package com.huiwan.configservice.constentity;


import com.google.gson.annotations.SerializedName;
import com.huiwan.configservice.model.ColorInterface;

/**
 * Created by <PERSON>wen on 2019/3/19.
 */
public class DrawColor implements ColorInterface {

    private transient boolean isColorBoard = false;

    @SerializedName("use_grade")
    private int use_grade;
    @SerializedName("color")
    private int color;
    @SerializedName("coin")
    private int coin;
    @SerializedName("exp")
    private int exp;//所需经验

    public static final int COLOR_BOARD_POSITION = 14;//色板是第14个

    @Override
    public int getGrade() {
        return use_grade;
    }
    @Override
    public int getColor() {//服务器下发0xffffff
        return color;
    }

    @Override
    public int getCoin() {
        return coin;
    }

    @Override
    public int getExp() {
        return exp;
    }

    public boolean isColorBoard() {
        return isColorBoard;
    }

    public void setColorBoard(boolean colorBoard) {
        isColorBoard = colorBoard;
    }

    public void setUseGrade(int use_grade) {
        this.use_grade = use_grade;
    }

    public void setColor(int color) {
        this.color = color;
    }
}
