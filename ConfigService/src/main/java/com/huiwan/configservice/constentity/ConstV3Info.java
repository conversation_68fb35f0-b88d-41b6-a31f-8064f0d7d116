package com.huiwan.configservice.constentity;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.IConfig;
import com.huiwan.configservice.R;
import com.huiwan.configservice.model.RoomBg;
import com.huiwan.configservice.model.RoomLease;
import com.huiwan.configservice.model.StuffInfo;
import com.huiwan.configservice.model.WespyGoods;
import com.huiwan.configservice.modelAbTest.AbTest;
import com.huiwan.constants.ConfigId;
import com.wepie.liblog.main.FLog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * date 2018/4/3
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class ConstV3Info implements IConfig {

    @SerializedName("region")
    private String region = "";

    @Override
    public int getVersion() {
        return 0;
    }

    @Override
    public String getVersionMD5() {
        return "";
    }

    @Override
    public String getRegion() {
        return region;
    }

    @Override
    public int getConfigId() {
        return ConfigId.CONST_V3_INFO_ID;
    }

    @SerializedName("is_unity_quick_start")
    public boolean isUnityQuickStart;

    /**
     * 当前只在红包这里用到了。
     */
    @SerializedName("stuff_info_list")
    public ArrayList<StuffInfo> stuffInfoList = new ArrayList<>();
    /**
     * 支付提示文案，砖石
     */
    @SerializedName("charge_page_tips")
    public String chargePageTips = "";

    /**
     * 支付提示文案,金币
     */
    @SerializedName("chip_coin_charge_tips")
    public String chipCoinChargeTips = "";

    /**
     * 高级房创建 期限相关信息
     */
    @SerializedName("start_lease_list")
    public ArrayList<RoomLease> startLeaseList = new ArrayList<>();
    /**
     * 是否为管理员设备
     */
    @SerializedName("is_super_admin_device")
    public boolean isSuperAdminDevice = false;
    /**
     * 是否测试用户
     */
    @SerializedName("is_test_device")
    public boolean isTestDevice = false;
    /**
     * 是否测试用户
     */
    @SerializedName("is_external_manager")
    public boolean isExternalManager = false;
    /**
     * 是否开启 root 检测
     */
    @SerializedName("rt_check")
    public boolean rtCheck = false;

    /**
     * 房间背景图片
     */
    @SerializedName("bg_list")
    public ArrayList<RoomBg> roomBgArrayList = new ArrayList<>();
    /**
     * 狼人杀高级房捐赠价格
     */
    @SerializedName("werewolf_lease_list")
    public ArrayList<RoomLease> werewolfLeaseList = new ArrayList<>();
    /**
     * 房间分享文案
     */
    @SerializedName("room_share_title")
    public String roomShareTitle = "{game_type}{rid}";
    /**
     * 你说我猜房间分享文案
     */
    @SerializedName("hwroom_share_title")
    public String hwRoomShareTitle = "{game_type}";
    /**
     * 画猜换词最大花费
     */
    @SerializedName("change_word_cost")
    public int changeWordMaxCost = 30;
    /**
     * 画猜换词花费
     */
    @SerializedName("change_word_cost_list")
    public ArrayList<Integer> changeWordCostList = new ArrayList<>();
    /**
     * miui8 礼物等配置
     */
    @SerializedName("show_miui8_toast")
    public int showMiui8Toast = 1;
    /**
     * 消息撤回开关
     */
    @SerializedName("message_recall_open")
    public boolean messageRecallOpen = false;
    /**
     * 广播预定刷新文案
     */
    @SerializedName("broadcast_refresh_tip")
    public String broadcastRefreshTip = "";
    /**
     * 连续签到金币奖励列表
     */
    @SerializedName("checkin_reward_list")
    public ArrayList<Integer> checkinRewardList = new ArrayList<>();
    /**
     * 用户少于 5 位好友时重新打开玩友圈好友推荐时间
     */
    @SerializedName("discover_recommend_reopen")
    public int discoverRecommendReopen = 43200;
    /**
     * 送礼物帮助页面
     */
    @SerializedName("send_gift_help")
    public String sendGiftHelp = "https://static-h5.weplayapp.com/gift-explain?head=0";
    /**
     * 用户协议页面
     */
    @SerializedName("user_protocol")
    public String userProtocol = "https://weplayapp.com/protocol-yhxy";
    /**
     * 用户隐私协议页面
     */
    @SerializedName("privacy_protocol")
    public String userPrivacyProtocol = "https://weplayapp.com/protocol-yszc-android";

    @SerializedName("vip_service_protocol")
    public String vipServiceProtocol = "https://weplayapp.com/protocol-hyfwxy";
    /**
     * 分享游戏等相关信息的网址
     */
    @SerializedName("my_share_url")
    public String myShareUrl = "https://api-fra.weplayapp.com/";

    @SerializedName("big_winner_help_url")
    public String voiceRoomBigWinnerHelpUrl = "";

    /**
     * 注销链接
     */
    @SerializedName("logout_url")
    public String deleteAccountUrl = "";

    /**
     * 广播列表头处的文案
     */
    @SerializedName("broadcast_title_text")
    public String broadcastTitleText = "";
    /**
     * 送礼物返还金币的下限
     */
    @SerializedName("send_gift_return_trigger_coin")
    public int sendGiftReturnTriggerCoin = 88;
    /**
     * 第三方客服 sdk 开关
     */
    @SerializedName("third_feedback_sdk_open")
    public boolean thirdFeedbackSdkOpen = false;
    /**
     * 我分享到社交平台的卡片的标题
     */
    @SerializedName("my_share_title")
    public String myShareTitle = "";
    /**
     * 分享的内容文案
     */
    @SerializedName("my_share_desc")
    public String myShareDesc = "";
    /**
     * 房间分享文案
     */
    @SerializedName("room_share_desc")
    public String roomShareDesc = "";
    /**
     * 商品列表，充值商品
     */
    @SerializedName("goods_list")
    public ArrayList<WespyGoods> goodsList = new ArrayList<>();
    /**
     * 画猜绘制时同步频率，单位毫秒
     */
    @SerializedName("sync_draw_frequency")
    public int syncDrawFrequency = 3000;
    /**
     * 广播预定提示文案
     */
    @SerializedName("broadcast_appoint_tip")
    public String broadcastAppointTip = ResUtil.getStr(R.string.broadcast_send_tip);
    /**
     * 婚礼预定刷新文案
     */
    @SerializedName("marry_refresh_tip")
    public String marryRefreshTip = ResUtil.getString(R.string.marry_refresh_tips);
    /**
     * 编辑用户资料时 自己的身价选择范围
     */
    @SerializedName("add_friend_price_list")
    public ArrayList<Integer> addFriendPriceList = new ArrayList<>();
    /**
     * 小游戏审核开关
     */
    @SerializedName("android_checking")
    public int marketChecking = 0;
    /**
     * 绑定手机号金币奖励
     */
    @SerializedName("bind_phone_coin")
    public int bindPhoneRewardCoin = 50;
    /**
     * 画猜绘制时间延时30秒所要消耗的金币
     */
    @SerializedName("draw_delay_cost")
    public int drawGuessDelayCost = 30;

    @SerializedName("draw_report_reason")
    public List<ReportReason> drawReportReason = new ArrayList<>();

    /**
     * 情感语音（脱单神器） 配置信息
     */
    @SerializedName("voice_match_config")
    public VoiceMatchConfig voiceMatchConfig = new VoiceMatchConfig();
    /**
     * 魅力值帮助相关
     */
    @SerializedName("charm_help")
    public CharmHelp charmHelp = new CharmHelp();
    /**
     * 设置中的游戏规则进入的页面
     */
    @SerializedName("game_rule")
    public String gameRule = "https://static-h5.weplayapp.com/app-setting-rule";
    /**
     * 预定广播处添加好声音的帮助进入的页面
     */
    @SerializedName("vow_broadcast_help_url")
    public String vowBroadcastHelpUrl = "https://static.afunapp.com/h5/wd_music_share/index.html";
    /**
     * 选择城市时 热门省份
     */
    @SerializedName("hot_province_list")
    public ArrayList<String> hotProvinceList = new ArrayList<>();
    /**
     * 用于限定对某人的守护值旁边的小树苗对应的资源图片
     */
    @SerializedName("care_section")
    public int[] careResSection = {300, 1000, 3000};
    /**
     * 创建狼人杀高级房消耗列表
     */
    @SerializedName("werewolf_start_lease_list")
    public ArrayList<RoomLease> werewolfVipRoomCreateLeaseList = new ArrayList<>();
    /**
     * 送的礼花根据数量确定的持续时间
     */
    @SerializedName("fireworks_keep_time")
    public HashMap<String, Integer> fireworksKeepTime = new HashMap<>();
    /**
     * 魅力值等级
     */
    @SerializedName("charm_level")
    public ArrayList<Integer> charmLevel = new ArrayList<>();

    /**
     * 魅力值等级
     */
    @SerializedName("user_game_level_icon")
    public ArrayList<Integer> familyJoinGameLevel = new ArrayList<>();
    /**
     * 非狼人杀高级房间捐赠价格
     */
    @SerializedName("renew_lease_list")
    public ArrayList<RoomLease> renewLeaseList = new ArrayList<>();
    /**
     * 守护说明页面
     */
    @SerializedName("care_manual_h5")
    public String careHelpUrl = "https://static-h5.weplayapp.com/guard-explain";
    /**
     * 分享赚金币文案
     */
    @SerializedName("task_share_desc")
    public String taskShareDesc = "";
    /**
     * 加入游戏的最少金币限制
     */
    @SerializedName("min_coin_to_game")
    public int minCoinToEnterGame = 0;
    /**
     * 婚礼相关场景背景音乐
     */
    @SerializedName("bg_music_list")
    public ArrayList<BgMusicInfo> marryRoomMusicBgList = new ArrayList<>();
    /**
     * 分享出去时卡片的图标
     */
    @SerializedName("share_icon_url")
    public String shareIconUrl = "https://pic.weplayapp.com/server/wodi_icon.png";

    @SerializedName("topic_tab_in_the_front")
    public boolean topicFirst;

    /**
     * 分享网址
     */
    @SerializedName("share_url")
    @SuppressWarnings("unused")
    public String shareUrl = "https://weplay.sg/";
    /**
     * 邀请注册应用的分享文案 该属性未用到
     */
    @SerializedName("share_desc")
    @SuppressWarnings("unused")
    public String shareDesc = "";
    /**
     * 分享应用文案，暂时无用
     */
    @SerializedName("share_title")
    @SuppressWarnings("unused")
    public String shareTitle = "";

    /**
     * 语音房最大id
     */
    @SerializedName("max_room_id")
    public int max_room_id;

    @SerializedName("app_slogan")
    public String app_slogan;

    @SerializedName("app_share_url")
    public String app_share_url;

    @SerializedName("user_home_care_people_num")
    public int userHomeCarePeopleNum = 3;

    @SerializedName("fix_room_id")
    public int[] fixRoomId = new int[2];

    @SerializedName("room_level_url")
    public String roomLevelUrl = ""; //https://www.baidu.com?rid=,房间等级说明H5页URL，需要自行拼接rid
    @SerializedName("room_rule_url")
    public String roomRuleUrl = "";//"https://www.baidu.com?game_tye=" 房间游戏规则说明页URL，需要自行拼接game_type


    @SerializedName("propose_require_care")
    public int proposeRequireCare = 0;

    @SerializedName("wespy_game_rule_url")
    public String wespyGameRuleUrl = "";

    @SerializedName("blk_game_rule_url")
    public String blkGameRuleUrl = "";

    @SerializedName("show_invite_code")
    public int showInviteCode = 0;

    @SerializedName("marry_config")
    public MarryConfig marryConfig = new MarryConfig();

    @SerializedName("draw_config")
    public DrawConfig drawConfig = new DrawConfig();

    @SerializedName("nest_help_url")
    public String nestHelpUrl = "";

    @SerializedName("nest_help_h5")
    public String nestHelpH5 = "";

    @SerializedName("church_help_url")
    public String churchHelpUrl = "";

    @SerializedName("church_help_h5")
    public String churchHelpH5 = "";

    @SerializedName("abtest")
    public AbTest abTest = new AbTest();

    @SerializedName("supervise_url")
    public SuperviseInfo superviseUrlInfo = new SuperviseInfo();

    @SerializedName("block_device_slices")
    public int blockDevice = 0;

    @SerializedName("game_room_help_url")
    public String gameRoomHelpUrl = "";//桌游房间帮助配置化


    @SerializedName("game_room_help_h5")
    public String gameRoomHelpH5 = "";//桌游房间帮助配置化

    @SerializedName("default_topic_id")
    public int defaultTopicId;//chatting分支用到这个字段

    @SerializedName("family")
    public FamilyConfig familyConfig = new FamilyConfig();

    @SerializedName("playcard_opened")
    public int battlePassOpen = 1;

    @SerializedName("werewolf_level")
    public int werewolfLevel = 7;

    @SerializedName("music_hum_share_url")
    public String musicHumShareUrl = "";

    @SerializedName("qa_share_url")
    public String qaShareUrl = "";

    @SerializedName("qa_extend_list")
    public List<Integer> qaExtendList = Collections.emptyList();

    @SerializedName("share_mode_list")
    public ArrayList<ShareModeInfo> shareModeList = new ArrayList<>();

    @SerializedName("app_list")
    public List<Object> appList = new ArrayList<>();
//    public List<AppInfo> appList = new ArrayList<>();

    @SerializedName("xroom_game_help_image_new")
    public String xroomGameHelpImageNew = "";

    @SerializedName("age_limit")
    public AgeLimit ageLimit = new AgeLimit();

    @SerializedName("ping_ip_list")
    public List<String> pingIpList = new ArrayList<>();

    @SerializedName("use_kcp")
    public boolean useKcp = false; //是否使用kcp，false则用tcp

    @SerializedName("nation_flag_reach_top")
    public String nationFlagReachTopUrl = "https://res.weplayapp.com/conf/1098C630-AF47-47E5-924C-FD4D4EA1ABC5.mp4";

    @SerializedName("nation_flag_earth")
    public String nationFlagEarthUrl = "https://res.weplayapp.com/conf/03DD46B1-D439-496B-BE1E-C69A17C6C18D.mp4";

    @SerializedName("nation_flag_rank")
    public String nationFlagRank = "";

    @SerializedName("nation_flag_reward")
    public String nationFlagReward = "";

    @SerializedName("nation_flag_rule")
    public String nationFlagRule = "";

    /**
     * baloot 预制出牌功能开关
     */
    @SerializedName("baloot_enable_preset_put_card")
    public boolean balootPresetCardSwitch = false;

    @SerializedName("light_board_manage_url")
    public String familyLightManageUrl = "";

    @SerializedName("qa_game_rule_url")
    public String gtaGameRuleUrl = "";

    @SerializedName("qa_game_energy_url")
    public String gtaGameEnergyUrl = "";

    @SerializedName("share_topics")
    public List<String> shareTopics = new ArrayList<>();

    /**
     * 靓号配置
     */
    @SerializedName("lucky_number_icons")
    public List<LuckyNumLevel> luckyNumLevelList = Collections.emptyList();

    public LuckyNumLevel getLuckyNumInfoByLevel(int level) {
        LuckyNumLevel luckyNumLevel = new LuckyNumLevel();
        if (luckyNumLevelList.isEmpty()) {
            return luckyNumLevel;
        }
        for (LuckyNumLevel luckyNum : luckyNumLevelList) {
            if (luckyNum.level == level) {
                luckyNumLevel = luckyNum;
                break;
            }
        }
        return luckyNumLevel;
    }

    public ConstV3Info() {
    }

    public static class ReportReason {
        @SerializedName("id")
        private int id;
        @SerializedName("reason")
        private String reason;

        public int getReportReasonId() {
            return id;
        }

        public String getReason() {
            return reason;
        }
    }

    public static class RoomTypeMaxNum {

        @SerializedName("room_type")
        public int roomType = 1;

        @SerializedName("max_num")
        public int maxPersonNum = 40;
    }

    public static class CharmHelp {
        /**
         * 比例， android 无用
         */
        @SuppressWarnings("unused")
        @SerializedName("ratio")
        public float ratio = 0.269F;

        /**
         * 帮助页面网址
         */
        @SerializedName("img")
        public String imgUrl = "https://static-h5.weplayapp.com/charm-explain";
    }

    /**
     * 七牛域名配置处理
     */
    public static class Bucket {
        @SerializedName("permanent")
        public Info permanent = new Info();

        @SerializedName("temp")
        public Info temp = new Info();

        public static class Info {
            @SerializedName("url")
            public String url = "https://wespypic2015.afunapp.com";

            @SerializedName("name")
            public String name = "wespy-temp-2015";
        }
    }

    public static class SuperviseInfo {
        @SerializedName("voice_room")
        public String voiceRoomSupervise = "https://static-h5.weplayapp.com/h5-module-room-report";
        @SerializedName("tuodan_radio")
        public String evMatchSupervise = "https://wespyadmin.17zjh.com/h5/tuodan_radio_supervise";
    }

    //这里没有version，取几个重要的数值标示有数据
    public boolean isAvailable() {
        return stuffInfoList.size() != 0 && goodsList.size() != 0;
    }
//    public static class AppInfo {
//        public String icon;
//        public String name;
//    }

    @SerializedName("first_charge_banner_landscape")
    public String firstChargeBannerLandscape = "";

    @SerializedName("ping_info")
    public PingInfoConfig pingInfo;

    @SerializedName("net_util_info")
    public NetUtilInfoConfig netUtilInfo;

    @SerializedName("first_charge_banner_tmproom")
    public String firstChargeBannerTmpRoom = "";

    @SerializedName("music_hum_contrib_card_max")
    public int contribCardMax = 0;

    @SerializedName("hide_contrib_card_max_limit")
    public boolean hideContribCardMaxLimit = false;

    @SerializedName("music_hum_contrib_sing_max")
    public int contribSingMax = 0;

    @SerializedName("sign_singer_protocol")
    public String signSingerProtocol = "";

    @SerializedName("music_hum_share_square_topic_id")
    public int shareSquareTopicId = 0;

    @SerializedName("disable_kingmate")
    public boolean disableKingMate = true;

    @SerializedName("disable_voice_match")
    public boolean disableAudioMatch = true;

    @SerializedName("family_offline_sys_msg_cnt")
    public int familyOfflineSysMsgCnt = 200;

    @SerializedName("chat_sync")
    public ChatMsgRoaming chatMsgRoaming = new ChatMsgRoaming();

    @SerializedName("sayguess_help")
    public String sayGuessHelp = "";

    @SerializedName("title_help_url")
    public String titleHelpUrl = "";

    @SerializedName("xroom_game_help_image")
    public String xRoomGameHelpImage = "";

    public static class ChatMsgRoaming {
        @SerializedName("retrieve_password_tip")
        public String resetPwdTip = "";
        @SerializedName("help_url")
        public String helpUrl = "https://static-h5.weplayapp.com/chat-record-roaming";
    }

    @SerializedName("user_charm_level_icon")
    public List<String> userCharmLevelIcons = new ArrayList<>();

    @SerializedName("charm_level_icon")
    public List<String> charmLevelIcons = new ArrayList<>();

    @SerializedName("xroom_game_help_video")
    public String xRoomGameHelpVideo = "";

    @SerializedName("xroom_invite_new")
    public String xRoomInviteNew = "";

    @SerializedName("npc_uids")
    public List<Integer> npcUidList = new ArrayList<>();

    @SerializedName("avatar_show_share_square_topic_id")
    public int avatarShareSquareTopicId = 0;

    @SerializedName("force_quit_tips")
    public QuitTipsInfo forceQuitTips = new QuitTipsInfo();

    public static class QuitTipsInfo {
        @SerializedName("default_tip")
        public String defaultTip = "";

        @SerializedName("tips")
        public List<TipsInfo> tipList = new ArrayList<>();
    }

    public static class TipsInfo {
        @SerializedName("tips")
        public String tips = "";

        @SerializedName("game_types")
        public List<Integer> gameTypes = new ArrayList<>();
    }

    public String getQuitTips(int gameType) {
        for (TipsInfo tipsInfo : forceQuitTips.tipList) {
            if (tipsInfo.gameTypes.contains(gameType)) {
                return tipsInfo.tips;
            }
        }
        return forceQuitTips.defaultTip;
    }

    @SerializedName("xroom_avatar_type")
    public int xRoomAvatarType = 1;

    @SerializedName("offline_spy_help")
    public String offlineSpyHelp = "";

    /**
     * 排行榜说明页面
     */
    @SerializedName("rank_helper_url")
    public String rankHelpUrl = "https://static-h5.weplayapp.com/rank-rule";
    /**
     * 收藏册说明页
     */
    @SerializedName("collection_help_url")
    public String collectionHelpUrl = "";
    @SerializedName("infinite_drawing_url")
    public String infiniteDrawUrl = "https://static-h5.weplayapp.com/infinite-drawing";

    @SerializedName("auction_introduction_h5")
    public String auctionIntroductionUrl = "https://static-h5.weplayapp.com/auction-room-explain";

    @SerializedName("xroom_game_help_url")
    public String xRoomGameHelpUrl = "";

    @SerializedName("game_praise")
    public GamePraiseInfo gamePraise;

    @SerializedName("grade_center")
    public GradeCenter gradeCenter = new GradeCenter();

    public static class GradeCenter {
        @SerializedName("url")
        public String url = "";
    }

    @SerializedName("medal_rank_help_url")
    public String medalRankHelpUrl = "";

    public static class GamePraiseInfo {
        @SerializedName("title")
        public String title = "";

        @SerializedName("text")
        public String text = "";

        @SerializedName("btn_word")
        public String btnWord = "";
    }

    @SerializedName("my_share_with_source")
    public MyShareWithSource myShareWithSource;

    public static class MyShareWithSource {
        @SerializedName("avatar_square")
        public AvatarSquare avatarSquare;

        @SerializedName("share_group_new")
        public ShareGroup shareGroup = new ShareGroup();

        @SerializedName("share_competition")
        public ShareCompetition shareCompetition = new ShareCompetition();
    }

    public static class AvatarSquare {
        @SerializedName("my_share_desc")
        public String myShareDesc = "";

        @SerializedName("my_share_title")
        public String myShareTitle = "";

        @SerializedName("my_share_url")
        public String myShareUrl = "";
    }

    public static class ShareGroup {
        @SerializedName("my_share_desc")
        public String myShareDesc = "";

        @SerializedName("my_share_title")
        public String myShareTitle = "";

        @SerializedName("my_share_url")
        public String myShareUrl = "";
    }

    @SerializedName("pop_up_praise_window_condition")
    public PopOKWindow popOKWindow = new PopOKWindow();

    /**
     * 应用商店【google, huawei】 好评弹窗
     */
    public static class PopOKWindow {
        /**
         * 玩游戏胜利一定次数后，弹出弹窗。
         * 弹窗中按钮可以跳转到对应应用市场
         */
        @SerializedName("win_game_condition")
        public List<Integer> winCondition = new ArrayList<>();


        /**
         * 冷启动一定次数后弹起谷歌对应评价弹窗
         */
        @SerializedName("open_app_condition")
        public List<Integer> openAppWindow = new ArrayList<>();

    }

    public static class LuckyNumLevel {
        @SerializedName("level")
        public int level = 1;

        @SerializedName("icon")
        public String url = "";
    }

    @SerializedName("activity_tab_configs")
    public Map<String, EventsTabName> activityTabConfigs = new HashMap<>();

    public static class EventsTabName {
        @SerializedName("tab_name")
        public String tabName = "";
    }

    @SerializedName("medal_help_url")
    public String medalHelpUrl = "";

    @SerializedName("desc_mode")
    public SpyMode spyDescMode = new SpyMode();

    @SerializedName("ask_mode")
    public SpyMode spyAskMode = new SpyMode();

    @SerializedName("ab_mode")
    public SpyMode spyAbMode = new SpyMode();

    @SerializedName("spy_mode_list")
    public List<Integer> spyModeList = Collections.emptyList();

    @SerializedName("spy_contribute_game_type")
    public int spyContributeGameType = 0;

    public static class SpyMode {
        @SerializedName("img_url")
        public String imgUrl = "";
        @SerializedName("new_label")
        public String newLabel = "";
        @SerializedName("change_mode_name")
        public String modeName = "";
    }

    @SerializedName("white_hosts")
    public List<String> whiteHosts = Collections.singletonList("weplayapp.com");

    @SerializedName("callback_urls")
    public CallbackUrls callbackUrls = new CallbackUrls();

    public static class CallbackUrls {
        @SerializedName("full_screen")
        public String fullScreen = "";

        @SerializedName("half_screen")
        public String halfScreen = "";
    }

    @SerializedName("music_hum_upload_switch")
    public MusicHumMachineLeaningSwitch musicHumUploadSwitch = new MusicHumMachineLeaningSwitch();

    public static class MusicHumMachineLeaningSwitch {
        @SerializedName("is_open")
        public boolean open = false;

        @SerializedName("only_wifi")
        public boolean onlyWifi = true;

        @SerializedName("daily_upload_limit")
        public int dailyUploadLimit = 0;

        @SerializedName("audio_format")
        public String fmt = "pcm";

        @SerializedName("sample_rate")
        public int sampleRate = 16000;
    }

    /**
     * 客户端默认值8s
     */
    @SerializedName("music_hum_feedback_delay_seconds")
    public int musicHumFeedbackDelaySeconds = 8;
    /**
     * 支持挂起语音房 玩的unity游戏
     */
    @SerializedName("suspend_room_unity_games")
    private List<SuspendRoomUnityGame> suspendRoomUnityGames = new ArrayList<>();

    @SerializedName("change_phone_url")
    private String changePhoneUrl = "";

    public String getChangePhoneUrl() {
        return changePhoneUrl;
    }

    public List<SuspendRoomUnityGame> getSuspendRoomUnityGames() {
        return suspendRoomUnityGames;
    }

    public static class SuspendRoomUnityGame {
        @SerializedName("game_type")
        private int gameType = 0;
        @SerializedName("scene_type")
        private int sceneType = -1;

        /**
         * 支持跳转语音房时，从游戏跳转语音房前的二次弹窗文案
         */
        @SerializedName("exit_text")
        public String exitText = "";

        public int getGameType() {
            return gameType;
        }

        public int getSceneType() {
            return sceneType;
        }
    }

    //磁盘空间不足判断阈值，单位：MB
    @SerializedName("low_storage_warning")
    public long lowStorageWarning = 0;

    @SerializedName("res_download_timeout")
    public int resDownloadTimeout = 5;

    @SerializedName("rank_limit")
    private RankLimit rankLimit = new RankLimit();

    public RankLimit getRankLimit() {
        return rankLimit;
    }

    public static class RankLimit {
        @SerializedName("register_day")
        private int registerDay = 3;

        @SerializedName("total_charge")
        private int totalCharge = 20;

        public int getRegisterDay() {
            return registerDay;
        }

        public int getTotalCharge() {
            return totalCharge;
        }
    }

    @SerializedName("advance_pk_help_url")
    private String advancePkHelpUrl = "";

    public String getAdvancePkHelpUrl() {
        return advancePkHelpUrl;
    }

    @SerializedName("friend_invite_url")
    private String friendInviteUrl = "";

    public String getFriendInviteUrl() {
        return friendInviteUrl;
    }

    @SerializedName("vip_game_list_refresh_millisecond")
    private int vipRoomListRefreshTime = 5000;

    public int getVipRoomListRefreshTime() {
        return vipRoomListRefreshTime;
    }

    @SerializedName("grade_level_icon")
    private List<GradeLevel> gradeLevelList = Collections.emptyList();

    @SerializedName("qualifying_grade")
    public List<QualifyingGrade> qualifyingGradeList = Collections.emptyList();

    @SerializedName("qualifying_urls")
    private QualifyingJumpLink qualifyingJumpLink = new QualifyingJumpLink();

    public GradeLevel findGradeLevel(int level) {
        GradeLevel gradeLevel = findGradeLevel(level, null);
        if (gradeLevel == null) {
            FLog.e(new IllegalArgumentException("error level " + level));
            gradeLevel = new GradeLevel();
        }
        return gradeLevel;
    }

    @Nullable
    public GradeLevel findGradeLevel(int level, GradeLevel fallback) {
        GradeLevel last = null;
        for (int i = 0; i < gradeLevelList.size(); i++) {
            GradeLevel gradeLevel = gradeLevelList.get(i);
            if (level >= gradeLevel.level) {
                last = gradeLevel;
            } else {
                break;
            }
        }
        if (last == null) {
            return fallback;
        }
        return last;
    }

    public QualifyingGrade findQFGrade(int grade) {
        QualifyingGrade previousGrade = null;
        for (QualifyingGrade qfGrade : qualifyingGradeList) {
            if (grade >= qfGrade.grade) {
                previousGrade = qfGrade;
            } else {
                break;
            }
        }
        if (previousGrade == null) {
            previousGrade = new QualifyingGrade();
        }
        return previousGrade;
    }

    public String getGradeLink() {
        if (qualifyingJumpLink == null) return "";
        return qualifyingJumpLink.confirmGradeLink;
    }

    public String getGradeDetailLink() {
        if (qualifyingJumpLink == null) return "";
        return qualifyingJumpLink.gradeDetailLink;
    }

    public String getQFRewardLink() {
        if (qualifyingJumpLink == null) return "";
        return qualifyingJumpLink.rewardDetailLink;
    }

    public static class GradeLevel {
        @SerializedName("level")
        public int level = 0;
        @SerializedName("icon")
        public String icon = "";
        @SerializedName("icon_background")
        public String iconBg = "";

    }

    public static class QualifyingGrade {
        @SerializedName("grade")
        public int grade = 0; //段位

        @SerializedName("name")
        public String name = ""; //段位名称

        @SerializedName("icon")
        public String icon = ""; //段位icon
    }

    public static class QualifyingJumpLink {
        @SerializedName("confirm_grade")
        public String confirmGradeLink = ""; //定级赛
        @SerializedName("grade_detail")
        public String gradeDetailLink = ""; //段位详情
        @SerializedName("reward_detail")
        public String rewardDetailLink = ""; //奖励详情
    }

    @SerializedName("default_bgm_volume")
    public int defaultBgmVolume = 50;

    @SerializedName("default_effect_volume")
    public int defaultEffectVolume = 100;

    /**
     * 赛事系统帮助页
     */
    @SerializedName("competition_help_url")
    private String competitionHelpUrl = "";

    public String getCompetitionHelpUrl() {
        return competitionHelpUrl;
    }

    public static class ShareCompetition {
        @SerializedName("my_share_url")
        public String myShareUrl = "https://static-h5.jackarooapp.com/ssg/match-game-share/index.html";

        @SerializedName("my_share_image")
        public String shareIconUrl = "https://res.weplayapp.com/conf/1748335693ebaa88f6-d1ec-401d-ae2b-caac224baf37.webp";
    }
}
