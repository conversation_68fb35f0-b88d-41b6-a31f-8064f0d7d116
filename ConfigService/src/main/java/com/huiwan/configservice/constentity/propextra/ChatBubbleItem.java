package com.huiwan.configservice.constentity.propextra;

import android.graphics.Color;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.store.file.FileConfig;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ColorUtil;
import com.huiwan.base.util.URIUtil;
import com.huiwan.configservice.R;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2019-12-11
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class ChatBubbleItem {
    @SerializedName("text_color")
    public String textColor = "#000000";
    @SerializedName("deep_link_color")
    public String deepLinkColor = "#24c572";
    @SerializedName("bg_img_url")
    public String bgImgUrl = "";
    @SerializedName("anim_lt")
    public String animLt = "";
    @SerializedName("anim_rt")
    public String animRt = "";
    @SerializedName("anim_lb")
    public String animLb = "";
    @SerializedName("anim_rb")
    public String animRb = "";

    @SerializedName("frame_anim_lt")
    public String frameAnimLt = "";
    @SerializedName("frame_anim_rt")
    public String frameAnimRt = "";
    @SerializedName("frame_anim_lb")
    public String frameAnimLb = "";
    @SerializedName("frame_anim_rb")
    public String frameAnimRb = "";
    @SerializedName("prop_tips")
    public List<String> tipTexts = new ArrayList<>();

    @SerializedName("gift_div_color")
    private String giftDividerColor = "#999999";
    @SerializedName("gift_tip_color")
    private String giftTipColor = "#999999";
    @SerializedName("gift_desc_color")
    private String giftDescColor = "#ee0000";

    private transient boolean colorNeedInit = true;
    private transient int initialiseTextColor = Color.BLACK;
    private transient int initialiseDeepLinkColor = ResUtil.getColor(R.color.color_accent_ex);
    private transient int initialiseGiftDivColor = 0xff999999;
    private transient int initialiseGiftTipColor = 0xff999999;
    private transient int initialiseGiftDescColor = 0xffee0000;

    private void initColorIfNeed() {
        if (colorNeedInit) {
            initialiseTextColor = ColorUtil.getColor(textColor);
            initialiseGiftTipColor = ColorUtil.getColor(giftTipColor);
            initialiseGiftDescColor = ColorUtil.getColor(giftDescColor);
            initialiseGiftDivColor = ColorUtil.getColor(giftDividerColor);
            initialiseDeepLinkColor = ColorUtil.getColor(deepLinkColor);
            colorNeedInit = false;
        }
    }

    public int getGiftDivColor() {
        initColorIfNeed();
        return initialiseGiftDivColor;
    }

    public int getGiftTipColor() {
        initColorIfNeed();
        return initialiseGiftTipColor;
    }

    public int getGiftDescColor() {
        initColorIfNeed();
        return initialiseGiftDescColor;
    }

    public int getTextColor() {
        initColorIfNeed();
        return initialiseTextColor;
    }

    public int getDeepLinkColor() {
        initColorIfNeed();
        return initialiseDeepLinkColor;
    }

    public static String getLocalPath(String url) {
        String pref = FileConfig.getPropDirPath(FileCacheName.CHAT_BUBBLE) + "c_bubble_";
        return TextUtils.isEmpty(url) ? "" : URIUtil.url2LocalPath(url, pref);
    }
}
