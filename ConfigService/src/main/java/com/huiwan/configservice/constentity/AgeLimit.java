package com.huiwan.configservice.constentity;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class AgeLimit {

    public static final int DEFAULT_YEAR = 0;

    /**
     * 是否显示生日填写界面 true 表示展现生日填写界面
     * 1. 新用户注册时，展现生日填写界面
     * 2. 用户更改年龄小于最小年龄限制（limit_age），重启 app 后，展现生日填写界面
     */
    @SerializedName("show_birthday_input")
    public boolean showBirthdayInput;

    /**
     * 最小年龄限制，当用户更改年龄后，年龄小于该最小年龄限制，则重启 app 需要展示生日界面
     */
    @SerializedName("limit_age")
    public int limitAge;

    /**
     * 在生日填写时，如果用户选择的年龄小于 最小年龄限制（limit_age）是走策略 1 还是 策略2
     * true  表示策略1：端上进行拦截，即：展现红色提示文案，提交按钮变灰
     * false 表示策略2：端上不进行拦截，即：不展示文案，可以进行提交，由服务端处理
     */
    @SerializedName("age_low_alert")
    public boolean ageLowAlert;

    /**
     * 默认选择的年龄
     * 当展现生日界面（showBirthdayInput 为 true） 时，默认选择的年份
     */
    @SerializedName("default_select_year")
    public int defaultSelectYear = DEFAULT_YEAR;

    /**
     * 生日非法提示
     * 当展现生日界面（showBirthdayInput 为 true）时，用户年龄小于最小年龄限制（limit_age）时，展现的文案
     */
    @SerializedName("illegal_birth_tips")
    public String illegalBirthTips = "";


    @SerializedName("hide_birth_input_register_type")
    public List<String> hideBirthRegisterTypeList = new ArrayList<>();

    @NonNull
    @Override
    public String toString() {
        return "AgeLimit{" +
                "showBirthdayInput=" + showBirthdayInput +
                ", limitAge=" + limitAge +
                ", ageLowAlert=" + ageLowAlert +
                ", defaultSelectYear=" + defaultSelectYear +
                ", illegalBirthTips='" + illegalBirthTips + '\'' +
                '}';
    }
}
