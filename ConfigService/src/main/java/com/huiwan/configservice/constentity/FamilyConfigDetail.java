package com.huiwan.configservice.constentity;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-10-23.
 */
public class FamilyConfigDetail {

    @SerializedName("create_family_coin")
    public int create_family_coin;
    @SerializedName("family_help_url")
    public String family_help_url = "";
    @SerializedName("family_rank_help_url")
    public String family_rank_help_url = "weplayapp.com";
    @SerializedName("mod_name_coin")
    public int mod_name_coin;
    @SerializedName("mod_avatar_coin")
    public int mod_avatar_coin;
    @SerializedName("create_family_min_flower")
    public int create_family_min_flower = 0;
    @SerializedName("voice_room_maintain_cost")
    public int voice_room_maintain_cost = 0;
    @SerializedName("vr_maintain_cost_by_level")
    public List<VoiceRoomMaintainCostItem> voiceRoomMaintainCostItemList = new ArrayList<>();
    @SerializedName("wheel_silver_lottery")
    public List<LotteryReward> wheel_silver_lottery = new ArrayList<>();
    @SerializedName("wheel_gold_lottery")
    public List<LotteryReward> wheel_gold_lottery = new ArrayList<>();
    @SerializedName("wheel_diamond_lottery")
    public List<LotteryReward> wheel_diamond_lottery = new ArrayList<>();
    @SerializedName("steal_help_url")
    public String steal_help_url = "";

    @SerializedName("wheel_card_coin")
    public int wheel_card_coin = 200;
    @SerializedName("donate_coin_prop")
    public List<Integer> donate_coin_prop = new ArrayList<>();
    @SerializedName("donate_active_prop")
    public List<Integer> donate_active_prop = new ArrayList<>();
    @SerializedName("level_up_tips")
    public List<LevelUpTips> level_up_tips = new ArrayList<>();
    @SerializedName("max_rank")
    public int max_rank = 999;
    @SerializedName("flower_not_enough_tip")
    public String flower_not_enough_tip = "";
    @SerializedName("family_treasure_box")
    public List<TreasureConfigInfo> treasureList = new ArrayList<>();

    @SerializedName("honor_title")
    public List<TitleConfigInfo> titleList = new ArrayList<>();

    @SerializedName("honor_title_help_url")
    public String honorTitleHelpUrl = "";

    public TitleConfigInfo getFamilyHonorTitleInfo(int hid) {
        for (TitleConfigInfo titleConfigInfo: titleList) {
            if (titleConfigInfo.hid == hid) {
                return titleConfigInfo;
            }
        }
        return null;
    }

    public static class LotteryReward {
        @SerializedName("prop_id")
        private int propId;
        @SerializedName("name")
        private String name = "";

        public int getPropId() {
            return propId;
        }

        public String getName() {
            return name;
        }

        @Override
        public int hashCode() {
            return propId;
        }

        @Override
        public boolean equals(@Nullable Object obj) {
            return obj != null && obj.getClass() == getClass() && ((LotteryReward)obj).propId == propId;
        }
    }

    public static class VoiceRoomMaintainCostItem {
        public VoiceRoomMaintainCostItem() {
        }

        @SerializedName("min_level")
        private int minLevel = 0;
        @SerializedName("max_level")
        private int maxLevel = 0;
        @SerializedName("cost")
        private int cost = 0;

        public int getCost() {
            return cost;
        }

        public int getMaxLevel() {
            return maxLevel;
        }

        public int getMinLevel() {
            return minLevel;
        }
    }

    public int getFamilyVoiceRoomMaintainCostByLevel(int curLevel) {
        int fund = 0;
        for (int i = 0; i < voiceRoomMaintainCostItemList.size(); ++i) {
            VoiceRoomMaintainCostItem item = voiceRoomMaintainCostItemList.get(i);
            if (item.minLevel <= curLevel && item.maxLevel >= curLevel) {
                fund = item.cost;
                break;
            }
        }
        return fund;
    }

    public static class LevelUpTips {
        @SerializedName("level")
        public int level;
        @SerializedName("text")
        public String text = "";
    }

    public static class TreasureConfigInfo {
        /**
         * 等级
         */
        @SerializedName("level")
        public int level = 0;

        /**
         * 宝箱ID
         */
        @SerializedName("prop_id")
        public int propId = 0;

        /**
         * 宝箱名称
         */
        @SerializedName("box_name")
        public String boxName = "";

        /**
         * 标题渐变色
         */
        @SerializedName("text_color")
        public List<String> textColor = new ArrayList<>();

        /**
         * 抢宝箱详情页-标题背景色值
         */
        @SerializedName("title_bg_color")
        public String titleBgColor = "";

        /**
         * 宝箱未打开
         */
        @SerializedName("box_not_open")
        public String boxNotOpen = "";

        /**
         * 宝箱已打开
         */
        @SerializedName("box_opened")
        public String boxOpened = "";

        /**
         * 抢宝箱背景
         */
        @SerializedName("grab_box_bg")
        public String grabBoxBg = "";

        /**
         * 宝箱气泡
         */
        @SerializedName("box_bubble")
        public String boxBubble = "";

        /**
         * 宝箱气泡
         */
        @SerializedName("box_bubble_from_other")
        public String boxBubbleFromOther = "";

        /**
         * 开宝箱按钮
         */
        @SerializedName("open_box_btn")
        public String openBoxBtn = "";

        /**
         * 宝箱弹窗
         */
        @SerializedName("box_window")
        public String boxWindow = "";

        /**
         * 开宝箱前景
         */
        @SerializedName("grab_img")
        public String grabImg = "";

        /**
         * 纯色背景图片地址
         */
        @SerializedName("bg_img")
        public String bgImg = "";

        /**
         * 宝箱气泡icon
         */
        @SerializedName("box_icon")
        public String boxIcon = "";

        public int getLevel() {
            return level;
        }

        public int getPropId() {
            return propId;
        }

        public String getBoxName() {
            return boxName;
        }

        public List<String> getTextColor() {
            return textColor;
        }

        public String getTitleBgColor() {
            return titleBgColor;
        }

        public String getBoxNotOpen() {
            return boxNotOpen;
        }

        public String getBoxOpened() {
            return boxOpened;
        }

        public String getGrabBoxBg() {
            return grabBoxBg;
        }

        public String getBoxBubble() {
            return boxBubble;
        }

        public String getBoxBubbleFromOther() {
            return boxBubbleFromOther;
        }

        public String getOpenBoxBtn() {
            return openBoxBtn;
        }

        public String getBoxWindow() {
            return boxWindow;
        }

        public String getGrabImg() {
            return grabImg;
        }

        public String getBgImg() {
            return bgImg;
        }

        public String getBoxIcon() {
            return boxIcon;
        }
    }

    public static class TitleConfigInfo {
        /**
         * 头衔id
         */
        @SerializedName("hid")
        public int hid = 0;

        /**
         * 头衔名称
         */
        @SerializedName("name")
        public String name = "";

        /**
         * 头衔介绍
         */
        @SerializedName("desc")
        public String desc = "";

        /**
         * 家族等级可用数量，从1-11级
         */
        @SerializedName("level_limit")
        public List<Integer> levelLimit = new ArrayList<>();

        /**
         * 任命条件
         */
        @SerializedName("condition")
        public TitleCondition mTitleCondition = new TitleCondition();

        public int getHid() {
            return hid;
        }

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }

        public List<Integer> getLevelLimit() {
            return levelLimit;
        }

        public TitleCondition getTitleCondition() {
            return mTitleCondition;
        }
    }

    public static class TitleCondition {
        @SerializedName("type")
        public String type = "";
        @SerializedName("min")
        public int min = 0;
        @SerializedName("max")
        public int max = 0;
    }

    @SerializedName("welfare_config")
    public List<welfareConfigItem> welfareConfig = new ArrayList<>();

    public static class welfareConfigItem {
        @SerializedName("welfare_type")
        public int type = 0;
        @SerializedName("welfare_title")
        public String title = "";
        @SerializedName("welfare_icon")
        public String welfare_icon = "";
        @SerializedName("welfare_desc")
        public String welfareDesc = "";
    }

    public welfareConfigItem getWelfareConfigItem(int type) {
        for (welfareConfigItem item : welfareConfig) {
            if (item.type == type) {
                return item;
            }
        }
        return new welfareConfigItem();
    }

    public String getFamilyWelfareHelpUrl() {
        return family_welfare_help_url;
    }

    @SerializedName("family_welfare_help_url")
    public String family_welfare_help_url = "";
}
