package com.huiwan.configservice.constentity;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.huiwan.base.interfaces.IDesc;

/**
 * Created by three on 15/10/10.
 */
public class BgMusicInfo implements IDesc {

    @Expose(serialize = true, deserialize = true)
    @SerializedName("bg_music_id")
    public int bg_music_id;

    @Expose(serialize = true, deserialize = true)
    @SerializedName("name")
    public String name = "I believe";

    @Expose(serialize = true, deserialize = true)
    @SerializedName("url")
    public String url = "";

    @Expose(serialize = true, deserialize = true)
    @SerializedName("avatar_url")
    public String avatar_url;

    @Override
    public String getDesc() {
        return name;
    }
}
