package com.huiwan.configservice.constentity;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class PingInfoConfig {
    @SerializedName("ping_interval_minute")
    public int interval;

    @SerializedName("ping_addr")
    public List<String> address = new ArrayList<>();

    @SerializedName("http_addr")
    public List<String> httpAddress = new ArrayList<>();

    @SerializedName("transport_addr")
    public List<String> transports = new ArrayList<>();
}
