package com.huiwan.configservice.constentity;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

// Created by bigwen on 2019/1/11.
public class MarryConfig {

    @SerializedName("auction_base_price")
    private int basePrice = 1000;
    @SerializedName("auction_raise_price_base")
    private int raiseBasePrice = 1000;
    @SerializedName("auction_raise_price_low")
    private int raiseLow = 1000;
    @SerializedName("auction_raise_price_medium")
    private int raiseMedium = 10000;
    @SerializedName("auction_raise_price_high")
    private int raiseHigh = 100000;
    @SerializedName("heat_section")
    private List<Integer> heatSection = new ArrayList<>();
    @SerializedName("auction_help_msg")
    private String auctionHelpMsg = "";
    @SerializedName("emcee_rank_help_url")
    private String emceeRankHelpUrl = "";
    @SerializedName("heat_reward_toast")
    private List<String> heatRewardToast = new ArrayList<>();
    @SerializedName("heat_icon")
    private List<String> heatIconList = new ArrayList<>();

    public int getBasePrice() {
        return basePrice;
    }

    public int getRaiseBasePrice() {
        return raiseBasePrice;
    }

    public int getRaiseLow() {
        return raiseLow;
    }

    public int getRaiseMedium() {
        return raiseMedium;
    }

    public int getRaiseHigh() {
        return raiseHigh;
    }

    public List<Integer> getHeatSection() {
        return heatSection;
    }

    public String getAuctionHelpMsg() {
        return auctionHelpMsg;
    }

    public String getEmceeRankHelpUrl() {
        return emceeRankHelpUrl;
    }

    public List<String> getHeatSectionText() {
        return heatRewardToast;
    }

    public List<String> getHeatIcon() {
        return heatIconList;
    }
}
