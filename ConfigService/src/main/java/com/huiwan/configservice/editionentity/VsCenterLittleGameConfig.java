package com.huiwan.configservice.editionentity;

import static java.util.Collections.emptyList;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 阿语服 2.8.5 新增
 * 小游戏首页优化
 * 支持 unity 小游戏
 */
public class VsCenterLittleGameConfig {
    public static final int JUMP_TYPE_MATCH = 1;
    public static final int JUMP_TYPE_CREATE = 2;
    public static final int JUMP_TYPE_GUIDE_DIALOG = 3;
    public static final int JUMP_TYPE_DEEPLINK = 4;
    public static final int JUMP_TYPE_SECOND_DIALOG = 5;
    public static final int JUMP_TYPE_RANK = 6;
    public static final int JUMP_TYPE_QUICK_START = 7;
    public static final int JUMP_TYPE_COCOS_GUIDE = 8;//跳转cocos内部新手教程

    @SerializedName("meta_data")
    public MetaData metaData = new MetaData();

    @SerializedName("person_info")
    public Person person = new Person();

    @SerializedName("entry_layout")
    public EntryLayout entryLayout = new EntryLayout();

    @SerializedName("func_layout")
    public FuncLayout funcLayout = new FuncLayout();

    @SerializedName("recommend")
    public Recommend recommend = new Recommend();

    @SerializedName("vip_room_config")
    public VipRoomConfig vipRoomConfig = new VipRoomConfig();

    @SerializedName("popup_entry_layout")
    private List<PopupEntryLayout> popupEntryLayouts = emptyList();

    public List<PopupEntryLayout> getPopupEntryLayouts() {
        return popupEntryLayouts;
    }

    /**
     * unity 不同模式的 帮助弹窗
     */
    public List<String> getGameModeGuide(int gameMode) {
        List<HelpUrl> helpUrlList = metaData.helpUrlList;
        if (helpUrlList.isEmpty()) {
            return new ArrayList<>();
        }
        for (HelpUrl helpUrl : helpUrlList) {
            if (helpUrl.gameMode == gameMode) {
                return helpUrl.urls;
            }
        }
        return new ArrayList<>();
    }

    public static class MetaData {
        static final int ENGINE_COCOS = 1;
        static final int ENGINE_UNITY = 2;
        @SerializedName("background_url")
        public String bgUrl = "";
        @SerializedName("engine")
        public int engine = 1;

        /**
         * cocos 的从原来的地方拿
         */
        @SerializedName("asset_url")
        public String unityRes = "";

        public transient List<String> unityResList = Collections.emptyList();

        /**
         * 空座位图
         */
        @SerializedName("empty_seat_url")
        public String emptySeatUrl = "";

        @SerializedName("button_urls")
        public BtnUrls btnUrls = new BtnUrls();

        /**
         * 帮助按钮
         */
        @SerializedName("help_urls")
        public List<HelpUrl> helpUrlList = new ArrayList<>();

        public boolean isCocos() {
            return engine == ENGINE_COCOS;
        }

        public boolean isUnity() {
            return engine == ENGINE_UNITY;
        }
    }

    public static class BtnUrls {
        @SerializedName("invite")
        public String invite;
        @SerializedName("invite_disable")
        public String inviteDisable;
        @SerializedName("start")
        public String start;
        @SerializedName("ready")
        public String ready;
        @SerializedName("unready")
        public String unready;
        @SerializedName("guide_start")
        public String guideStart;
    }

    public static class Person {
        @SerializedName("achievement_list")
        public List<PersonAchievement> achievementList = emptyList();

        @SerializedName("style")
        public PersonStyle styleConfig = new PersonStyle();

        @SerializedName("currency")
        public PersonCurrency currency = new PersonCurrency();
    }

    public static class PersonAchievement {
        @SerializedName("title")
        public String title = "";

        @SerializedName("query_key")
        public String queryKey = "";
    }

    public static class PersonCurrency {
        @SerializedName("currency_list")
        public List<PersonCurrencyItem> currencyList = emptyList();
        @SerializedName("style")
        public PersonCurrencyStyle style = new PersonCurrencyStyle();
    }

    public static class PersonCurrencyItem {
        @SerializedName("icon")
        public String icon = "";

        @SerializedName("query_key")
        public String queryKey = "";

        @SerializedName("plus")
        public String plus = "";

        public PersonCurrencyItem() {
        }

        public PersonCurrencyItem(String icon, String queryKey) {
            this.icon = icon;
            this.queryKey = queryKey;
        }
    }

    public static class PersonCurrencyStyle {
        @SerializedName("background_color")
        public String bgColor = "#ffffff";

        @SerializedName("big_word_color")
        public String txtColor = "#ffffff";
    }

    public static class PersonStyle {
        @SerializedName("background_color")
        public String bgColor = "#ffffff";

        @SerializedName("small_word_color")
        public String keyColor = "#000000";

        @SerializedName("big_word_color")
        public String valueColor = "#000000";
    }

    public static class LayoutItem {
        private static final int STYLE_FULL_LINE = 1;

        @SerializedName("entry_img")
        public String entryImg = "";

        @SerializedName("style")
        public int style = 1;

        @SerializedName("jump_type")
        public int jumpType = 1;

        @SerializedName("jump_details")
        public JumpDetails jumpDetails = new JumpDetails();

        @SerializedName("report_comment")
        public String reportComment = "";

        @SerializedName("min_version")
        public String minVersion = "";

        @SerializedName("max_version")
        public String maxVersion = "";

        public boolean isFullLine() {
            return style == STYLE_FULL_LINE;
        }
    }

    public static class EntryLayout {
        @SerializedName("layout_list")
        public List<LayoutItem> layoutList = new ArrayList<>();

        public boolean getHasGuideByGameType(int betLevel) {
            for (LayoutItem item : layoutList) {
                if (item != null) {
                    JumpDetails jumpDetails = item.jumpDetails;
                    JumpMatchItem matchItem = jumpDetails.matchItem;
                    if (matchItem.getBetLevel() == betLevel) {
                        return matchItem.hasCocosGuide();
                    }
                }
            }
            return false;
        }
    }

    public static class FuncLayout {
        @SerializedName("bottom_start")
        public List<FuncItem> bottomStart = new ArrayList<>();

        @SerializedName("bottom_end")
        public List<FuncItem> bottomEnd = new ArrayList<>();
        @SerializedName("top_end")
        public List<FuncItem> topEnd = new ArrayList<>();
    }

    public static class FuncItem {
        @SerializedName("icon_url")
        public String iconUrl = "";

        @SerializedName("title")
        public String title = "";

        @SerializedName("jump_type")
        public int jumpType = 1;

        @SerializedName("jump_details")
        public JumpDetails jumpDetails = new JumpDetails();

        @SerializedName("report_comment")
        public String reportComment = "";

        @SerializedName("min_version")
        public String minVersion = "";

        @SerializedName("max_version")
        public String maxVersion = "";
    }

    public static class JumpDetails {
        @SerializedName("match_item")
        public JumpMatchItem matchItem = new JumpMatchItem();
        @SerializedName("deeplink")
        public String deeplink = "";
        @SerializedName("popup_window")
        public JumpPop popItem = new JumpPop();

        @NonNull
        @Override
        public String toString() {
            if (!TextUtils.isEmpty(deeplink)) {
                return "JumpDetails{" + deeplink + "}";
            }
            if (matchItem.betLevel != 0) {
                return "JumpDetails{" + matchItem.getDescStr() + "}";
            }
            return "JumpDetails{" + popItem.title + "}";
        }
    }

    public static class JumpMatchItem implements ILittleGameMatchInfo {
        @SerializedName("bet_level")
        public int betLevel = 0;
        @SerializedName("coin")
        public int coin = 0;
        @SerializedName("coin_desc")
        public String coinDesc = "";
        @SerializedName("match_mode")
        public int matchMode = 0;
        @SerializedName("room_mode")
        public int roomMode = 0;
        @SerializedName("game_mode")
        public int gameMode = 0;
        @SerializedName("game_currency_type")
        public int currencyType = 0;
        @SerializedName("has_guide")
        public boolean hasGuide = false; // 是否有Cocos新手教程

        @Override
        public int getRoomMode() {
            return roomMode;
        }

        @Override
        public int getMatchMode() {
            return matchMode;
        }

        @Override
        public int getBetLevel() {
            return betLevel;
        }

        @Override
        public int getGameMode() {
            return gameMode;
        }

        @Override
        public int getCurrencyType() {
            return currencyType;
        }

        @Override
        public boolean hasCocosGuide() {
            return hasGuide;
        }

        @Override
        public StringBuilder getDescStr() {
            return ILittleGameMatchInfo.super.getDescStr();
        }

        /**
         * 本地添加参数，用于记录当前是从创建还是匹配入口
         */
        private Boolean isCreate = false;

        @Override
        public Boolean getIsCreate() {
            return isCreate;
        }

        @Override
        public void setIsCreate(Boolean isCreateScene) {
            isCreate = isCreateScene;
        }
    }

    public static class JumpPop {
        @SerializedName("title")
        public String title = "";
        @SerializedName("layout_list")
        public List<LayoutItem> list = new ArrayList<>();
    }

    public static class HelpUrl {
        @SerializedName("game_mode")
        public int gameMode;
        @SerializedName("urls")
        public List<String> urls = new ArrayList<>();
        @SerializedName("hide")
        public boolean hide = false;
    }

    public static class Recommend {
        @SerializedName("high_light_board")
        public String highLightBoard = "";
        @SerializedName("high_light_board_short")
        public String highLightBoardShort = "";
    }

    public static class RecommendItem {
        @SerializedName("recommend_match_item")
        public JumpMatchItem recommendMatchItem = new JumpMatchItem();
    }

    public static class VipRoomConfig {
        @SerializedName("background_image")
        public String topBgUrl = "";

        @SerializedName("title_image")
        public String titleImage = "";

        @SerializedName("room_game_mode")
        public List<VipRoomGameMode> vipRoomGameModeList = Collections.emptyList();

        public VipRoomGameMode findByGameMode(int gameMode) {
            for (VipRoomGameMode mode : vipRoomGameModeList) {
                if (mode.gameMode == gameMode) {
                    return mode;
                }
            }
            return new VipRoomGameMode();
        }

    }

    public static class VipRoomGameMode {

        @SerializedName("game_mode")
        public int gameMode = 0;

        @SerializedName("text")
        public String text = "";

        @SerializedName("background_color")
        public List<String> backgroundColor = Collections.emptyList();

        @SerializedName("text_color")
        public List<String> textColor = Collections.emptyList();
    }

    public static class PopupEntryLayout {
        @SerializedName("game_mode")
        public int gameMode = 0;
        @SerializedName("game_mode_text")
        public String gameModeText = "";
        @SerializedName("layout_list")
        public List<LayoutItem> layoutList = emptyList();
    }
}
