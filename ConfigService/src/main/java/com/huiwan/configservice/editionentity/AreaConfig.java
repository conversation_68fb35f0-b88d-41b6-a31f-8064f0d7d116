package com.huiwan.configservice.editionentity;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.huiwan.configservice.ConfigHelper;
import com.wepie.liblog.main.FLog;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2021/03/20
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class AreaConfig implements IVersion {

    @SerializedName("version_num")
    public int versionNum = 0;

    @SerializedName("md5_version")
    private String versionMD5 = "";

    @SerializedName("region")
    private String region = "";

    @SerializedName("area_flag_base_url")
    private String flagBaseUrl = "https://pic.weplayapp.com/pic/national_flag/";

    @SerializedName("hot_area_list")
    private List<Area> hotRegionList = new ArrayList<>();


    @SerializedName("area_config_list")
    private List<Area> areaList = new ArrayList<>();

    private transient List<Area> hotShownList;
    private transient List<Area> shownConfigList;

    public List<Area> getHotRegionList() {
        if (hotShownList == null) {
            hotShownList = hotRegionList;
        }
        return hotShownList;
    }

    public boolean isAvailable() {
        if (!TextUtils.isEmpty(versionMD5)) {
            return true;
        } else {
            return versionNum > 0;
        }
    }

    @Override
    public int getVersion() {
        return versionNum;
    }

    @Override
    public String getVersionMD5() {
        return versionMD5;
    }

    @Override
    public String getRegion() {
        return region;
    }


    public List<Area> getShownAreaList() {
        if (shownConfigList == null) {
            shownConfigList = getAreaList();
        }
        return shownConfigList;
    }

    public List<Area> getAreaList() {
        if (areaList == null) {
            return new ArrayList<>();
        }
        return areaList;
    }

    public String getFlagBaseUrl() {
        return flagBaseUrl;
    }

    public AreaConfig.Area getArea(String area) {
        if (areaList == null) {
            areaList = new ArrayList<>();
        }
        for (int i = 0; i < areaList.size(); i++) {
            if (areaList.get(i).area.equals(area)) {
                return areaList.get(i);
            }
        }
        AreaConfig.Area area1 = new AreaConfig.Area();
        area1.area = area;
        return area1;
    }

    public static class Area {
        @SerializedName("area")
        public String area = "";

        @SerializedName("area_code")
        public String areaCode = "";

        @SerializedName("name")
        public String name = "";

        /**
         * region, 大区，逗号分割 "U,C"
         */
        @SerializedName("region")
        public String region = "";


        private transient String areaUrl = "";
        public transient boolean showHotArea = false;

        public String getAreaUrl() {
            if (!TextUtils.isEmpty(areaUrl)) {
                return areaUrl;
            }
            String localArea = area;
            if (TextUtils.isEmpty(localArea)) {
                localArea = "Global";
                FLog.e(new IllegalStateException("error area:" + areaCode + " " + name));
            }
            areaUrl = ConfigHelper.getInstance().getAreaConfig().getConfig().getFlagBaseUrl() + localArea;
            return areaUrl;
        }
    }
}
