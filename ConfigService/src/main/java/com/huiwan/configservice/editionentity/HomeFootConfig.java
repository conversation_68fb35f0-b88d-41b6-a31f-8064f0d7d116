package com.huiwan.configservice.editionentity;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class HomeFootConfig {

    @SerializedName("foot_icons")
    public List<FootIconConfig> footIcons = new ArrayList<>();

    public static class FootIconConfig {

        @SerializedName("index")
        public int index;

        @SerializedName("desc")
        public String desc = "";

        @SerializedName("selected_icon_info")
        public IconConfig selected = new IconConfig();

        @SerializedName("unselected_icon_info")
        public IconConfig unSelected = new IconConfig();

        @SerializedName("back_top_icon_info")
        public IconConfig backTopIconInfo = new IconConfig();

        public static class IconConfig {
            @SerializedName("icon_type")
            public int iconType;

            @SerializedName("icon_url")
            public String iconUrl = "";

            @SerializedName("desc_color")
            public String titleColor = "";

            public transient int defaultTitleColor;

            public transient int defaultImage;
        }

    }
}
