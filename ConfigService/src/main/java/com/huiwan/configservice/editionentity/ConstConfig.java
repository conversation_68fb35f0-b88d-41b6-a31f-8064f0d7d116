package com.huiwan.configservice.editionentity;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.common.MediaChooseLimit;

import java.util.ArrayList;
import java.util.List;

public class ConstConfig implements IVersion {

    @SerializedName("region")
    public String region = "";

    @SerializedName("md5_version")
    public String versionMD5 = "";

    @SerializedName("lang")
    public String lang = "";

    @SerializedName("version_num")
    public int version = 0;

    @SerializedName("official_tags")
    public List<OfficialTag> OfficialTags = new ArrayList<>();

    @SerializedName("new_emoji_open_switch")
    private boolean newEmojiOpenSwitch = false;

    @SerializedName("forbid_reply_invite_type")
    private List<Integer> forbidReplyInviteType = new ArrayList<>();

    @SerializedName("family_introduction")
    public FamilyIntroduction familyIntroduction = new FamilyIntroduction();

    @SerializedName("group_welcome_template")
    public ArrayList<String> groupWelcomeTemplate = new ArrayList<>();
    /**
     * groupCategory
     */
    @SerializedName("group_category")
    private List<GroupCategory> groupCategoryList = new ArrayList<>();
    /**
     * groupLevelConfig
     */
    @SerializedName("group_level_config")
    private List<GroupLevelConfig> groupLevelConfig = new ArrayList<>();

    /**
     * 群聊等级H5
     */
    @SerializedName("group_level_link")
    private String groupLevelLink;

    @SerializedName("fantasy_card_default_bg")
    private String fantasyCardbg = "";

    @SerializedName("msg_file_upload_limit")
    private MediaChooseLimit msgFileUploadLimit = new MediaChooseLimit();

    /**
     * 群聊页面拉取群主关联的语音房间隔
     */
    @SerializedName("sync_bind_adv_room_interval")
    private int syncInterval = 15;

    public Competition getCompetitionInfo() {
        return competition;
    }
    @SerializedName("competition")
    private Competition competition = new Competition();

    public static class Competition {
        @SerializedName("edit_battle_vip_limit")
        private int editBattleVipLimit = 5;

        /**
         * 创建赛事时抽取的费用 百分比
         * 10 代表 10%
         */
        @SerializedName("service_fee_ratio")
        private int createFeePercentRate = 10;

        /**
         * 奖励设置配置
         */
        @SerializedName("reward")
        public Reward reward = new Reward();

        /**
         * 赛事系统创建时，可选的段位列表。
         */
        @SerializedName("display_grade")
        public List<Integer> displayGrade = new ArrayList<>();

        public int getEditBattleVipLimit() {
            return editBattleVipLimit;
        }
        /**
         * 获取创建赛事时抽取的费用 百分比
         * 10 代表 10%
         */
        public int getCreateFeePercentRate() {
            return createFeePercentRate;
        }

        /**
         * 奖励相关设置配置
         */
        public static class Reward {
            /**
             * 第一名最小值
             */
            @SerializedName("min_first")
            public int minFirst = 5000;
            /**
             * 第二名最小值
             */
            @SerializedName("min_second")
            public int minSecond = 3000;
            /**
             * 最大值
             */
            @SerializedName("max")
            public int max = 1000_000;
        }
    }

    @Override
    public int getVersion() {
        return version;
    }

    @Override
    public String getVersionMD5() {
        return versionMD5;
    }

    @Override
    public String getRegion() {
        return region;
    }

    @Override
    public boolean isAvailable() {
        if (!TextUtils.isEmpty(versionMD5)) {
            return true;
        } else {
            return version > 0;
        }
    }

    public String getGroupLevelLink() {
        return groupLevelLink;
    }

    public void setGroupLevelLink(String groupLevelLink) {
        this.groupLevelLink = groupLevelLink;
    }

    public List<Integer> getForbidReplyInviteType() {
        if (forbidReplyInviteType == null) {
            forbidReplyInviteType = new ArrayList<>();
        }
        return forbidReplyInviteType;
    }

    public MediaChooseLimit getMsgFileUploadLimit() {
        if (null != msgFileUploadLimit) {
            msgFileUploadLimit.init();
        }
        return msgFileUploadLimit;
    }

    public void setMsgFileUploadLimit(MediaChooseLimit msgFileUploadLimit) {
        this.msgFileUploadLimit = msgFileUploadLimit;
    }

    public int getSyncInterval() {
        if (syncInterval <= 0) {
            syncInterval = 15;
        }
        return syncInterval;
    }

    public List<GroupCategory> getGroupCategoryList() {
        return groupCategoryList;
    }

    public void setGroupCategoryList(List<GroupCategory> groupCategory) {
        this.groupCategoryList = groupCategory;
    }

    public List<GroupLevelConfig> getGroupLevelConfig() {
        return groupLevelConfig;
    }

    public void setGroupLevelConfig(List<GroupLevelConfig> groupLevelConfig) {
        this.groupLevelConfig = groupLevelConfig;
    }

    public static class OfficialTag {
        @SerializedName("tag_type")
        public int tagType;

        @SerializedName("desc")
        public String desc = "";

        @SerializedName("desc_color")
        public String descColor = "";

        @SerializedName("border_color")
        public String borderColor = "";
    }

    /**
     * 群聊创建类型数据 兴趣群聊主类型
     */

    public static class GroupCategory {
        /**
         * 分类ID
         */
        @SerializedName("category_id")
        private Integer categoryId;
        /**
         * 分类名字
         */
        @SerializedName("category_name")
        private String categoryName;

        /**
         * 图标
         */
        @SerializedName("icon")
        private String icon;

        /**
         * 选中时背景色
         */
        @SerializedName("bg_color")
        private String bgColor;

        /**
         * 选中时背景边框
         */
        @SerializedName("bg_border_color")
        private String bgBorderColor;

        /**
         * 选中时文字颜色
         */
        @SerializedName("text_focus_color")
        private String textFocusColor;

        /**
         * subList
         */
        @SerializedName("sub_list")
        private List<SubType> subList = new ArrayList<>();

        /**
         * 是否被选中,本地使用,与服务器无关
         */
        public boolean isSelect = false;

        public Integer getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(Integer categoryId) {
            this.categoryId = categoryId;
        }

        public String getCategoryName() {
            return categoryName;
        }

        public void setCategoryName(String categoryName) {
            this.categoryName = categoryName;
        }

        public List<SubType> getSubList() {
            if (subList == null) {
                subList = new ArrayList<>();
            }
            return subList;
        }

        public void setSubList(List<SubType> subList) {
            this.subList = subList;
        }


        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getBgColor() {
            return bgColor;
        }

        public void setBgColor(String bgColor) {
            this.bgColor = bgColor;
        }

        public String getBgBorderColor() {
            return bgBorderColor;
        }

        public void setBgBorderColor(String bgBorderColor) {
            this.bgBorderColor = bgBorderColor;
        }

        public String getTextFocusColor() {
            return textFocusColor;
        }

        public void setTextFocusColor(String textFocusColor) {
            this.textFocusColor = textFocusColor;
        }

        /**
         * 兴趣群聊子类型
         */
        public static class SubType {
            /**
             * subCateId
             */
            @SerializedName("sub_cate_id")
            private Integer subCateId;
            /**
             * subName
             */
            @SerializedName("sub_name")
            private String subName;

            public Integer getSubCateId() {
                return subCateId;
            }

            public void setSubCateId(Integer subCateId) {
                this.subCateId = subCateId;
            }

            public String getSubName() {
                return subName;
            }

            public void setSubName(String subName) {
                this.subName = subName;
            }
        }
    }

    /**
     * 群聊等级信息配置
     */
    public static class GroupLevelConfig {
        /**
         * level
         */
        @SerializedName("level")
        private Integer level;
        /**
         * minExp
         */
        @SerializedName("min_exp")
        private Integer minExp;

        /**
         * 等级图
         */
        @SerializedName("icon")
        private String icon;
        /**
         * groupMemberLimit
         */
        @SerializedName("group_member_limit")
        private Integer groupMemberLimit;
        /**
         * adminLimit
         */
        @SerializedName("admin_limit")
        private Integer adminLimit;
        /**
         * 是否能自定义群头像
         */
        @SerializedName("custom_group_image")
        private Boolean customGroupImage;
        /**
         * recommendExtraScore
         */
        @SerializedName("recommend_extra_score")
        private Float recommendExtraScore;

        public Integer getLevel() {
            return level;
        }

        public void setLevel(Integer level) {
            this.level = level;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getIcon() {
            return icon;
        }

        public Integer getMinExp() {
            return minExp;
        }

        public void setMinExp(Integer minExp) {
            this.minExp = minExp;
        }

        public Integer getGroupMemberLimit() {
            return groupMemberLimit;
        }

        public void setGroupMemberLimit(Integer groupMemberLimit) {
            this.groupMemberLimit = groupMemberLimit;
        }

        public Integer getAdminLimit() {
            return adminLimit;
        }

        public void setAdminLimit(Integer adminLimit) {
            this.adminLimit = adminLimit;
        }

        public Boolean getCustomGroupImage() {
            return customGroupImage;
        }

        public void setCustomGroupImage(Boolean customGroupImage) {
            this.customGroupImage = customGroupImage;
        }

        public Float getRecommendExtraScore() {
            return recommendExtraScore;
        }

        public void setRecommendExtraScore(Float recommendExtraScore) {
            this.recommendExtraScore = recommendExtraScore;
        }
    }

    /**
     * 根据群等级获取群聊等级配置信息
     *
     * @param level
     * @return
     */
    public GroupLevelConfig getGroupLevelConfig(int level) {
        for (GroupLevelConfig levelConfig : groupLevelConfig) {
            if (levelConfig.level == level) {
                return levelConfig;
            }
        }
        return null;
    }

    /**
     * 根据group一级类型获取一级信息
     *
     * @param categoryId
     * @return
     */
    public GroupCategory getGroupCateGory(int categoryId) {
        for (GroupCategory groupCategory : groupCategoryList) {
            if (groupCategory.categoryId == categoryId) {
                return groupCategory;
            }
        }
        return null;
    }

    /**
     * 根据group一级类型获取一级信息
     *
     * @param categoryId
     * @return
     */
    public GroupCategory.SubType getGroupSubType(int categoryId, int subType) {
        for (GroupCategory groupCategory : groupCategoryList) {
            if (groupCategory.categoryId == categoryId) {
                for (GroupCategory.SubType type : groupCategory.subList) {
                    if (type.subCateId == subType) {
                        return type;
                    }
                }
            }
        }
        return null;
    }

    public static class FamilyIntroduction {
        @SerializedName("family_play_page")
        public String familyPlayPage = "";

        @SerializedName("family_task_page")
        public String familyTaskPage = "";

        @SerializedName("family_recommend_page")
        public String familyRecommendPage = "";
    }


    public OfficialTag getOfficialTag(int tagType) {
        OfficialTag tag = null;
        for (OfficialTag t : OfficialTags) {
            if (t.tagType == tagType && !TextUtils.isEmpty(t.desc)) {
                tag = t;
                break;
            }
        }
        return tag;
    }

    public boolean getEmojiSwitch() {
        return newEmojiOpenSwitch;
    }
}
