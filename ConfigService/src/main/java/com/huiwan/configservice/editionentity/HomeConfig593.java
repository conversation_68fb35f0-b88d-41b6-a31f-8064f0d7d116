package com.huiwan.configservice.editionentity;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2019-10-24
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class HomeConfig593 implements IVersion {
    private static final int ROOM_WIDGET_TYPE_UNDEFINED = 0;
    private static final int ROOM_WIDGET_TYPE_SITUATION_PUZZLE = 1;

    @SerializedName("version_num")
    private int version = 0;

    @SerializedName("md5_version")
    private String versionMD5 = "";

    @SerializedName("region")
    private String region = "";

    @SerializedName("list")
    private List<ConfigItem> homeGameConfigInfo = new ArrayList<>();

    @SerializedName("we_party")
    private ConfigWeParty weParty = new ConfigWeParty();

    @SerializedName("game_center")
    private GameCenter gameCenter = new GameCenter();

    @SerializedName("more_game_config")
    private MoreGameConfig moreGameConfig = new MoreGameConfig();

    public HomeConfig593() {
    }

    @Override
    public int getVersion() {
        return version;
    }

    @Override
    public String getVersionMD5() {
        return versionMD5;
    }

    @Override
    public String getRegion() {
        return region;
    }

    public List<ConfigItem> getHomeGameConfigInfo() {
        if (homeGameConfigInfo == null) {
            homeGameConfigInfo = new ArrayList<>();
        }
        return homeGameConfigInfo;
    }

    public GameItem getConfigItemByGameType(int gameType) {
        if (homeGameConfigInfo == null) {
            return null;
        }
        for (ConfigItem item : homeGameConfigInfo) {
            for (GameItem gameItem : item.getList()) {
                if (gameItem.gameType == gameType) {
                    return gameItem;
                }
            }
        }
        return null;
    }

    public ConfigWeParty getWeParty() {
        return weParty;
    }

    public GameCenter getGameCenter() {
        if (gameCenter == null) {
            gameCenter = new GameCenter();
        }
        return gameCenter;
    }

    public MoreGameConfig getMoreGameConfig() {
        if (moreGameConfig == null) {
            moreGameConfig = new MoreGameConfig();
        }
        return moreGameConfig;
    }

    public static class MoreGameConfig {
        @SerializedName("is_show")
        public boolean show = false;

        @SerializedName("game_list")
        private List<MoreGameItem> list = new ArrayList<>();

        /**
         * 如果后台返回game_list = null,那么默认值就没什么用
         */
        public List<MoreGameItem> getList() {
            if (list == null) {
                list = new ArrayList<>();
            }
            return list;
        }

        public static class MoreGameItem extends GameItem {
            @SerializedName("small_icon")
            private String smallIcon = "";

            public String getSmallIcon() {
                return smallIcon;
            }
        }
    }

    public static class ConfigItem {
        @SerializedName("name")
        private String name;

        @SerializedName("game_list")
        private List<GameItem> list = new ArrayList<>();

        @SerializedName("contribute_list")
        private ContributeItem contributeList = new ContributeItem();

        @SerializedName("help_customer_service")
        private boolean helpCustomerService = false;

        @SerializedName("tab_config")
        public TabConfig tabConfig = new TabConfig();

        @SerializedName("home_tab_head_bg")
        private String homeTabHeadBg = "";

        @SerializedName("home_foot_icons")
        private HomeFootConfig homeFootIcons = new HomeFootConfig();

        public List<GameItem> getList() {
            if (list == null) {
                list = new ArrayList<>();
            }
            return list;
        }

        public String getName() {
            return name;
        }

        public ContributeItem getContributeList() {
            return contributeList;
        }

        public String getHomeTabHeadBg() {
            return homeTabHeadBg;
        }

        public HomeFootConfig getHomeFootIcons() {
            return homeFootIcons;
        }

        public boolean isHelpCustomerService() {
            return helpCustomerService;
        }
    }

    public static class TabConfig {
        @SerializedName("discover_list")
        public List<EntranceConfig> discoverList = new ArrayList<>();
        @SerializedName("my_list")
        public List<EntranceConfig> myList = new ArrayList<>();

        public List<Integer> getTabConfigRedDotIdList() {
            List<Integer> idList = new ArrayList<>();
            addId2List(discoverList, idList);
            addId2List(myList, idList);
            return idList;
        }

        public List<Integer> getDiscoverConfigRedDotIds() {
            List<Integer> idList = new ArrayList<>();
            addId2List(discoverList, idList);
            return idList;
        }

        public List<Integer> getMeConfigRedDotIds() {
            List<Integer> idList = new ArrayList<>();
            addId2List(myList, idList);
            return idList;
        }

        private void addId2List(List<EntranceConfig> configList, List<Integer> idList) {
            if (configList == null || configList.isEmpty()) {
                return;
            }
            for (EntranceConfig config : configList) {
                if (config.redDotId != 0) {
                    idList.add(config.redDotId);
                }
            }
        }
    }

    public static class EntranceConfig {
        @SerializedName("icon")
        public String icon = "";
        @SerializedName("name")
        public String name = "";
        @SerializedName("desc")
        public String desc = "";
        @SerializedName("desc_color")
        public String descColor = "";
        @SerializedName("url")
        public String url = "";
        @SerializedName("red_dot_id")
        public int redDotId = 0;
    }

    public static class GameItem {
        @SerializedName("is_little_game")
        private boolean hotUpdate;
        @SerializedName("hot_update")
        private boolean littleGame;
        @SerializedName("game_type")
        private int gameType;
        @SerializedName("game_cate")
        private int cateSize;
        @SerializedName("cover_image")
        private String imgUrl;
        @SerializedName("recommand_icon")
        private String recommandIcon = "";
        @SerializedName("recommand_text")
        private String recommandText = "";
        @SerializedName("combine")
        private int combine;
        @SerializedName("game_name")
        private String gameName = "";
        @SerializedName("widget_type")
        private int widget = 0;
        @SerializedName("extra_unity_base_config")
        private UnityBaseConfig unityBaseConfig = new UnityBaseConfig();
        @SerializedName("deeplink")
        private String deeplink = "";

        @SerializedName("unity_home")
        private boolean unityHome = false;

        @Override
        public String toString() {
            return "GameItem{" +
                    "gameType=" + gameType +
                    ", gameName='" + gameName + '\'' +
                    '}';
        }

        public GameItem() {
        }

        public GameItem(int gameType) {
            this.gameType = gameType;
        }

        public int getGameType() {
            return gameType;
        }

        public void setLittleGame(boolean littleGame) {
            this.littleGame = littleGame;
        }

        public boolean getLittleGame() {
            return littleGame;
        }

        public int getCateSize() {
            return cateSize;
        }

        public String getImgUrl() {
            return imgUrl;
        }

        //是否是小游戏
        public boolean isLittleGame() {
            return hotUpdate;
        }

        public boolean isCombine() {
            return combine == 1;
        }

        public String getRecommandIcon() {
            return recommandIcon;
        }

        public String getRecommandText() {
            return recommandText;
        }

        public String getGameName() {
            return gameName;
        }

        public void setGameName(String gameName) {
            this.gameName = gameName;
        }

        public boolean isSituationPuzzle() {
            return widget == ROOM_WIDGET_TYPE_SITUATION_PUZZLE;
        }

        public UnityBaseConfig getUnityBaseConfig() {
            return unityBaseConfig;
        }

        public String getDeeplink() {
            return deeplink;
        }

        public boolean isUnityHome() {
            return unityHome;
        }

        public static boolean isValid(int gameType) {
            return gameType > 0;
        }
    }

    public static class ContributeItem {
        @SerializedName("music_hum")
        private boolean musicHumState;
        @SerializedName("spy")
        private boolean spyState;
        @SerializedName("draw")
        private boolean drawState;
        @SerializedName("voice_room")
        private boolean voiceRoomState;

        public ContributeItem() {
        }

        public boolean showMusicHumSelect() {
            return musicHumState;
        }

        public boolean showSpySelect() {
            return spyState;
        }

        public boolean showDrawSelect() {
            return drawState;
        }

        public boolean showVoiceRoomSelect() {
            return voiceRoomState;
        }
    }

    public static class ConfigWeParty {
        @SerializedName("name")
        private String name;
        @SerializedName("module_image")
        private List<String> moduleImages;
        @SerializedName("activity_info")
        private ActivityInfo activityInfo = new ActivityInfo();

        public String getName() {
            return name;
        }

        public List<String> getModuleImages() {
            return moduleImages;
        }

        public ActivityInfo getActivityInfo() {
            return activityInfo;
        }
    }

    public static class ActivityInfo {
        @SerializedName("icon")
        private String icon;

        @SerializedName("h5_url")
        private String h5Url;

        @SerializedName("is_open")
        private boolean isOpen;

        @SerializedName("name")
        private String name;

        @SerializedName("label_word")
        private String labelWord;

        public String getIcon() {
            return icon;
        }

        public String getH5Url() {
            return h5Url;
        }

        public boolean getOpenState() {
            return isOpen;
        }

        public String getName() {
            return name;
        }

        public String getLabelWord() {
            return labelWord;
        }

    }

    public static class UnityBaseConfig {
        @SerializedName("id")
        private int id;
        @SerializedName("path")
        private String path;
        @SerializedName("is_config_game")
        private boolean isConfigGame;
        @SerializedName("scene_type")
        private int sceneType = -1;
        @SerializedName("loading_url")
        private String loadingUrl = "";
        @SerializedName("orientation_type")
        private int orientationType = 1;
        @SerializedName("pronunciation_game")
        private boolean isPronunciationGame = true;
        @SerializedName("min_client_version")
        private String minClientVersion = "";

        public boolean isConfigGame() {
            return isConfigGame;
        }

        public int getSceneType() {
            return sceneType;
        }

        public String getLoadingUrl() {
            return loadingUrl;
        }

        public boolean isLandscape() {
            return orientationType == 0;
        }

        public boolean isPronunciationGame() {
            return isPronunciationGame;
        }

        public String getMinClientVersion() {
            return minClientVersion;
        }


        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }
    }

    public void resetVersion() {
        version = 0;
    }

    public boolean isAvailable() {
        return version > 0 || !TextUtils.isEmpty(versionMD5);
    }
}
