package com.huiwan.configservice.editionentity

import com.google.gson.annotations.SerializedName

class GameCenter {
    @SerializedName("game_center_list")
    val gameCenterList: MutableList<GameCenterItem> = ArrayList()

    @SerializedName("currency")
    val currency: GameCenterCurrency = GameCenterCurrency()

    companion object {
        fun sortByPlayTime(gamesMap: Map<Int, Long>?, gameCenterList: MutableList<GameCenterItem>) {
            gamesMap?.let {
                gameCenterList.forEach {
                    val time = gamesMap[it.gameType] ?: Long.MIN_VALUE
                    it.playTime = time
                }
                gameCenterList.sortByDescending { it.playTime }
            }
        }
    }
}

class GameCenterItem {
    @SerializedName("game_type")
    var gameType: Int = 0

    @Transient
    var playTime: Long = Long.MIN_VALUE
}

class GameCenterCurrency {
    @SerializedName("currency_list")
    val list: List<VsCenterLittleGameConfig.PersonCurrencyItem> = emptyList()
}

