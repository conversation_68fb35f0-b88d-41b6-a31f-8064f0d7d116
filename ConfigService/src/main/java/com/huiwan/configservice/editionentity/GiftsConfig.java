package com.huiwan.configservice.editionentity;

import android.text.TextUtils;
import android.util.SparseArray;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.IConfig;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.constants.ConfigId;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * date 2018/4/2
 * email <EMAIL>
 *
 * <AUTHOR>
 * <p>
 * 所有礼物
 */
public class GiftsConfig implements IConfig {

    @SerializedName("version_num")
    private int version = 0;

    @SerializedName("md5_version")
    private String versionMD5 = "";

    @SerializedName("region")
    private String region = "";

    @SerializedName("gift_config")
    private ArrayList<Gift> gifts = new ArrayList<>();

    @SerializedName("gift_category")
    private List<GiftCategory> giftCategoryList = new ArrayList<>();

    @SerializedName("gift_endorsement")
    private Endorsement endorsement = new Endorsement();

    public Endorsement getEndorsement() {
        if (endorsement == null) {
            endorsement = new Endorsement();
        }
        return endorsement;
    }

    @SerializedName("nation_flag_endorsement")
    private Endorsement nationFlagEndorsement = new Endorsement();

    public Endorsement getNationFlagEndorsement() {
        if (nationFlagEndorsement == null) {
            nationFlagEndorsement = new Endorsement();
        }
        return nationFlagEndorsement;
    }

    @Override
    public int getConfigId() {
        return ConfigId.GIFTS_CONFIG_ID;
    }

    @NonNull
    private transient List<Gift> shownGift = new LinkedList<>();

    @Expose(serialize = false, deserialize = false)
    private transient SparseArray<Gift> cacheGift = new SparseArray<>();

    public void transform() {
        updateShownGifts();
    }

    @Override
    public int getVersion() {
        return version;
    }

    @Override
    public String getVersionMD5() {
        return versionMD5;
    }

    @Override
    public String getRegion() {
        return region;
    }

    public boolean isAvailable() {
        if (!TextUtils.isEmpty(versionMD5)) {
            return true;
        } else {
            return version > 0;
        }
    }

    public List<GiftCategory> getGiftCategoryList() {
        return new ArrayList<>(giftCategoryList);
    }

    @Nullable
    public GiftCategory getCategoryById(int categoryId) {
        for (GiftCategory category : giftCategoryList) {
            if (category.id == categoryId) {
                return category;
            }
        }
        return null;
    }

    /**
     * 礼物原始数据，没有过滤
     */
    public List<Gift> getTotalGift() {
        return new ArrayList<>(gifts);
    }

    @NonNull
    public List<Gift> getShownGift() {
        return new ArrayList<>(shownGift);
    }

    @NonNull
    public List<Gift> getPreDownloadGifts() {
        Set<Gift> set = new HashSet<>(shownGift);
        Set<Integer> giftCardGiftIdSet = ConfigHelper.getInstance().getPropConfig().getGiftCardGiftIdSet();
        for (Gift gift : gifts) {
            if (giftCardGiftIdSet.contains(gift.getGift_id())) {
                set.add(gift);
            }
        }
        return new ArrayList<>(set);
    }

    @Nullable
    public Gift getGift(int id) {
        Gift g = cacheGift.get(id);
        if (g != null) {
            return g;
        }
        for (Gift gift : gifts) {
            if (gift.getGift_id() == id) {
                cacheGift.put(id, gift);
                return gift;
            }
        }
        return null;
    }

    private void updateShownGifts() {
        List<Gift> list = new LinkedList<>();
        for (Gift gift : gifts) {
            if (gift.canShow()) {
                list.add(gift);
            }
        }
        shownGift = list;
    }

    public boolean showCombo(int giftId) {
        for (Gift gift : gifts) {
            if (gift.getGift_id() == giftId) {
                return gift.showCombo();
            }
        }
        return false;
    }

    public List<Gift> getAuctionGift() {
        List<Gift> auctionList = new ArrayList<>();
        for (Gift gift : shownGift) {
            if (gift.showInScene(Gift.GIFT_SCENE_AUCTION_ROOM)) {
                auctionList.add(gift);
            }
        }
        return auctionList;
    }

    public List<Gift> getShownListByScene(int scene) {
        List<Gift> list = new ArrayList<>();
        for (Gift gift : shownGift) {
            if (gift.showInScene(scene)) {
                list.add(gift);
            }
        }
        return list;
    }

    public static class Endorsement {
        @SerializedName("gift_id")
        public int lastGiftId; // 上期礼物
        @SerializedName("gift_name")
        public String giftName = ""; // 上周礼物名
        @SerializedName("uid")
        public int lottoLastUid; // 上周礼物代言人
        @SerializedName("user_name")
        public String nickname; // 上周礼物代言人昵称
        @SerializedName("last_bg")
        public LastBg lastBg = new LastBg();
        @SerializedName("lotto")
        public String lotto = ""; // 本周背景图
        @SerializedName("icon")
        public String icon = ""; //本周 点击 icon
        @SerializedName("url")
        public String url = ""; // 本周 活动 url
        @SerializedName("lotto_desc")
        public String lottoDesc = ""; // 本周活动标签 "大乐透"
        @SerializedName("current_gift")
        public int curGift = 0; // 本周 礼物
    }

    @NonNull
    @Override
    public String toString() {
        return "GiftConfig{version: " + version + ", size: " + gifts.size() + "}";
    }

    public static class GiftCategory {
        @SerializedName("id")
        public int id;
        @SerializedName("name")
        public String name = "";
    }

    public static class LastBg {
        @SerializedName("landscape")
        public String landscape = "";
        @SerializedName("portrait")
        public String portrait = "";
    }
}
