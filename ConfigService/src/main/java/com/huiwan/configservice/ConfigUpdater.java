package com.huiwan.configservice;

import android.annotation.SuppressLint;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.TypeAdapter;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.AUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.baseservice.ServiceObservable;
import com.huiwan.baseservice.ServiceObserver;
import com.huiwan.configservice.constentity.ConstV3Info;
import com.huiwan.configservice.editionentity.AISceneGiftOrdersConfig;
import com.huiwan.configservice.editionentity.IVersion;
import com.huiwan.configservice.http.ConstApi;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.store.PrefUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.three.http.constant.Config;
import com.wejoy.weplay.ex.ILife;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.main.HLog;

import java.io.File;
import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

class ConfigUpdater {
    private static final String PREF_FILE_NAME = "hw_config_service_version_pref";
    private static final String PREF_PKG_REQUIRE_VERSION = "require_version";
    private static final String PREF_PKG_OPTIONAL_VERSION = "optional_version";
    static final String TAG = "ConfigUpdater";
    final List<ConfigInfo<? extends IVersion>> allConfig = new ArrayList<>();
    final List<ConfigInfo<? extends IVersion>> requiredConfig = new ArrayList<>();
    final List<ConfigInfo<? extends IVersion>> optionalConfig = new ArrayList<>();
    private final SharedPreferences sp;
    private final ServiceObservable<IVersion, ServiceObserver<IVersion>> observable = new ServiceObservable<>();

    ConfigUpdater(List<ConfigInfo<?>> configInfoList) {
        for (ConfigInfo<?> configInfo : configInfoList) {
            if (configInfo.required) {
                requiredConfig.add(configInfo);
            } else {
                optionalConfig.add(configInfo);
            }
        }
        allConfig.addAll(configInfoList);
        PrefUtil.getInstance().importFromSharedPreferences(PREF_FILE_NAME);
        sp = PrefUtil.getInstance().getPref();

    }

    void register(ServiceObserver<IVersion> observer) {
        observable.register(observer);
    }

    void unregister(ServiceObserver<IVersion> observer) {
        observable.unregister(observer);
    }

    void mainFinishClear() {
        observable.unregisterAll();
    }

    <T extends IVersion> void loadLocalConfig(ConfigInfo<T> configInfo) {
        String region = GlobalConfigManager.getInstance().getRegion();
        HLog.d(TAG, "loadLocalConfig, c:{}, {}", configInfo.url, region);
        if (configInfo.isNotInit()) {
            //获取当前所属大区，加载对应大区的缓存
            T config = loadEntity(configInfo);
            configInfo.updateConfigIfNeed(false, false, config);
            observable.notifyChange(config);
            HLog.d(TAG, HLog.USR, "loadLocalConfig,config={}, region={},config={}", configInfo.url, region, config);
        }
    }

    void updateRequiredConfig(ConfigHelper.UpdateCallback callback) {
        HLog.d(TAG, "updateRequiredConfig, callback: {}", callback);
        SoftReference<ConfigHelper.UpdateCallback> softReference = new SoftReference<>(callback);
        ConfigHelper.UpdateCallback versionUpdater = new ConfigHelper.UpdateCallback() {
            @Override
            public void onSuccess() {
                ConfigHelper.UpdateCallback updateCallback = softReference.get();
                if (updateCallback != null) updateCallback.onSuccess();
                updateRequiredConfigVersion();
            }

            @Override
            public void onFailed(String msg) {
                ConfigHelper.UpdateCallback updateCallback = softReference.get();
                if (updateCallback != null) updateCallback.onFailed(msg);
            }
        };
        ConfigHelper.UpdateCallback countCallback = new CountUpdateCallback(versionUpdater, requiredConfig.size());
        boolean force = requiredConfigNeedForceUpdate();
        for (ConfigInfo<?> configInfo : requiredConfig) {
            updateConfigInternal(configInfo, force, countCallback);
        }
    }

    void updateAllConfig(boolean force, ConfigHelper.UpdateCallback callback) {
        HLog.d(TAG, HLog.USR, "updateAllConfig, force: {}, callback: {}", force, callback);
        ConfigHelper.UpdateCallback countCallback = new CountUpdateCallback(callback, allConfig.size());
        for (ConfigInfo<?> configInfo : allConfig) {
            updateConfigInternal(configInfo, force, countCallback);
        }
    }

    <T extends IVersion> void updateConfig(Class<T> configClass, boolean force, ConfigHelper.UpdateCallback callback) {
        HLog.d(TAG, "updateConfig, configClass: {}, force:{}, callback: {}", configClass, force, callback);
        for (ConfigInfo<?> configInfo : allConfig) {
            if (configInfo.c == configClass) {
                updateConfigInternal(configInfo, force, callback);
                return;
            }
        }
        HLog.e(TAG, HLog.CLR, "error no config class found: {}", configClass);
    }

    private <T extends IVersion> void updateConfigInternal(ConfigInfo<T> configInfo, boolean force, ConfigHelper.UpdateCallback callback) {
        HLog.d(TAG, HLog.USR, "updateConfigInternal,url={},c={}, version={}, force={} ", configInfo.url, configInfo.c.getSimpleName(), configInfo.getVersion(), force);
        ConstApi.getVersionConfig(configInfo.required, configInfo.url, configInfo.getReqParam(force), new LifeDataCallback<String>((ILife) null) {
            @Override
            public void onSuccess(Result<String> result) {
                JsonObject resultData = null;
                try {
                    JsonObject json = JsonParser.parseString(result.data).getAsJsonObject();
                    resultData = json.get("result").getAsJsonObject();
                    int respVersion = resultData.has("version_num") ? resultData.get("version_num").getAsInt() : 0;
                    String versionMD5 = resultData.has("md5_version") ? resultData.get("md5_version").getAsString() : "";
                    boolean dataValid = respVersion != 0 || !TextUtils.isEmpty(versionMD5) || configInfo.c == ConstV3Info.class || configInfo.c == AISceneGiftOrdersConfig.class;
                    HLog.d(TAG, HLog.USR, "updateConfigInternal,onSuccess! url={},dataValid={}, resultData={}", configInfo.url, dataValid, resultData);

                    if (dataValid) {
                        TypeAdapter<T> adapter = configInfo.adapter;
                        T o;
                        if (adapter != null) {
                            o = adapter.fromJsonTree(resultData);
                        } else {
                            o = JsonUtil.fromJson(resultData, configInfo.c);
                        }
                        if (o != null) {
                            boolean apply = configInfo.updateConfigIfNeed(true, force, o);
                            String region = GlobalConfigManager.getInstance().getRegion();
                            if (apply) {
                                observable.notifyChange(configInfo.getConfig());
                                writeFile(region, configInfo.cacheFileName, resultData.toString());
                            }
                            HLog.d(TAG, HLog.USR, "updateConfigInternal! config={}, apply={}, version={},region={}", configInfo.url, apply, o.getVersion(), region);
                        }
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    } else {
                        if (callback != null) {
                            callback.onFailed("server data error");
                        }
                    }
                } catch (Throwable e) {
                    if (callback != null) {
                        callback.onFailed("error parse server data");
                    }
                    String logMsg = "updateConfigInternal error! domain=" + Config.domain + ",url=" + configInfo.url + ",e=" + e;
                    HLog.e(TAG, HLog.USR, logMsg);
                    HLog.aliLog(AliNetLogUtil.PORT.configFail, "updateConfigInternal error!", AliNetLogUtil.TYPE.err, logMsg + ", result=" + (null == resultData ? "" : resultData.toString()));
                }
            }

            @Override
            public void onFail(int code, String msg) {
                if (callback != null) {
                    callback.onFailed(msg);
                }
                String logMsg = "updateConfigInternal onFail! code=" + code + ", msg=" + msg;
                HLog.e(TAG, HLog.USR, logMsg);
                HLog.aliLog(AliNetLogUtil.PORT.configFail, "updateConfigInternal fail!", AliNetLogUtil.TYPE.err, logMsg);
            }
        });
    }

    public boolean allConfigAvailable() {
        for (ConfigInfo<?> configInfo : allConfig) {
            if (!configInfo.getConfig().isAvailable()) {
                return false;
            }
        }
        return true;
    }

    private void writeFile(String region, String fileName, String value) {
        FileUtil.writeFileAsync(getCacheFileName(region, fileName), value);
    }

    private String getCacheFileName(String region, String fileName) {
        if (TextUtils.isEmpty(region)) {
            region = "public_region";
        }
        return region + File.separator + LibBaseUtil.getLang() + File.separator + fileName;
    }

    private <T extends IVersion> T loadEntity(ConfigInfo<T> configInfo) {
        String region = GlobalConfigManager.getInstance().getRegion();
        String fileName = getCacheFileName(region, configInfo.cacheFileName);
        Class<? extends T> c = configInfo.c;
        String json = FileUtil.readFileSync(fileName);
        T config = null;
        boolean flag = !TextUtils.isEmpty(json);
        if (flag) {
            try {
                String toDecode = json;
                if (toDecode.charAt(0) != '{') {
                    toDecode = AUtil.aOutTextDx9(toDecode);
                }
                if (toDecode.charAt(0) == '{') {
                    TypeAdapter<T> adapter = configInfo.adapter;
                    if (adapter != null) {
                        config = adapter.fromJson(toDecode);
                    } else {
                        config = JsonUtil.fromJson(toDecode, c);
                    }
                }
            } catch (Throwable e) {
                HLog.d(TAG, HLog.USR, "loadEntity error! msg={}", e);
            }
        }
        HLog.d(TAG, HLog.USR, "loadEntity, flag={}", flag);
        return config;
    }

    static class CountUpdateCallback implements ConfigHelper.UpdateCallback {
        private final ConfigHelper.UpdateCallback realCallback;
        private final int needSuccessCount;
        private final AtomicInteger curSuccessCount = new AtomicInteger(0);
        private boolean failed = false;

        public CountUpdateCallback(ConfigHelper.UpdateCallback realCallback, int needSuccessCount) {
            this.realCallback = realCallback;
            this.needSuccessCount = needSuccessCount;
        }

        @Override
        public void onSuccess() {
            if (realCallback == null) {
                return;
            }
            if (curSuccessCount.incrementAndGet() == needSuccessCount) {
                realCallback.onSuccess();
            }
        }

        @Override
        public void onFailed(String msg) {
            if (failed || realCallback == null) {
                return;
            }
            failed = true;
            realCallback.onFailed(msg);
        }
    }

    private boolean requiredConfigNeedForceUpdate() {
        String version = sp.getString(PREF_PKG_REQUIRE_VERSION, "");
        return version == null || !version.equals(LibBaseUtil.getBaseConfig().versionName);
    }

    @SuppressLint("ApplySharedPref")
    private void updateRequiredConfigVersion() {
        sp.edit().putString(PREF_PKG_REQUIRE_VERSION, LibBaseUtil.getBaseConfig().versionName).apply();
    }

    private boolean optionalConfigNeedForceUpdate() {
        String version = sp.getString(PREF_PKG_OPTIONAL_VERSION, "");
        return version == null || !version.equals(LibBaseUtil.getBaseConfig().versionName);
    }

    private void updateOptionalConfigVersion() {
        sp.edit().putString(PREF_PKG_OPTIONAL_VERSION, LibBaseUtil.getBaseConfig().versionName).apply();
    }
}
