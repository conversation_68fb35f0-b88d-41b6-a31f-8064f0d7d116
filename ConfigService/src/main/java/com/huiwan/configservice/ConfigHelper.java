package com.huiwan.configservice;

import android.os.Build;
import android.text.TextUtils;
import android.text.format.DateUtils;

import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.baseservice.BaseService;
import com.huiwan.baseservice.IHwService;
import com.huiwan.baseservice.ServiceObserver;
import com.huiwan.configservice.constentity.AgeLimit;
import com.huiwan.configservice.constentity.BgMusicInfo;
import com.huiwan.configservice.constentity.ConstV3Info;
import com.huiwan.configservice.constentity.DragonConfig;
import com.huiwan.configservice.constentity.DrawConfig;
import com.huiwan.configservice.constentity.FamilyConfig;
import com.huiwan.configservice.constentity.JKGameConfig;
import com.huiwan.configservice.constentity.JKHomeConfig;
import com.huiwan.configservice.constentity.MarryConfig;
import com.huiwan.configservice.constentity.propextra.HeadDecorExtra;
import com.huiwan.configservice.editionentity.AISceneGiftOrdersConfig;
import com.huiwan.configservice.editionentity.AreaConfig;
import com.huiwan.configservice.editionentity.AvatarConfig;
import com.huiwan.configservice.editionentity.AvatarPropConfig;
import com.huiwan.configservice.editionentity.ConstConfig;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.configservice.editionentity.GamesConfig;
import com.huiwan.configservice.editionentity.GiftsConfig;
import com.huiwan.configservice.editionentity.HomeConfig593;
import com.huiwan.configservice.editionentity.IVersion;
import com.huiwan.configservice.editionentity.InviteGamesConfig;
import com.huiwan.configservice.editionentity.MedalConfig;
import com.huiwan.configservice.editionentity.NationFlagConfig;
import com.huiwan.configservice.editionentity.PropItemConfig;
import com.huiwan.configservice.editionentity.UserTagConfig;
import com.huiwan.configservice.editionentity.VipConfig;
import com.huiwan.configservice.editionentity.VoiceConfigs;
import com.huiwan.configservice.editionentity.VoiceRoomConfig;
import com.huiwan.configservice.http.OrderApi;
import com.huiwan.configservice.http.UrlConfig;
import com.huiwan.configservice.international.regoin.RegionCommonConfig;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.model.RoomBg;
import com.huiwan.configservice.model.RoomLease;
import com.huiwan.configservice.model.StuffInfo;
import com.huiwan.configservice.model.WespyGoods;
import com.huiwan.configservice.model.voiceroom.CpRoomConfig;
import com.huiwan.configservice.model.voiceroom.FaceInfo;
import com.huiwan.configservice.model.voiceroom.TrickFaceInfo;
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo;
import com.huiwan.configservice.model.voiceroom.VoiceThemeInfo;
import com.huiwan.configservice.modelAbTest.AbTest;
import com.huiwan.constants.ConfigId;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUtil;
import com.huiwan.store.file.FileCacheName;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.LifeDataCallbackProxy;
import com.three.http.callback.Result;
import com.wepie.liblog.main.HLog;
import com.wepie.skynet.apm.Apm;

import org.json.JSONArray;
import org.json.JSONException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * date 2018/4/5
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class ConfigHelper extends BaseService implements IHwService {
    private static final int OPEN = 1;
    private static final String TAG = "ConfigHelper";

    // 版本类配置 刷新频率，10分钟 1 次
    private static final long MIN_UPDATE_INTERVAL = DateUtils.MINUTE_IN_MILLIS * 10;
    // 首次检测配置延迟时间
    private static final long FIRST_DELAY_CHECK = 5000;
    private final static ConfigHelper INSTANCE = new ConfigHelper();

    private final ConfigInfo<ConstV3Info> constV3Info = new ConfigInfo<>(ConstV3Info.class, true, UrlConfig.CONST_API_GET_CONST_CONFIG_DATA_V3, FileCacheName.CONST_V3_JSON);
    private final ConfigInfo<PropItemConfig> propConfig = new ConfigInfo<>(PropItemConfig.class, false, UrlConfig.CONST_API_ITEM_LIST, FileCacheName.REWARD_ITEM_LIST_JSON, new PropItemConfig.PropItemConfigAdapter());
    private final ConfigInfo<AreaConfig> areaConfig = new ConfigInfo<>(AreaConfig.class, true, UrlConfig.CONST_API_GET_AREA_CONFIG, FileCacheName.AREA_CONFIG);
    private final ConfigInfo<HomeConfig593> homeConfig = new ConfigInfo<>(HomeConfig593.class, true, UrlConfig.CONST_API_HOME_NEW, FileCacheName.HOME_CONFIG_593);
    private final ConfigInfo<GiftsConfig> giftsConfig = new ConfigInfo<>(GiftsConfig.class, false, UrlConfig.CONST_API_GET_GIFT_CONFIGS, FileCacheName.CONST_GIFT_CONFIG);
    private final ConfigInfo<GamesConfig> gamesConfig = new ConfigInfo<>(GamesConfig.class, false, UrlConfig.CONST_API_GET_GAME_CONFIGS, FileCacheName.CONST_GAME_CONFIG);
    private final ConfigInfo<UserTagConfig> userTagConfig = new ConfigInfo<>(UserTagConfig.class, false, UrlConfig.CONST_API_GET_TAG_CONFIGS, FileCacheName.CONST_USER_TAG_CONFIG);
    private final ConfigInfo<VoiceRoomConfig> voiceRoomConfig = new ConfigInfo<>(VoiceRoomConfig.class, false, UrlConfig.CONST_API_GET_VOICE_ROOM_CONFIGS, FileCacheName.CONST_VOICE_ROOM_CONFIG);
    private final ConfigInfo<VipConfig> vipConfig = new ConfigInfo<>(VipConfig.class, false, UrlConfig.CONST_API_VIP_CONFIG, FileCacheName.VIP_CONFIG_JSON);
    private final ConfigInfo<FamilyConfig> familyConfig = new ConfigInfo<>(FamilyConfig.class, false, UrlConfig.FAMILY_API_CONFIG, FileCacheName.FAMILY_CONFIG_JSON);
    private final ConfigInfo<VoiceConfigs> voiceSdkConfig = new ConfigInfo<>(VoiceConfigs.class, false, UrlConfig.CONST_API_VOICE_CONFIG, FileCacheName.VOICE_CONFIG);
    private final ConfigInfo<InviteGamesConfig> inviteGamesConfig = new ConfigInfo<>(InviteGamesConfig.class, false, UrlConfig.CONST_API_GET_INVITE_GAME_CONFIGS, FileCacheName.BOARD_GAMES_CONFIG);
    private final ConfigInfo<AvatarConfig> avatarConfig = new ConfigInfo<>(AvatarConfig.class, false, UrlConfig.CONST_API_GET_AVATAR_CONFIG, FileCacheName.AVATAR_CONFIG);
    private final ConfigInfo<RegionCommonConfig> regionCommonConfig = new ConfigInfo<>(RegionCommonConfig.class, false, UrlConfig.REGION_COMMON_CONFIG, FileCacheName.REGOIN_COMMON_CONFIG);
    private final ConfigInfo<AvatarPropConfig> avatarPropConfig = new ConfigInfo<>(AvatarPropConfig.class, false, UrlConfig.AVATAR_PROP_CONFIG, FileCacheName.AVATAR_PROP_SIMPLE_CONFIG);
    private final ConfigInfo<MedalConfig> medalConfig = new ConfigInfo<>(MedalConfig.class, false, UrlConfig.MEDAL_PROP_CONFIG, FileCacheName.MEDAL_CONFIG);
    private final ConfigInfo<NationFlagConfig> nationFlagConfig = new ConfigInfo<>(NationFlagConfig.class, false, UrlConfig.NATION_FLAG_CONFIG, FileCacheName.NATION_FLAG_CONFIG);
    private final ConfigInfo<ConstConfig> constConfig = new ConfigInfo<>(ConstConfig.class, false, UrlConfig.CONST_API_CONST_CONFIG, FileCacheName.CONST_CONFIG);
    private final ConfigInfo<AISceneGiftOrdersConfig> giftOrdersConfig = new ConfigInfo<>(AISceneGiftOrdersConfig.class, false, UrlConfig.CONST_API_AI_SCENE_GIFT_ORDERS, FileCacheName.AI_SCENE_GIFT_ORDERS_CONFIG);
    private final ConfigInfo<DragonConfig> dragonConfig = new ConfigInfo<>(DragonConfig.class, false, UrlConfig.CONST_API_BATTLE_CONFIG, FileCacheName.DRAGON_CONFIG);
    private final ConfigInfo<JKGameConfig> jkGameConfig = new ConfigInfo<>(JKGameConfig.class, false, UrlConfig.CONST_API_JK_GAME_CONFIG, FileCacheName.JK_GAME_CONFIG);
    private final ConfigInfo<JKHomeConfig> jkHomeConfig = new ConfigInfo<>(JKHomeConfig.class, false, UrlConfig.CONST_API_JK_HOME_CONFIG, FileCacheName.JK_HOME_CONFIG);
    private final List<StuffInfo> rpList = new ArrayList<>();

    // 出现特效播放异常的设备
    private final List<String> exceptionDeviceBrands = Arrays.asList("ANY-AN00", "SM-M115F", "SM-J600G", "ANY-AN00", "SM-A217F");

    private final ConfigUpdater updaterCn = new ConfigUpdater(Arrays.asList(
            constV3Info,
            propConfig,
            homeConfig,
            giftsConfig,
            gamesConfig,
            userTagConfig,
            voiceRoomConfig,
            voiceSdkConfig,
            vipConfig,
            familyConfig,
            inviteGamesConfig,
            avatarConfig
    ));
    private final ConfigUpdater updaterOs = new ConfigUpdater(Arrays.asList(
            jkHomeConfig,
            jkGameConfig,
            homeConfig,
            constV3Info,
            propConfig,
            giftsConfig,
            gamesConfig,
            userTagConfig,
            voiceRoomConfig,
            voiceSdkConfig,
            vipConfig,
            familyConfig,
            inviteGamesConfig,
//            avatarConfig,
            areaConfig,
            regionCommonConfig,
            avatarPropConfig,
            medalConfig,
            nationFlagConfig,
            dragonConfig,
            constConfig,
            giftOrdersConfig
    ));

    public final ConfigUpdaterK updater = new ConfigUpdaterK(updaterOs);

    private long lastUpdateTime = 0;

    private final ExecutorService service = ThreadUtil.newSingleThreadPool("ConfigHelper");

    private ConfigUpdater getConfigUpdate() {
        if (LibBaseUtil.isBuildChina()) {
            return updaterCn;
        } else if (LibBaseUtil.isBuildOverSeas()) {
            return updaterOs;
        } else {
            return updaterCn;
        }
    }

    private ConfigHelper() {
        // index 1 different from play game activity
        // use in home.
        homeConfig.addExtUrlParam("index", "1");
        propConfig.addExtUrlParam("width", String.valueOf(ScreenUtil.getScreenWidth()));
        initExceptionDevices();
        if (exceptionDeviceBrands.contains(Build.MODEL)) {
            ScreenUtil.getScreenHeight();//这里需要预先取一下高度，否则在dialog中直接使用高度有问题
            propConfig.addExtUrlParam("height", String.valueOf(ScreenUtil.getScreenWidth() * 16 / 9));
        } else {
            propConfig.addExtUrlParam("height", String.valueOf(ScreenUtil.getScreenHeight()));
        }
    }

    private void initExceptionDevices() {
        String localDevices = PrefUtil.getInstance().getString(PrefUtil.EXCEPTION_DEVICE_KEY, "");
        if (TextUtils.isEmpty(localDevices)) {
            return;
        }
        JSONArray array = null;
        try {
            array = new JSONArray(localDevices);
        } catch (Exception e) {
            //ignore
        }
        if (null == array) {
            return;
        }
        String device = "";
        for (int i = 0; i < array.length(); i++) {
            try {
                device = array.getString(i);
            } catch (JSONException e) {
                //ignore
            }
            exceptionDeviceBrands.add(device);
        }
    }

    public static ConfigHelper getInstance() {
        return INSTANCE;
    }

    public AbTest getAbtest() {
        return constV3Info.getConfig().abTest;
    }

    public List<String> getUserCharmIconList() {
        return constV3Info.getConfig().userCharmLevelIcons;
    }

    public Boolean rtCheck() {
        return constV3Info.getConfig().rtCheck;
    }

    public void registerObserver(ServiceObserver<IVersion> observer) {
        getConfigUpdate().register(observer);
    }

    public void unregisterObserver(ServiceObserver<IVersion> observer) {
        getConfigUpdate().unregister(observer);
    }


    public interface UpdateCallback {
        void onSuccess();

        void onFailed(String msg);
    }

    public boolean isLocalAvailable() {
        boolean isRegionLegal = true;
        String homeRegion = homeConfig.getConfig().getRegion();
        String configRegion = GlobalConfigManager.getInstance().getRegion();
        if (homeConfig.required) {
            isRegionLegal = configRegion.equalsIgnoreCase(homeRegion);
        }
        boolean isAvailable = homeConfig.getConfig().isAvailable();
        HLog.d(TAG, HLog.USR, "isLocalAvailable! homeRegion={}, configRegion={}, isAvailable={}", homeRegion, configRegion, isAvailable);
        return isRegionLegal && isAvailable;
    }

    public void loadWithUpdateRequiredConfig(final UpdateCallback updateCallback) {
        updateRequiredConfig(updateCallback);
    }

    public void updateRequiredConfig(final UpdateCallback updateCallback) {
        getConfigUpdate().updateRequiredConfig(updateCallback);
    }

    public void checkAllConfig() {
        service.execute(this::updateConfigWithVersion);
    }

    private void updateConfigWithVersion() {
        if (updater.getNeedWaitLocalLoad()) {
            // 高低端机一般都在 2 s 以内完成配置加载。
            ThreadUtil.runOnUiThreadDelay(DateUtils.SECOND_IN_MILLIS * 2, this::checkAllConfig);
            return;
        }
        boolean configCheckTimeout = System.currentTimeMillis() - lastUpdateTime > MIN_UPDATE_INTERVAL;
        boolean configNotAvailable = !checkConfigAvailable();

        Apm.recordTimePoint("updateConfigWithVersion!" + configCheckTimeout + ", " + configNotAvailable);
        if (configCheckTimeout || configNotAvailable) {
            updateAllEditions();
        }
    }

    public boolean checkConfigAvailable() {
        return getConfigUpdate().allConfigAvailable();
    }

    public void forceUpdatePropConfigWithVersion(final UpdateCallback callback) {
        getConfigUpdate().updateConfig(PropItemConfig.class, false, callback);
    }

    public void forceUpdateGiftConfigWithVersion(final UpdateCallback callback) {
        getConfigUpdate().updateConfig(GiftsConfig.class, true, callback);
    }

    public void forceUpdateAISceneGiftOrdersConfigWithVersion(final UpdateCallback callback) {
        getConfigUpdate().updateConfig(AISceneGiftOrdersConfig.class, true, callback);
    }

    public void forceUpdateAllEditions(UpdateCallback callback) {
        getConfigUpdate().updateAllConfig(true, new TimeUpdateCallback(callback));
    }

    private void updateAllEditions() {
        getConfigUpdate().updateAllConfig(false, new TimeUpdateCallback(null));
    }

    public void updateEditionsForLogin() {
        new ConfigUpdater(Arrays.asList(homeConfig, gamesConfig, voiceRoomConfig, giftOrdersConfig)).updateAllConfig(true, null);
    }

    public void loadRegionCommonConfig(ConfigHelper.UpdateCallback callback) {
        if (regionCommonConfig.isNotInit()) {
            getConfigUpdate().updateConfig(RegionCommonConfig.class, false, callback);
        } else if (callback != null) {
            callback.onSuccess();
        }
    }

    private class TimeUpdateCallback implements UpdateCallback {
        UpdateCallback ori;

        public TimeUpdateCallback(UpdateCallback ori) {
            this.ori = ori;
        }

        @Override
        public void onSuccess() {
            lastUpdateTime = System.currentTimeMillis();
            if (ori != null) {
                ori.onSuccess();
            }
        }

        @Override
        public void onFailed(String msg) {
            if (ori != null) {
                ori.onFailed(msg);
            }
        }

        @Override
        public String toString() {
            return String.valueOf(ori);
        }
    }

    public Map<Integer, String> getConfigJsonMap(List<Integer> configIds) {
        Map<Integer, String> ret = new HashMap<>();
        for (int i : configIds) {
            ret.put(i, getConfigJson(i));
        }
        return ret;
    }

    private String getConfigJson(int id) {
        return JsonUtil.toJson(configIdMap(id));
    }

    private Object configIdMap(int id) {
        switch (id) {
            case ConfigId.CONST_V3_INFO_ID:
                return constV3Info.getConfig();
            case ConfigId.PROP_ITEM_CONFIG_ID:
                return propConfig.getConfig();
            case ConfigId.GIFTS_CONFIG_ID:
                return giftsConfig.getConfig();
            case ConfigId.GAMES_CONFIG_ID:
                return gamesConfig.getConfig();
            case ConfigId.USER_TAG_CONFIG_ID:
                return userTagConfig.getConfig();
            case ConfigId.VOICE_ROOM_CONFIG_ID:
                return voiceSdkConfig.getConfig();
            case ConfigId.VIP_CONFIG_ID:
                return vipConfig.getConfig();
            case ConfigId.FAMILY_CONFIG_ID:
                return familyConfig.getConfig();
            case ConfigId.HOME_CONFIG593_ID:
                return homeConfig.getConfig();
            default:
                return null;
        }
    }

    public void resetHomeVersionAndSave() {
        homeConfig.getConfig().resetVersion();
        FileUtil.writeEntityAsync(homeConfig.cacheFileName, homeConfig.getConfig());
    }


    /**
     * 服务端保证其 version_num 不会减小，不考虑整形溢出
     */
    public void updateGameConfig(@Nullable final UpdateCallback callback) {
        getConfigUpdate().updateConfig(GamesConfig.class, false, callback);
    }

    public boolean marketChecking() {
        return constV3Info.getConfig().marketChecking == OPEN;
    }

    public ArrayList<WespyGoods> getGoods() {
        return constV3Info.getConfig().goodsList;
    }

    public ArrayList<RoomBg> getRoomBackgrounds() {
        return constV3Info.getConfig().roomBgArrayList;
    }

    public ArrayList<RoomLease> getRoomProLongLease() {
        return constV3Info.getConfig().renewLeaseList;
    }


    public ArrayList<RoomLease> getRoomCreateLease() {
        return constV3Info.getConfig().startLeaseList;
    }

    public ArrayList<RoomLease> getWerewolfRoomCreateLease() {
        return constV3Info.getConfig().werewolfVipRoomCreateLeaseList;
    }

    public ArrayList<RoomLease> getWerewolfRoomProlongLease() {
        return constV3Info.getConfig().werewolfLeaseList;
    }

    public ArrayList<Integer> getAddSelfFriendPriceArray() {
        return constV3Info.getConfig().addFriendPriceList;
    }

    public boolean useKcp() {
        return constV3Info.getConfig().useKcp;
    }

    public int[] getCarePlantResSection() {
        return constV3Info.getConfig().careResSection;
    }

    public int getMinGoldCost() {
        return constV3Info.getConfig().minCoinToEnterGame;
    }

    public int getDrawGuessChangeWordCost(int times) {
        if (times >= 0 && times < constV3Info.getConfig().changeWordCostList.size()) {
            return constV3Info.getConfig().changeWordCostList.get(times);
        }
        return constV3Info.getConfig().changeWordMaxCost;
    }

    public int getDrawGuessDelayCost() {
        return constV3Info.getConfig().drawGuessDelayCost;
    }

    public int getDrawPathSyncInterval() {
        return constV3Info.getConfig().syncDrawFrequency;
    }

    public ArrayList<BgMusicInfo> getRoomBgMusicInfoList() {
        return constV3Info.getConfig().marryRoomMusicBgList;
    }

    public void getRpList(String currency, LifeDataCallback<List<StuffInfo>> callback) {
        OrderApi.getRedPacketList(currency, new LifeDataCallbackProxy<>(callback) {
            @Override
            public void onSuccess(Result<List<StuffInfo>> result) {
                rpList.clear();
                rpList.addAll(result.data);
                super.onSuccess(result);
            }
        });
    }

    @Nullable
    public BgMusicInfo getMusicInfoByUrl(String musicUrl) {
        for (BgMusicInfo info : constV3Info.getConfig().marryRoomMusicBgList) {
            if (info.url.equals(musicUrl)) return info;
        }
        return null;
    }

    @Nullable
    public BgMusicInfo getMusicInfoById(int id) {
        for (BgMusicInfo info : constV3Info.getConfig().marryRoomMusicBgList) {
            if (info.bg_music_id == id) return info;
        }
        return null;
    }

    public ArrayList<StuffInfo> getStuffInfoList() {
        return constV3Info.getConfig().stuffInfoList;
    }

    @Nullable
    public StuffInfo getStuffInfoById(int id) {
        for (StuffInfo info : rpList) {
            if (info.stuff_id == id) {
                return info;
            }
        }
        for (StuffInfo info : constV3Info.getConfig().stuffInfoList) {
            if (info.stuff_id == id) {
                return info;
            }
        }
        return null;
    }

    public int getFireworksKeepTime(int fireworksNum) {
        String key = Integer.toString(fireworksNum);
        if (constV3Info.getConfig().fireworksKeepTime.containsKey(key)) {
            return constV3Info.getConfig().fireworksKeepTime.get(key);
        }
        return 0;
    }

    public ArrayList<String> getHotProvince() {
        return constV3Info.getConfig().hotProvinceList;
    }

    public String getTaskShareDec() {
        return constV3Info.getConfig().taskShareDesc;
    }

    public String getMyShareUrl() {
        return constV3Info.getConfig().myShareUrl;
    }

    public String getMyShareDesc() {
        return constV3Info.getConfig().myShareDesc;
    }

    public String getMyShareTitle() {
        return constV3Info.getConfig().myShareTitle;
    }

    public String getGroupShareTitle() {
        return constV3Info.getConfig().myShareWithSource.shareGroup.myShareTitle;
    }

    public String getGroupShareUrl() {
        return constV3Info.getConfig().myShareWithSource.shareGroup.myShareUrl;
    }

    public String getGroupShareDesc() {
        return constV3Info.getConfig().myShareWithSource.shareGroup.myShareDesc;
    }

    public String getShareIconUrl() {
        return constV3Info.getConfig().shareIconUrl;
    }

    public String getRoomShareTitle() {
        return constV3Info.getConfig().roomShareTitle;
    }

    public String getHwRoomShareTitle() {
        return constV3Info.getConfig().hwRoomShareTitle;
    }

    public String getRoomShareDesc() {
        return constV3Info.getConfig().roomShareDesc;
    }

    public int getDiscoverRecommendReopenInterval() {
        return constV3Info.getConfig().discoverRecommendReopen;
    }

    public String getChargeTip() {
        return constV3Info.getConfig().chargePageTips;
    }

    public String getChipCoinChargeTip() {
        return constV3Info.getConfig().chipCoinChargeTips;
    }

    public int getBindPhoneCoinReward() {
        return constV3Info.getConfig().bindPhoneRewardCoin;
    }


    public AgeLimit getAgeLimit() {
        return constV3Info.getConfig().ageLimit;
    }

    public String getCharmImgUrl() {
        return constV3Info.getConfig().charmHelp.imgUrl;
    }

    public List<TrickFaceInfo> getVisibleTrickFaces() {
        List<TrickFaceInfo> visibleFaces = new ArrayList<>();
        for (TrickFaceInfo trickFaceInfo : voiceRoomConfig.getConfig().getTricks()) {
            if (!trickFaceInfo.isHide()) {
                visibleFaces.add(trickFaceInfo);
            }
        }
        return visibleFaces;
    }

    public List<FaceInfo> getFaces(long showType) {
        return voiceRoomConfig.getConfig().getFaces(showType);
    }

    public VoiceRoomConfig getVoiceRoomConfig() {
        return voiceRoomConfig.getConfig();
    }

    public LiveData<VoiceRoomConfig> getVoiceRoomConfigLiveData() {
        return voiceRoomConfig.getLiveData();
    }

    public LiveData<ConstV3Info> getConstV3InfoLiveData() {
        return constV3Info.getLiveData();
    }

    public VoiceConfigs getVoiceConfigs() {
        return voiceSdkConfig.getConfig();
    }

    public List<VoiceThemeInfo> getVoiceRoomThemes() {
        return voiceRoomConfig.getConfig().getThemes();
    }

    public List<VoiceThemeInfo> getFamilyRoomThemes() {
        return voiceRoomConfig.getConfig().getFamilyThemes();
    }

    public List<VoiceLabelInfo> getVoiceRoomLabels() {
        return voiceRoomConfig.getConfig().getLabels();
    }

    public VoiceLabelInfo getVoiceLabelById(int labelId) {
        for (VoiceLabelInfo labelInfo : voiceRoomConfig.getConfig().getLabels()) {
            if (labelInfo.getId() == labelId) return labelInfo;
        }
        return null;
    }

    @Nullable
    public TrickFaceInfo getTrickById(int id) {
        return voiceRoomConfig.getConfig().getTrickById(id);
    }

    @Nullable
    public FaceInfo getFaceById(int id) {
        return voiceRoomConfig.getConfig().getFaceById(id);
    }

    @Nullable
    public VoiceThemeInfo getRoomThemeById(int id) {
        return voiceRoomConfig.getConfig().getThemeInfoById(id);
    }

    public VoiceThemeInfo getFamilyRoomThemeById(int themeId) {
        return voiceRoomConfig.getConfig().getFamilyThemeInfoById(themeId);
    }

    @Nullable
    public VoiceLabelInfo getLabelById(int id) {
        return voiceRoomConfig.getConfig().getLabelById(id);
    }

    public CpRoomConfig getCpRoomConfig() {
        return voiceRoomConfig.getConfig().getCpRoomConfig();
    }

    @SuppressWarnings("unused")
    public boolean testDevice() {
        return constV3Info.getConfig().isTestDevice;
    }

    public boolean superAdminDevice() {
        return constV3Info.getConfig().isSuperAdminDevice;
    }

    public boolean isExternalManager() {
        return constV3Info.getConfig().isExternalManager;
    }

    public ArrayList<Integer> getCharmLevel() {
        return constV3Info.getConfig().charmLevel;
    }

    public String getSendGiftHelpUrl() {
        return constV3Info.getConfig().sendGiftHelp;
    }

    public boolean isTopicFirst() {
        return constV3Info.getConfig().topicFirst;
    }

    public boolean showGiftRewardCoin(int coin) {
        return coin >= constV3Info.getConfig().sendGiftReturnTriggerCoin;
    }

    public String getGameRuleUrl() {
        return constV3Info.getConfig().gameRule;
    }

    public String getUserProtocolUrl() {
        return constV3Info.getConfig().userProtocol;
    }

    public String getSignSingerProtocolUrl() {
        return constV3Info.getConfig().signSingerProtocol;
    }

    public String getUserPrivacyUrl() {
        return constV3Info.getConfig().userPrivacyProtocol;
    }

    public String getBroadcastTitle() {
        return constV3Info.getConfig().broadcastTitleText;
    }

    public String getVowBroadcastShareUrl() {
        return constV3Info.getConfig().vowBroadcastHelpUrl;
    }

    public boolean voiceMatchOpen() {
        return constV3Info.getConfig().voiceMatchConfig.open == OPEN;
    }

    public String voiceMatchHelpUrl() {
        return constV3Info.getConfig().voiceMatchConfig.help_url;
    }

    public int voiceMatchToastTime() {
        return constV3Info.getConfig().voiceMatchConfig.show_toast_time;
    }

    public boolean thirdFeedBackOpen() {
        return constV3Info.getConfig().thirdFeedbackSdkOpen;
    }

    public String getBroadcastAppointedTip() {
        return constV3Info.getConfig().broadcastAppointTip;
    }

    public String getBroadcastRefreshTip() {
        return constV3Info.getConfig().broadcastRefreshTip;
    }

    public boolean msgRecallClose() {
        return !constV3Info.getConfig().messageRecallOpen;
    }

    public String marryRefreshTip() {
        return constV3Info.getConfig().marryRefreshTip;
    }

    public String careHelpUrl() {
        return constV3Info.getConfig().careHelpUrl;
    }

    public List<ConstV3Info.ReportReason> getReportReason() {
        return constV3Info.getConfig().drawReportReason;
    }

    public int maxVoiceRoomId() {
        return constV3Info.getConfig().max_room_id;
    }

    public String getAppShareUrl() {
        return constV3Info.getConfig().app_share_url;
    }

    public String getAppSlogan() {
        return constV3Info.getConfig().app_slogan;
    }

    public int getUserHomeCarePeopleNum() {
        return constV3Info.getConfig().userHomeCarePeopleNum;
    }

    public GamesConfig getGameConfig() {
        return gamesConfig.getConfig();
    }

    public LiveData<GamesConfig> getGameConfigLiveData() {
        return gamesConfig.getLiveData();
    }

    public GameConfig getGameConfig(int gameType) {
        return gamesConfig.getConfig().getGameConfig(gameType);
    }

    public List<GameConfig.MatchInfo> getGameMatchInfo(int gameType) {
        return gamesConfig.getConfig().getMatchInfo(gameType);
    }

    public String getGameMatchResUrlAn(int gameType) {
        return gamesConfig.getConfig().getGameConfig(gameType).getMatchResUrlAn();
    }

    /**
     * 神策打点，调用ShenceSource.getGameTypeSource()
     */
    public String getGameName(int gameType) {
        return gamesConfig.getConfig().getGameName(gameType);
    }

    /**
     * 短游戏名字，显示游戏状态
     */
    public String getShortGameName(int gameType) {
        return gamesConfig.getConfig().getShortGameName(gameType);
    }

    public GiftsConfig getGiftConfig() {
        return giftsConfig.getConfig();
    }

    public UserTagConfig getUserTagConfig() {
        return userTagConfig.getConfig();
    }

    public InviteGamesConfig getInviteGamesConfig() {
        return inviteGamesConfig.getConfig();
    }

    public List<RoomLease> getLandlordCreateLease() {
        return new ArrayList<>();
    }


    public String getNestHelpUrl() {
        return constV3Info.getConfig().nestHelpUrl;
    }

    public String getChurchHelpUrl() {
        return constV3Info.getConfig().churchHelpUrl;
    }

    public boolean isFixRoomId(int rid) {
        if (constV3Info.getConfig().fixRoomId.length < 2) return false;
        return rid >= constV3Info.getConfig().fixRoomId[0] && rid <= constV3Info.getConfig().fixRoomId[1];
    }

    @Nullable
    public HeadDecorExtra getHeadDecorationById(int propId) {
        PropItem propItem = propConfig.getConfig().getPropItem(propId);
        if (propItem != null) {
            return propItem.getHeadDecorExtra();
        }
        return null;
    }

    public String getFixRoomLevelUrl() {
        return constV3Info.getConfig().roomLevelUrl;
    }

    public String getFixRoomRuleUrl() {
        return constV3Info.getConfig().roomRuleUrl;
    }

    public boolean showBattlePass() {
        return constV3Info.getConfig().battlePassOpen == 1;
    }

    public int getProposeRequestCare() {
        return constV3Info.getConfig().proposeRequireCare;
    }

    public String getNornalSpyGameRule() {
        return constV3Info.getConfig().wespyGameRuleUrl;
    }

    public String getBlackSpyGameRule() {
        return constV3Info.getConfig().blkGameRuleUrl;
    }

    public PropItemConfig getPropConfig() {
        return propConfig.getConfig();
    }

    public boolean showInviteCode() {
        return constV3Info.getConfig().showInviteCode == 1;
    }

    public MarryConfig getMarryConfig() {
        return constV3Info.getConfig().marryConfig;
    }

    public DrawConfig getDrawConfig() {
        return constV3Info.getConfig().drawConfig;
    }

    public boolean isBlockVirtualApp() {
        return constV3Info.getConfig().blockDevice > 0;
    }

    public boolean isBlockEmulatorNew() {
        return constV3Info.getConfig().blockDevice == 2;
    }

    public String getRoomHelperPic() {
        return constV3Info.getConfig().gameRoomHelpUrl;
    }

    public String getVoiceRoomReportUrl(int uid) {
        return constV3Info.getConfig().superviseUrlInfo.voiceRoomSupervise + "?uid=" + uid;
    }

    public String getEvMatchReportUrl(int uid) {
        return constV3Info.getConfig().superviseUrlInfo.evMatchSupervise + "?uid=" + uid;
    }

    public VipConfig getVipConfig() {
        return vipConfig.getConfig();
    }

    public int getDefaultTopicId() {//chatting分支用到这个字段
        return constV3Info.getConfig().defaultTopicId;
    }

    public FamilyConfig getFamilyConfig() {
        return familyConfig.getConfig();
    }

    public HomeConfig593 getHomeConfig593() {
        return homeConfig.getConfig();
    }

    public LiveData<HomeConfig593> getHomeConfigLiveData() {
        return homeConfig.getLiveData();
    }

    public LiveData<JKHomeConfig> getJkHomeConfigLiveData() {
        return jkHomeConfig.getLiveData();
    }

    public ConstV3Info getConstV3Info() {
        return constV3Info.getConfig();
    }

    public AvatarConfig getAvatarConfig() {
        return avatarConfig.getConfig();
    }

    public ConfigInfo<AreaConfig> getAreaConfig() {
        return areaConfig;
    }

    public RegionCommonConfig getRegionCommonConfig() {
        return regionCommonConfig.getConfig();
    }

    public ConfigInfo<AvatarPropConfig> getAvatarPropConfig() {
        return avatarPropConfig;
    }

    public ConstConfig getConstConfig() {
        return constConfig.getConfig();
    }

    public String getGuardHelpText() {
        return voiceRoomConfig.getConfig().getAdvancedRoomConfig().getGuardianHelp();
    }

    public AISceneGiftOrdersConfig getGiftOrdersConfig() {
        return giftOrdersConfig.getConfig();
    }

    public MedalConfig getMedalConfig() {
        return medalConfig.getConfig();
    }

    public JKGameConfig getJKGameConfig() {
        return jkGameConfig.getConfig();
    }

    public JKHomeConfig getJKHomeConfig() {
        return jkHomeConfig.getConfig();
    }

    @Override
    public void logoutClear() {

    }

    @Override
    public void mainFinishClear() {
        getConfigUpdate().mainFinishClear();
    }

    public NationFlagConfig getNationFlagConfig() {
        return nationFlagConfig.getConfig();
    }

    public DragonConfig getDragonConfig() {
        return dragonConfig.getConfig();
    }

    public List<String> getWhiteHosts() {
        return constV3Info.getConfig().whiteHosts;
    }

    public String getCallbackUrlsFullScreen() {
        return constV3Info.getConfig().callbackUrls.fullScreen;
    }

    public String getCallbackUrlsHalfScreen() {
        return constV3Info.getConfig().callbackUrls.halfScreen;
    }

    public ConfigInfo<? extends IVersion> getConfig(Class<? extends IVersion> cls) {
        for (ConfigInfo<? extends IVersion> info : getConfigUpdate().allConfig) {
            IVersion config = info.getConfig();
            if (config.getClass() == cls) {
                return info;
            }
        }
        return null;
    }

    public VoiceRoomConfig.BingoGame getBingoGame() {
        return getVoiceRoomConfig().getBingoGame();
    }
}
