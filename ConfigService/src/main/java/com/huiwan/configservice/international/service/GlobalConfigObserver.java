package com.huiwan.configservice.international.service;

import com.huiwan.base.common.GlobalConfig;

public abstract class GlobalConfigObserver {

    // update config cache success
    public void updateCacheSuccess() {

    }

    public void forceAllUpdate(boolean success, String msg) {

    }

    public void requestResultBack(boolean isSuccess, GlobalConfig globalConfig, int code, String msg) {

    }
}
