package com.huiwan.configservice.international.service;

import android.app.Activity;
import android.os.Process;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.common.GlobalCommon;
import com.huiwan.base.common.GlobalConfig;
import com.huiwan.base.common.GlobalConfig.ConfigItem;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.R;
import com.huiwan.configservice.http.UrlConfig;
import com.huiwan.configservice.international.regoin.RegionCancelEvent;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.huiwan.lib.api.plugins.LoginSApi;
import com.huiwan.store.PrefUtil;
import com.huiwan.store.SdkBaseStore;
import com.three.http.alternatedomain.AlternateDomainManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.three.http.constant.Config;
import com.three.http.core.HttpBuilder;
import com.three.http.core.HttpUtil;
import com.three.http.dns.AliDns;
import com.wejoy.weplay.ex.GlobalLife;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.main.HLog;

import org.greenrobot.eventbus.EventBus;

import java.io.StringReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

public class GlobalConfigManager {
    private static final String TAG = "GlobalConfig";


    // link:https://yapi.17zjh.com/project/714/interface/api/20990
    public static final String HTTP_API = "http_api";
    public static final String werewolf_server = "werewolf_server";
    public static final String chat_server = "chat_server";
    public static final String connector_server = "connector_server";
    private GlobalConfig config;
    private String configStr;
    private volatile boolean isReady = false;
    // update cache only once time
    private volatile boolean isUpdateCaching = false;
    private final AtomicInteger requestCount = new AtomicInteger(-1);
    private final AtomicInteger failedTimes = new AtomicInteger(0);
    private volatile IGlobalConfigStrategy strategy;
    CopyOnWriteArrayList<GlobalConfigObserver> observers = new CopyOnWriteArrayList<>();


    private GlobalConfigManager() {
    }

    public static GlobalConfigManager getInstance() {
        return Holder.instance;
    }

    public void init(IGlobalConfigStrategy strategy) {
        this.strategy = strategy;
        HLog.d(TAG, HLog.USR, "init! globalDomain=" + strategy.getDomain());
//        loadLocalConfig();
//        request("");
    }

    public void loadLocalConfig() {
        GlobalConfig configItemMap = load();
        updateConfigCache(configItemMap, false);
    }

    public Map<String, List<String>> getCDNConfig() {
        if (null == config) {
            loadLocalConfig();
            if (null == config) {
                return null;
            }
        }
        return config.cdnMap;
    }

    public Map<String, String> getTwitterConfig() {
        if (null == config) {
            loadLocalConfig();
            if (null == config) {
                return null;
            }
        }
        return config.twitterConfig;
    }

    @NonNull
    public GlobalConfig.VoiceKInfo getVoiceConfig() {
        if (null == config) {
            loadLocalConfig();
            if (null == config) {
                return new GlobalConfig.VoiceKInfo();
            }
        }
        return config.getVoiceConfig();
    }

    @Nullable
    public ConfigItem getConfig(String serverName) {
        if (null == config) {
            loadLocalConfig();
            if (null == config) {
                return null;
            }
        }
        return config.getItem(serverName);
    }

    private void applyConfigs(GlobalConfig globalConfig) {
        if (this.config == null) {
            this.config = globalConfig;
        } else {
            this.config.update(globalConfig);
        }
        configStr = null;
        ConfigItem item = getConfig(HTTP_API);
        if (item != null && item.configItems != null) {
            HLog.d(AlternateDomainManager.TAG, "update from applyConfigs", globalConfig.configMap);
            AlternateDomainManager.updateDomains(getRegion(), item.configItems);
        }
    }


    public void request(String confirmRegion) {
        failedTimes.set(0);
        if (strategy != null) {
            strategy.request(confirmRegion);
        }
        doRequest(confirmRegion);
    }

    private void doRequest(String confirmRegion) {
        String domain = "";
        if (strategy != null) {
            domain = strategy.getDomain();
        }
        HLog.d(TAG, HLog.USR, "request, confirmRegion=" + confirmRegion + " domain=" + domain);
        HttpBuilder builder = HttpUtil.newBuilder()
                .domain(domain)
                .uri(UrlConfig.GLOBAL_PATH)
                .param(addParams(confirmRegion))
                .retryCount(1)
                .useOriginalResponse(false)
                .setDisableQuic(true);
        if (requestCount.incrementAndGet() > 1) {
            builder.useIndependentClient();
        }
        builder.build().postNow(callback);
    }

    private GlobalConfig load() {
        String configs = PrefUtil.getInstance().getString(PrefUtil.KET_GLOBAL_SERVER_CONFIGS, "");
        GlobalConfig globalConfig = null;
        if (!TextUtil.isEmpty(configs)) {
            globalConfig = GlobalConfig.parse(configs);
        }
        HLog.d(TAG, HLog.USR, "load! processID={} configs={} {}", Process.myPid(), configs, globalConfig != null, new RuntimeException("load"));
        return globalConfig;
    }

    public boolean saveToLocal(String configs) {
        boolean flag = false;
        if (!TextUtil.isEmpty(configs)) {
            save(configs);
            flag = true;
        }
        return flag;
    }

    private void save(String configs) {
        HLog.d(TAG, HLog.USR, "saveToLocal!");
        PrefUtil.getInstance().setString(PrefUtil.KET_GLOBAL_SERVER_CONFIGS, configs);
    }

    private Map<String, String> addParams(String confirmRegion) {
        HashMap<String, String> params = new HashMap<>();
        String region = PrefUtil.getInstance().getString(PrefUtil.KET_GLOBAL_SERVER_REGION, "");
        if (!TextUtil.isEmpty(region)) {
            params.put(GlobalConfig.KEY_REGION, region);
        }
        //如果用户确认了confirmRegion 那么取全服配置的时候优先使用用户确认的region服务器
        if (!TextUtil.isEmpty(confirmRegion)) {
            params.put(GlobalConfig.KEY_REGION, confirmRegion);
        }
        String countryIso = ApiService.of(HwApi.class).getSimCountry();
        params.put("sim_area", countryIso);
        params.put("confirm_region", confirmRegion);
        HLog.d(TAG, HLog.USR, "addParams " + params);
        return params;
    }


    public boolean isReady() {
        return isReady;
    }

    LifeDataCallback<GlobalConfig> callback = new LifeDataCallback<>(GlobalLife.INSTANCE) {
        @Override
        public void onSuccess(Result<GlobalConfig> result) {
            HLog.d(TAG, HLog.USR, "onSuccess! code=" + result.code + ",  msg=" + result.msg);
            updateConfig(result.data, false);
            notifyRequestResult(true, result.data, 0, "success");
        }

        @Override
        public void onFail(int code, String msg) {
            HLog.d(TAG, HLog.USR, "onFail! code={}, msg={}", code, msg);
            if (strategy != null && strategy.filterRequestFail(code, msg)) {
                doRequest("");
            } else {
                notifyRequestResult(false, null, code, msg);
            }
        }

        @Override
        public Result<GlobalConfig> parseResponse(String response) {
            return parseConfigResponse(response);
        }
    };

    private void notifyCacheUpdate() {
        HLog.d(TAG, HLog.USR, "notifyGlobalConfigUpdate! observers size=" + observers.size());
        LibBaseUtil.updateRegion(getRegion());
        for (GlobalConfigObserver observer : observers) {
            observer.updateCacheSuccess();
        }
    }

    private void notifyRequestResult(boolean isSuccess, GlobalConfig globalConfig, int code, String msg) {
        HLog.d(TAG, HLog.USR, "notifyRequestResult! observers size=" + observers.size());
        for (GlobalConfigObserver observer : observers) {
            observer.requestResultBack(isSuccess, globalConfig, code, msg);
        }
    }

    public boolean updateConfig(GlobalConfig globalConfig, boolean force) {
        boolean dbUpload = false, cacheUpload = false;
        if (isValid(globalConfig)) {
            dbUpload = saveToLocal(globalConfig.toJson());
            cacheUpload = updateConfigCache(globalConfig, force);
        }
        return dbUpload && cacheUpload;
    }

    private boolean isValid(GlobalConfig globalConfig) {
        return null != globalConfig && null != globalConfig.configMap && !globalConfig.configMap.isEmpty();
    }

    private synchronized boolean updateConfigCache(GlobalConfig globalConfig, boolean forceUpdate) {
        boolean flag = false;
        do {
            if (isUpdateCaching) {
                break;
            }
            isUpdateCaching = true;
            if (null == globalConfig) {
                break;
            }
            if (!forceUpdate && isReady) {
                break;
            }
            applyConfigs(globalConfig);
            updateRegion();
            isReady = true;
            notifyCacheUpdate();
            flag = true;
        } while (false);
        HLog.d(TAG, HLog.USR, "updateConfigCache! flag={},region={}", flag, getRegion());
        isUpdateCaching = false;
        return flag;
    }

    private void updateRegion() {
        boolean isLogin = SdkBaseStore.getUid() > 0;
        String currentRegion = getRegion();
        AliNetLogUtil.updateRegion(currentRegion);
        AliDns.Companion.preloadLocale(currentRegion);
        ConfigItem item = getConfig(HTTP_API);
        if (item != null && item.configItems != null) {
            HLog.d(TAG, "update from updateRegion");
            AlternateDomainManager.updateDomains(currentRegion, item.configItems);
        }
        LibBaseUtil.currentRegion = currentRegion;
        HLog.d(TAG, HLog.USR, "updateRegion, confirmRegion=" + currentRegion + ", isLogin=" + isLogin);
    }

    public Result<GlobalConfig> parseConfigResponse(String response) {
        HLog.d(TAG, HLog.USR, "parseConfigResponse! response=" + response);

        Result<GlobalConfig> result = new Result<>();
        try {
            StringReader stringReader = new StringReader(response);
            JsonReader reader = new JsonReader(stringReader);
            reader.beginObject();
            while (reader.hasNext()) {
                String name = reader.nextName();
                if (reader.peek() == JsonToken.NULL) {
                    reader.skipValue();
                    continue;
                }
                if (Config.json_code_key.equals(name)) {
                    result.code = reader.nextInt();
                } else if (Config.json_message_key.equals(name)) {
                    result.msg = reader.nextString();
                } else if (Config.json_data_key.equals(name)) {
                    result.data = GlobalConfig.parse(reader);
                } else {
                    reader.skipValue();
                }
            }
            reader.endObject();
        } catch (Exception e) {
            HLog.e(TAG, HLog.USR, "parseConfigResponse error! msg=" + e);
        }
        return result;
    }

    public void handleConfigException(String data, GlobalConfigObserver observer) {
        if (TextUtil.isEmpty(data)) {
            forceUpdateCallBack(observer, false, "data is empty!");
            return;
        }
        HLog.d(TAG, HLog.USR, "handleConfigException");
        try {
            StringReader stringReader = new StringReader(data);
            JsonReader reader = new JsonReader(stringReader);
            GlobalConfig globalConfig = null;
            String redirectRegion = "";
            reader.beginObject();
            while (reader.hasNext()) {
                String name = reader.nextName();
                if (reader.peek() == JsonToken.NULL || !Config.json_data_key.equals(name)) {
                    reader.skipValue();
                    continue;
                }
                reader.beginObject();
                while (reader.hasNext()) {
                    name = reader.nextName();
                    if (reader.peek() == JsonToken.NULL) {
                        reader.skipValue();
                        continue;
                    }
                    if (Config.redirect_region_key.equals(name)) {
                        redirectRegion = reader.nextString();
                    } else if (Config.region_server_key.equals(name)) {
                        globalConfig = GlobalConfig.parse(reader);
                    } else {
                        reader.skipValue();
                    }
                }
                reader.endObject();
            }
            reader.endObject();
            handleSwitchRegion(data, redirectRegion, globalConfig, observer);
        } catch (Exception e) {
            forceUpdateCallBack(observer, false, "error! error msg is " + e);
        }
    }

    private void handleSwitchRegion(String data, String region, GlobalConfig globalConfig, GlobalConfigObserver observer) {
        HLog.d(TAG, HLog.USR, "handleSwitchRegion {}", region);
        if (TextUtil.isEmpty(region)) {
            // 不确定区服，需要用户选择
            Activity activity = ActivityTaskManager.getInstance().getTopActivity();
            ApiService.of(LoginSApi.class).showSelectRegionDialog(activity, data, selectRegion -> {
                HLog.d(TAG, HLog.USR, "updateUserSelectRegion {}", selectRegion);
                if (!TextUtil.isEmpty(selectRegion)) {
                    updateUserSelectRegion(selectRegion, observer);
                } else {
                    //如果选择区服为空,说明用户取消了
                    EventBus.getDefault().post(new RegionCancelEvent());
                }
                return null;
            });
        } else {
            updateGlobalInfoFromServer(globalConfig, observer);
        }
    }

    private void updateUserSelectRegion(String selectRegion, GlobalConfigObserver observer) {
        addObserver(new GlobalConfigObserver() {
            @Override
            public void requestResultBack(boolean isSuccess, GlobalConfig globalConfig, int code, String msg) {
                removeObserver(this);
                updateConfig(globalConfig, true);
                forceUpdateConfigs(observer);
            }
        });
        request(selectRegion);
    }

    private void updateGlobalInfoFromServer(GlobalConfig globalConfig, GlobalConfigObserver observer) {
        boolean allUpdate = updateConfig(globalConfig, true);
        HLog.d(TAG, HLog.USR, "forceUpdate! allUpdate=" + allUpdate + ", data=" + globalConfig.configMap);
        if (allUpdate) {
            forceUpdateConfigs(observer);
        } else {
            forceUpdateCallBack(observer, false, "allUpdate false!");
        }
    }

    public void forceUpdateConfigs(GlobalConfigObserver observer) {
        Activity activity = ActivityTaskManager.getInstance().getTopActivity();
        if (null != activity) {
            ApiService.of(HwApi.class).showProgressDialog(activity, ResUtil.getStr(R.string.pull_config_again), false);
        }
        ConfigHelper.getInstance().forceUpdateAllEditions(new ConfigHelper.UpdateCallback() {
            @Override
            public void onSuccess() {
                ApiService.of(HwApi.class).hideProgressDialog(activity);
                forceUpdateCallBack(observer, true, " forceUpdateAllEditions success!");
            }

            @Override
            public void onFailed(String msg) {
                ApiService.of(HwApi.class).hideProgressDialog(activity);
                forceUpdateCallBack(observer, false, "forceUpdateAllEditions error! msg=" + msg);
            }
        });
    }

    private void forceUpdateCallBack(GlobalConfigObserver observer, boolean isSuccess, String msg) {
        HLog.d(TAG, HLog.USR, "forceUpdateCallBack! msg={}", msg);
        if (null != observer) {
            observer.forceAllUpdate(isSuccess, msg);
        }
    }

    public void addObserver(GlobalConfigObserver observer) {
        if (null == observer || observers.contains(observer)) {
            return;
        }
        observers.add(observer);
    }

    public void removeObserver(GlobalConfigObserver observer) {
        if (null == observer || !observers.contains(observer)) {
            return;
        }
        observers.remove(observer);
    }

    public void clear() {
        isReady = false;
        if (strategy != null) {
            strategy.clear();
        }
        save("");
    }

    public String getRegion() {
        String region = "";
        if (null != config) {
            return config.region;
        }
        return region;
    }

    // 判断是否刷新缓存
    public void updateCacheIfNeed(GlobalConfig globalConfig) {
        if (null == globalConfig) {
            return;
        }
        boolean isWerewolfSame = isSameHost(globalConfig, werewolf_server);
        boolean isChatSame = isSameHost(globalConfig, chat_server);
        boolean isConnectorSame = isSameHost(globalConfig, connector_server);
        HLog.d(TAG, HLog.USR, "updateCacheIfNeed isWerewolfSame=" + isWerewolfSame + ", isChatSame=" + isChatSame + ", isConnectorSame=" + isConnectorSame);
        if (!isWerewolfSame || !isChatSame || !isConnectorSame) {
            updateConfigCache(globalConfig, true);
        }
    }

    // 是否是相同的host
    public boolean isSameHost(GlobalConfig globalConfig, String key) {
        boolean flag = false;
        ConfigItem config = globalConfig.configMap.get(key);
        ConfigItem cacheConfig = this.config.getItem(key);
        if (null != config && null != cacheConfig) {
            if (!TextUtil.isEmpty(cacheConfig.host)) {
                flag = cacheConfig.host.equalsIgnoreCase(config.host) && cacheConfig.port == config.port;
            }
        }
        return flag;
    }

    /**
     * 仅代表华语服，
     */
    public boolean isChineseServer() {
        return GlobalCommon.INSTANCE.isChineseServer(getRegion());
    }

    /**
     * 是否是泰服
     */
    public boolean isThailandServer() {
        return GlobalCommon.INSTANCE.isThailandServer(getRegion());
    }

    /**
     * 是否是菲律宾服
     */
    public boolean isPhilippinesServer() {
        return GlobalCommon.INSTANCE.isPhilippinesServer(getRegion());
    }

    /**
     * 是否是越南服
     */
    public boolean isVietnamServer() {
        return GlobalCommon.INSTANCE.isVietnamServer(getRegion());
    }

    /**
     * 是否是马来服
     */
    public boolean isMalaysiaServer() {
        return GlobalCommon.INSTANCE.isMalaysiaServer(getRegion());
    }

    /**
     * 是否是阿语服
     */
    public boolean isArServer() {
        return GlobalCommon.INSTANCE.isArServer(getRegion());
    }

    /**
     * 是否是Jackaroo
     */
    public static boolean isJackarooServer() {
        return GlobalCommon.isJackarooServer(getInstance().getRegion());
    }

    /**
     * 是否是土语服
     */
    public boolean isTurkeyServer() {
        return GlobalCommon.INSTANCE.isTurkeyServer(getRegion());
    }

    /**
     * 是否是印度服
     */
    public boolean isIndiaServer() {
        return GlobalCommon.INSTANCE.isIndiaServer(getRegion());
    }


    public String getConfigs() {
        if (null == config) {
            loadLocalConfig();
        }
        if (TextUtils.isEmpty(configStr)) {
            configStr = config.toJson();
        }
        return configStr;
    }

    public static boolean showInCurrent(String regions) {
        return !TextUtils.isEmpty(regions) && regions.contains(getInstance().getRegion());
    }

    private static class Holder {
        private static final GlobalConfigManager instance = new GlobalConfigManager();
    }
}
