package com.huiwan.configservice;

import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.google.gson.TypeAdapter;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.configservice.constentity.ConstV3Info;
import com.huiwan.configservice.editionentity.AISceneGiftOrdersConfig;
import com.huiwan.configservice.editionentity.IVersion;
import com.wepie.liblog.main.HLog;

import java.util.HashMap;
import java.util.Map;

public class ConfigInfo<T extends IVersion> {
    private T config;
    final Class<T> c;
    private final MutableLiveData<T> liveData = new MutableLiveData<>();
    final String url;
    final String cacheFileName;
    final boolean required;
    final Map<String, String> extParam = new HashMap<>();
    /**
     * 序列化工具，需要支持的可以主动传入
     */
    @Nullable
    final TypeAdapter<T> adapter;
    private final static String TAG = "ConfigInfo";
    public ConfigInfo(Class<T> c, boolean required, String url, String cacheFileName) {
        this(c, required, url, cacheFileName, null);
    }
    public ConfigInfo(Class<T> c, boolean required, String url, String cacheFileName, @Nullable TypeAdapter<T> adapter) {
        this.c = c;
        this.url = url;
        this.cacheFileName = cacheFileName;
        this.required = required;
        this.adapter = adapter;
        try {
            this.config = c.newInstance();
        } catch (Exception e) {
            if (LibBaseUtil.buildDebug()) {
                throw new IllegalArgumentException("default constructor need, check the proguard-rules if need");
            }
        }
    }

    public ConfigInfo<T> addExtUrlParam(String key, String value) {
        extParam.put(key, value);
        return this;
    }

    public Map<String, Object> getReqParam(boolean forceRefresh) {
        Map<String, Object> map = new HashMap<>();
        map.put("version_num", forceRefresh ? 0 : getVersion());
        map.put("md5_version", forceRefresh ? "" : getVersionMD5());
        map.putAll(extParam);
        return map;
    }

    public T getConfig() {
        return config;
    }

    public LiveData<T> getLiveData() {
        return liveData;
    }

    public int getVersion() {
        return config.getVersion();
    }

    public String getVersionMD5() {
        return config.getVersionMD5();
    }

    public synchronized boolean updateConfigIfNeed(boolean fromServer, boolean force, T config) {
        if (config == null) {
            HLog.d(TAG, HLog.USR, "updateConfigIfNeed, config is null! c={}", c.getSimpleName());
            return false;
        }
        try {
            if (config instanceof IConfig) {
                ((IConfig) config).transform();
            }
            HLog.d(TAG, HLog.USR, "updateConfigIfNeed ! force={},fromServer={}, old_version={},new_version={},md5={},new_md5={},c={}", force, fromServer, getVersion(), config.getVersion(), getVersionMD5(), config.getVersionMD5(), c.getSimpleName());
            if (force) {
                this.config = config;
                HLog.d(TAG, "force update config {}, to version:{}, ", c.getSimpleName(), config.getVersion());
                return true;
            } else if (fromServer) {
                if (c == ConstV3Info.class || c == AISceneGiftOrdersConfig.class) {
                    HLog.d(TAG, "update config from {} to {} for {}", this.config.getVersion(), config.getVersion(), c.getSimpleName());
                    this.config = config;
                    return true;
                } else if (TextUtils.isEmpty(this.config.getVersionMD5())) {
                    if (this.config.getVersion() <= 0 || this.config.getVersion() != config.getVersion()) {
                        HLog.d(TAG, "update config from {} to {} for {}", this.config.getVersion(), config.getVersion(), c.getSimpleName());
                        this.config = config;
                        return true;
                    } else {
                        HLog.d(TAG, "update config ignored, version same: {}, for {}", config.getVersion(), c.getSimpleName());
                        return false;
                    }
                } else {
                    if (!this.config.getVersionMD5().equals(config.getVersionMD5())) {
                        HLog.d(TAG, "update config from {} to {} for {}", this.config.getVersion(), config.getVersion(), c.getSimpleName());
                        this.config = config;
                        return true;
                    } else {
                        HLog.d(TAG, "update config ignored, version same: {}, for {}", config.getVersion(), c.getSimpleName());
                        return false;
                    }
                }
            } else {
                if (TextUtils.isEmpty(getVersionMD5())) {
                    if (getVersion() == 0) {
                        HLog.d(TAG, "init config from local for {}", c.getSimpleName());
                        this.config = config;
                        return true;
                    } else {
                        HLog.d(TAG, "init config after http req, ignored {}", c.getSimpleName());
                        return false;
                    }
                } else {
                    HLog.d(TAG, "init config after http req, ignored {}", c.getSimpleName());
                    return false;
                }
            }
        } finally {
            if (this.config != null) {
                liveData.postValue(this.config);
            } else {
                liveData.postValue(config);
            }
        }
    }

    public boolean isNotInit(){
        if (TextUtils.isEmpty(getVersionMD5())){
            return getVersion() == 0;
        }
        return false;
    }
}
