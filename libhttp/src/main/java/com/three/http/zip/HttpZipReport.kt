package com.three.http.zip

import com.welib.alinetlog.AliNetLogUtil
import com.wepie.liblog.main.FLog
import com.wepie.liblog.main.HLog

/**
 * 解压上报：
 *   1. 开启和不开启压缩情况下，http/tcp 整体耗时监控
 *   2. 开启和不开启压缩情况下，流量消耗监控
 *   3. 解压耗时监控
 *   4. 解压失败监控
 * <AUTHOR>
 * @since 2023/2/8 16:39
 */
object HttpZipReport {

    private const val tag = "HttpReport"

    /**
     * 解压失败统计
     */
    fun zipErrorReport(error: String?,  url: String, zip: String) {
        val map: MutableMap<String, String> = HashMap()
        map["name"] = "ZipError"
        map["errmsg"] = error ?: ""
        map["req_zip"] = zip
        map["type"] = "http"
        map["url"] = url
        HLog.d(tag, "error:{} map={}", error, map)
        HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "", map)

        // 只记录错误，不抛出异常，避免中断正常的请求流程
        try {
            FLog.e(IllegalStateException(error))
        } catch (e: Exception) {
            // 防止日志记录本身出现问题
            HLog.e(tag, "Failed to log error: ${e.message}")
        }
    }
}