package com.three.http.zip

import com.three.http.interceptor.ZipInterceptor
import com.wepie.liblog.main.HLog
import okhttp3.Headers
import java.io.IOException
import java.io.InputStream

/**
 * http 压缩工具类
 *
 * tcp的加一个brotli的枚举，
 * http的x-compress-alg请求头改为和tcp accept_encoding的含义一样，返回头改为使用的压缩算法，枚举值和tcp的枚举保持一致
 *
 * <AUTHOR>
 * @since 2023/2/2 16:41
 */
object HttpZipUtil {

    // 压缩字符标识
    const val GZIP = "Gzip"
    const val BROTLI = "Brotli"
    const val NO = "no"

    private const val tag = "Zip_Http"

    // 1 表示 Gzip   2 表示
    private const val httpGzipTag = "X-CA"

    private const val UnSupportCompress_VALUE = 0
    private const val GZIP_VALUE = 1
    private const val Brotli_VALUE = 2

    // 是否开启压缩功能: 开关
    @Volatile
    private var useZipSwitch = true

    // 当前压缩类型
    private var zipType = GZIP


    // 是否开启日志
    var isOpenLog = false


    /**
     * 添加头部压缩标识, 支持 Gzip 和 Brotli
     *
     * @param map 头部map
     */
    @JvmStatic
    fun addGzipParams(map: MutableMap<String, String>) {
        val flag = GZIP_VALUE or Brotli_VALUE
        addGzipParams(map, flag)
    }


    /**
     * 添加头部压缩标识, 支持 Gzip 和 Brotli
     *
     * @param map 头部map
     */
    @JvmStatic
    fun addGzipParams(map: MutableMap<String, String>, acceptEncoding: Int) {
        if (useZipSwitch) {
            map[httpGzipTag] = acceptEncoding.toString()
        } else {
            map[httpGzipTag] = UnSupportCompress_VALUE.toString()
        }
    }


    /**
     * 响应头头部标识
     *
     * @param headers 头部
     */
    @JvmStatic
    fun isCompress(headers: Headers): Boolean {
        val flag = headers[httpGzipTag]
        val zip = flag?.isNotEmpty() == true
        if (!zip) zipType = NO
        return zip
    }


    /**
     * 响应头头部标识
     *
     * @param headers 头部
     */
    @JvmStatic
    fun isCompress(headers: Map<String, List<String>>): Boolean {
        val list: List<String>? = headers[httpGzipTag]
        val zip = list?.isNotEmpty() == true
        if (!zip) zipType = NO
        return zip
    }


    /**
     * 解压
     *
     * @param interceptor 拦截器
     * @param headers 头部
     */
    @JvmStatic
    @Throws(IOException::class)
    fun compress(
        interceptor: ZipInterceptor, headers: Headers,
        input: InputStream, url: String
    ): InputStream {
        val flag = getHeadTag(headers)
        return rCompress(interceptor, flag, input, url)
    }


    /**
     * 解压
     *
     * @param interceptor 拦截器
     * @param headers 头部
     */
    @JvmStatic
    @Throws(IOException::class)
    fun compress(
        interceptor: ZipInterceptor, headers: Map<String, List<String>>,
        input: InputStream, url: String
    ): InputStream {
        val flag = getHeadTag(headers)
        return rCompress(interceptor, flag, input, url)
    }


    /**
     * 解压
     *
     */
    @JvmStatic
    @Throws(IOException::class)
    private fun rCompress(
        interceptor: ZipInterceptor, flag: String,
        input: InputStream, url: String
    ): InputStream {
        var realInput: InputStream = input
        when (flag) {
            GZIP_VALUE.toString() -> {
                try {
                    zipType = GZIP
                    realInput = interceptor.gzipUnCompress(input)
                    logS(url, GZIP)
                } catch (e: Exception) {
                    val errorMsg = e.message ?: ""

                    // 如果是请求取消，不禁用压缩开关
                    if (!errorMsg.contains("canceled", ignoreCase = true) &&
                        !errorMsg.contains("cancelled", ignoreCase = true)) {
                        useZipSwitch = false
                    }

                    logE(url, GZIP, errorMsg)
                    throw IOException("GZIP decompression failed: $errorMsg", e)
                }
            }

            Brotli_VALUE.toString() -> {
                try {
                    zipType = BROTLI
                    realInput = interceptor.brotliUnCompress(input)
                    logS(url, BROTLI)
                } catch (e: Exception) {
                    val errorMsg = e.message ?: ""

                    // 如果是请求取消，不禁用压缩开关
                    if (!errorMsg.contains("canceled", ignoreCase = true) &&
                        !errorMsg.contains("cancelled", ignoreCase = true)) {
                        useZipSwitch = false
                    }

                    logE(url, "Brotli", errorMsg)
                    throw IOException("Brotli decompression failed: $errorMsg", e)
                }
            }

            else -> {
                zipType = NO
                if (isOpenLogs()) {
                    HLog.d(tag, "url:{}(no compress)", url)
                }
            }
        }
        return realInput
    }


    private fun getHeadTag(headers: Headers): String {
        return headers[httpGzipTag] ?: ""
    }

    private fun getHeadTag(headers: Map<String, List<String>>): String {
        val list = headers[httpGzipTag]
        if (list.isNullOrEmpty()) {
            return ""
        }
        return list[0]
    }


    /**
     * 解压成功日志
     *
     * @param url 请求 url
     * @param from 来源  http/quic
     * @param result 解压后内容
     */
    @JvmStatic
    fun logS(url: String, zip: String) {
        if (isOpenLogs()) {
            // 解压耗时
            HLog.d(tag, "{} {} success!", url, zip)
        }
    }


    /**
     * 解压失败日志
     *
     * @param url 请求 url
     * @param zipType 压缩类型
     * @param error 错误信息
     */
    @JvmStatic
    fun logE(url: String, zipType: String, error: String) {
        val str = "$url $zipType error! $error"
        uploadLog(str)

        // 检查是否是请求取消错误，如果是则降低日志级别
        if (error.contains("canceled", ignoreCase = true) ||
            error.contains("cancelled", ignoreCase = true)) {
            // 请求取消是正常情况，使用DEBUG级别日志
            HLog.d(tag, "Request canceled during decompression: $str")
        } else {
            // 其他错误才进行错误上报
            HttpZipReport.zipErrorReport(error, url, zipType)
        }
    }


    @JvmStatic
    fun log(format: String, vararg obj: Any) {
        if (isOpenLogs()) {
            HLog.d(tag, format, *obj)
        }
    }


    /**
     * 上报日志
     */
    @JvmStatic
    fun uploadLog(msg: String) {
        HLog.e(tag, HLog.USR, msg)
    }

    /**
     * 目前是进入超级用户中心，设置 http 压缩选项后，会开启 log
     */
    @JvmStatic
    fun openLog() {
        isOpenLog = true
    }


    @JvmStatic
    fun zipType(): String {
        return zipType
    }


    private fun isOpenLogs(): Boolean {
        return isOpenLog
    }
}