package com.three.http.alternatedomain

import com.huiwan.base.LibBaseUtil
import com.huiwan.base.common.GlobalCommon
import com.huiwan.base.common.GlobalConfig.ConfigItem
import com.three.http.core.HttpUtil
import com.wepie.liblog.main.HLog
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Request
import okhttp3.Response
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/****
 * <AUTHOR> zhangjing
 * @date : 星期四 10/19/23
 */
object AlternateDomainManager {

    const val TAG = "ApiAlternateDomain"

    private val currentHostItem = AtomicReference<HostItem>()
    private var isChecking = AtomicBoolean(false)
    private val checkResult = ConcurrentHashMap<String, Long>()
    private val errorCount = ConcurrentLinkedQueue<Long>()
    private val client by lazy {
        HttpUtil.getDefaultHttpClient()
    }

    fun getFinalHost(host: String): String? {
        if (!checkHostValid(host)) return null
        val finalHost = currentHostItem.get()?.alternateDomains?.firstOrNull()
        if (host != finalHost) {
            HLog.d(TAG, "replace host: {} to finalHost: {}", host, finalHost)
        }
        return finalHost
    }

    @JvmStatic
    fun updateDomains(region: String, configs: List<ConfigItem>) {
        val extraDomains = configs.map {
            if (it.host.contains("://")) {
                it.host.substringAfter("://")
            } else {
                it.host
            }
        }
        HLog.d(TAG, HLog.USR, "update extra size: {}, domains: {}", extraDomains.size, extraDomains)
        val host = if (LibBaseUtil.buildDebug()) {
            regionMapDebug[region]
        } else {
            regionMap[region]
        }
        if (host == null) {
            HLog.d(TAG, "couldn't find host for region: {}", region)
            return
        }
        if (!checkHostValid(host)) return
        var oldDomains = if (LibBaseUtil.buildDebug()) {
            defaultDomainsMapDebug[host]
        } else {
            defaultDomainsMap[host]
        }?.toMutableList()
        if (oldDomains == null) {
            HLog.d(TAG, "no default domains set for host: {}", host)
            oldDomains = mutableListOf()
        }
        extraDomains.forEach {
            if (it !in oldDomains) {
                oldDomains.add(it)
            }
        }
        currentHostItem.set(HostItem(host, oldDomains))
    }

    fun increaseErrorTime(host: String) {
        if (!checkHostValid(host)) return
        errorCount.offer(System.currentTimeMillis())
        if (errorCount.size >= 3) {
            val lastTime = errorCount.peek() ?: return
            if (lastTime - System.currentTimeMillis() < 5000) {
                currentHostItem.get()?.let {
                    tryUpdate(it.host, it.alternateDomains)
                }
                errorCount.clear()
            }
            errorCount.poll()
        }
    }

    fun clearErrorTime(host: String) {
        if (!checkHostValid(host)) return
        errorCount.clear()
    }

    fun forceUpdate() {
        currentHostItem.get()?.let {
            tryUpdate(it.host, it.alternateDomains)
        }
    }

    private fun tryUpdate(host: String, alternateDomains: List<String>) {
        if (isChecking.get() || host.isBlank() || alternateDomains.isEmpty()) {
            return
        }
        isChecking.set(true)
        checkResult.clear()
        HLog.d(
            TAG, "start ping, host: {}, size: {}, domains: {}",
            host, alternateDomains.size, alternateDomains
        )

        fun onPingEnd() {
            val newDomains = mutableListOf<String>()
            val oldDomains = currentHostItem.get()?.alternateDomains ?: return
            oldDomains.filter {
                (checkResult[it] ?: -1) > 0
            }.toMutableList().sortedBy {
                checkResult[it] ?: -1L
            }.let {
                newDomains.addAll(it)
            }
            oldDomains.filter {
                (checkResult[it] ?: -1) <= 0
            }.let {
                newDomains.addAll(it)
            }
            HLog.d(TAG, "new order: {}.", newDomains)
            currentHostItem.set(HostItem(host, newDomains))
            isChecking.set(false)
        }

        alternateDomains.map {
            Request.Builder().url("https://$it/").build()
        }.forEach {
            client.newCall(it).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    if (call.request().url.host !in alternateDomains) {
                        return
                    }
                    checkResult[call.request().url.host] = -1
                    HLog.d(TAG, "ping failed, host: {}, error: {}", call.request().url.host, e)
                    if (checkResult.size >= alternateDomains.size) {
                        onPingEnd()
                    }
                }

                override fun onResponse(call: Call, response: Response) {
                    val responseTime =
                        response.receivedResponseAtMillis - response.sentRequestAtMillis
                    checkResult[call.request().url.host] = responseTime
                    HLog.d(
                        TAG,
                        "ping successful, host: {}, time: {}, checkResultSize: {}",
                        call.request().url.host,
                        responseTime,
                        checkResult.size
                    )
                    if (checkResult.size >= alternateDomains.size) {
                        onPingEnd()
                    }
                }
            })
        }
    }

    private fun checkHostValid(host: String): Boolean {
        val hostList = if (LibBaseUtil.buildDebug()) {
            defaultDomainsMapDebug.map { it.key }
        } else {
            defaultDomainsMap.map { it.key }
        }
        return if (host in hostList) {
            if (currentHostItem.get()?.host != host) {
                initHostItem(host)
            }
            true
        } else {
            false
        }
    }

    private fun initHostItem(host: String) {
        if (LibBaseUtil.buildDebug()) {
            defaultDomainsMapDebug[host]
        } else {
            defaultDomainsMap[host]
        }?.let {
            HLog.d(TAG, "init hostItem, host from {} to : {}", currentHostItem.get()?.host, host)
            currentHostItem.set(HostItem(host, it))
        }
    }

    private val defaultDomainsMap: Map<String, List<String>> = mapOf(
        "api.weplayapp.com" to listOf("api.weplayapp.com"),
        "api-bkk.weplayapp.com" to listOf("api-bkk.weplayapp.com"),
        "api-mas.weplayapp.com" to listOf("api-mas.weplayapp.com"),
        "api-phl.weplayapp.com" to listOf("api-phl.weplayapp.com"),
        "api-vnm.weplayapp.com" to listOf("api-vnm.weplayapp.com"),
        "api-tky.weplayapp.com" to listOf("api-tky.weplayapp.com"),
        "api-sel.weplayapp.com" to listOf("api-sel.weplayapp.com"),
        "api-fra.weplayapp.com" to listOf("api-fra.weplayapp.com"),
        "api-tur.weplayapp.com" to listOf("api-tur.weplayapp.com"),
        "api-in.weplayapp.com" to listOf("api-in.weplayapp.com"),
        "api-sv.weplayapp.com" to listOf("api-sv.weplayapp.com")
    )

    private val defaultDomainsMapDebug: Map<String, List<String>> = mapOf(
        "api-dev.weplayapp.com" to listOf("api-dev.weplayapp.com"),
        "api-dev-bkk.weplayapp.com" to listOf("api-dev-bkk.weplayapp.com"),
        "api-dev-mas.weplayapp.com" to listOf("api-dev-mas.weplayapp.com"),
        "api-dev-phl.weplayapp.com" to listOf("api-dev-phl.weplayapp.com"),
        "api-dev-vnm.weplayapp.com" to listOf("api-dev-vnm.weplayapp.com"),
        "api-dev-tky.weplayapp.com" to listOf("api-dev-tky.weplayapp.com"),
        "api-dev-sel.weplayapp.com" to listOf("api-dev-sel.weplayapp.com"),
        "api-dev-fra.weplayapp.com" to listOf("api-dev-fra.weplayapp.com"),
        "api-dev-tur.weplayapp.com" to listOf("api-dev-tur.weplayapp.com"),
        "api-dev-in.weplayapp.com" to listOf("api-dev-in.weplayapp.com"),
        "api-dev-sv.weplayapp.com" to listOf("api-dev-sv.weplayapp.com")
    )

    private val regionMap: Map<String, String> = mapOf(
        GlobalCommon.SERVER_REGION_AR to "api-fra.weplayapp.com",
        GlobalCommon.SERVER_REGION_TR to "api-tur.weplayapp.com",
        GlobalCommon.SERVER_REGION_HI to "api-in.weplayapp.com"
    )

    private val regionMapDebug: Map<String, String> = mapOf(
        GlobalCommon.SERVER_REGION_AR to "api-dev-fra.weplayapp.com",
        GlobalCommon.SERVER_REGION_TR to "api-dev-tur.weplayapp.com",
        GlobalCommon.SERVER_REGION_HI to "api-dev-in.weplayapp.com"
    )

}

class HostItem(val host: String, domains: List<String>) {
    val alternateDomains: List<String>

    init {
        alternateDomains = domains.filter { it.isNotBlank() }
    }
}