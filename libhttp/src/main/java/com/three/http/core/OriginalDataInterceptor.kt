package com.three.http.core

import androidx.collection.ArrayMap
import com.three.http.interceptor.ParamInterceptor.ParamConfig
import com.three.http.zip.HttpZipUtil
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.MediaType
import okhttp3.RequestBody
import okhttp3.Response
import okhttp3.ResponseBody
import okhttp3.internal.closeQuietly
import okio.Buffer
import okio.BufferedSink
import okio.BufferedSource
import java.io.InputStream

class OriginalDataInterceptor(private val configBuilder: ConfigBuilder) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var body = request.body

        if (body !is OriginalDataRequestBody) {
            return chain.proceed(request)
        }
        val oldBody: OriginalDataRequestBody = body
        val url = request.url.toString()
        val needSign = oldBody.needSign
        val checkShumeiId = oldBody.needCheckShumeiId

        val requestBuild = request.newBuilder()
        val bodyBuilder = FormBody.Builder()
        val bodyMap = ArrayMap<String, String?>()
        val paramCfg = ParamConfig(checkShumeiId)
        configBuilder.paramInterceptor.addCommonParams(bodyMap, paramCfg) //把公用参数添加到map
        bodyMap.putAll(oldBody.map)
        if (needSign) {
            val signKey = configBuilder.signInterceptor.getSignKey()
            var uri = ""
            request.url.pathSegments.forEach {
                uri += "/$it"
            }
            val signValue = configBuilder.signInterceptor.getSignValue(uri, bodyMap)
            bodyMap[signKey] = signValue
        }
        bodyMap.entries.forEach {
            if (it.value != null) {
                bodyBuilder.add(it.key, it.value!!)
            }
        }

        body = bodyBuilder.build()
        val enc = configBuilder.encInterceptor.needEnc(url)
        val headerMap = ArrayMap<String, String?>()
        if (enc) {
            configBuilder.encInterceptor.addEncHeader(headerMap)
            body = EncRequestBody(body, configBuilder.encInterceptor)
        }
        if (oldBody.disableQuic) {
            requestBuild.addHeader(HttpUtil.REQ_DISABLE_QUIC, true.toString())
        }

        headerMap.entries.forEach {
            if (it.value != null) {
                requestBuild.addHeader(it.key, it.value!!)
            }
        }

        val rsp = chain.proceed(requestBuild.method(request.method, body).build())
        if (!rsp.isSuccessful || rsp.body == null) {
            return rsp
        }
        val compress = HttpZipUtil.isCompress(rsp.headers)
        if (enc || compress) {
            val responseBody: ResponseBody = OriginalResponseBody(rsp, configBuilder, enc)
            return rsp.newBuilder().body(responseBody).build()
        }
        return rsp
    }
}

class OriginalDataRequestBody(
    val map: MutableMap<String, String>,
    val needSign: Boolean,
    val disableQuic: Boolean,
    val needCheckShumeiId: Boolean
) : RequestBody() {
    override fun contentType(): MediaType? = null

    override fun writeTo(sink: BufferedSink) {
        TODO("Not yet implemented") //不允许使用
    }
}

class OriginalResponseBody(
    private val rsp: Response,
    private val configBuilder: ConfigBuilder,
    private val enc: Boolean = true,
    private val compress: Boolean = true
) : ResponseBody() {
    private var realStream: InputStream? = null
    private val contentBuffer = Buffer()
    private val body = rsp.body!!

    override fun contentLength(): Long {
        updateContentBuffer()
        return contentBuffer.size
    }

    override fun contentType(): MediaType? = body.contentType()

    override fun source(): BufferedSource {
        updateContentBuffer()
        return contentBuffer
    }

    override fun close() {
        super.close()
        realStream?.close()
        body.closeQuietly()
    }

    private fun updateContentBuffer() {
        if (contentBuffer.size > 0) {
            return
        }

        val url = rsp.request.url.toString()
        var input: InputStream = body.byteStream()
        if (enc) {
            input = configBuilder.encInterceptor.decrypt(input)
        }

        if (compress) {
            input = HttpZipUtil.compress(
                configBuilder.zipInterceptor, rsp.headers,
                input, url
            )
        }
        contentBuffer.readFrom(input)
        this.realStream = input
    }
}
