buildscript {

    dependencies {
        classpath libs.android.gradlePlugin
        //添加 Sensors Analytics android-gradle-plugin 依赖
        classpath libs.sensorsdata.analytics.plugin

        classpath libs.agcp.plugin

        classpath libs.imagecompress.plugin

        classpath libs.matrix.gradle.plugin
        // Performance Monitoring plugin
        classpath libs.manifestplugin
        classpath libs.skynet.gradle.plugin
        classpath libs.xml.proguard.plugin
        classpath libs.trace.plugin
    }
}

plugins {
    alias(libs.plugins.kotlin.jvm) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.compose.compiler) apply false
    alias(libs.plugins.dynamic.feature) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.androidTest) apply false
    alias(libs.plugins.baselineprofile) apply false
    alias(libs.plugins.firebase.crashlytics) apply false
    alias(libs.plugins.gms) apply false
    alias(libs.plugins.protobuf) apply false
    id 'com.github.ben-manes.versions' version '0.50.0'
}

apply from: "config.gradle"