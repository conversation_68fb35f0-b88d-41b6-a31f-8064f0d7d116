package com.wepie.debug

import android.app.Application
import android.webkit.WebView
import com.huiwan.lib.api.plugins.IDebugPluginApi

/**
 * 实现类
 * <AUTHOR>
 * @since 2023/4/4 09:49
 */
class DebugDefaultImpl : IDebugPluginApi {
    override fun initConfig(context: Application) {
    }

    override fun initWebView(mWebView: WebView?) {
    }

    override fun showCurPageMsg(key: String, value: String) {
    }

    override fun showCurPageMsg(params: Map<String?, String?>?) {
    }

    override fun monitorRequestNet(url: String?, params: Map<String?, String?>?) {
    }

    override fun monitorTcp(desc: String?, content: String?) {
    }

    override fun monitorResponseNet(success: Boolean, data: String?, url: String?) {
    }

    override fun monitorLog(type: String?, tag: String?, console: String?) {
    }

    override fun onResourceReady(resource: Any?, url: Any?, target: Any?) {
    }

    override fun mockTcp(cmd: Int, type: Int, callback: Any?): Bo<PERSON>an {
        return false
    }

}