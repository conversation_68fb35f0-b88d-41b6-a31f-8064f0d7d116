package com.wepie.debug;

import android.util.Log;

/**
 * ProtoReflectionMapper测试类
 * 用于验证修复后的映射功能是否正常工作
 */
public class ProtoReflectionMapperTest {
    
    private static final String TAG = "ProtoMapperTest";
    
    /**
     * 测试主方法
     */
    public static void runTests() {
        Log.e(TAG, "=== ProtoReflectionMapper 测试开始 ===");
        
        // 测试初始化
        testInitialization();
        
        // 测试特定的command映射
        testSpecificCommands();
        
        // 测试command 5 (GROUP)
        testGroupCommand();
        
        // 测试RspBody类获取
        testRspBodyClassRetrieval();
        
        Log.e(TAG, "=== ProtoReflectionMapper 测试完成 ===");
    }
    
    /**
     * 测试初始化功能
     */
    private static void testInitialization() {
        Log.e(TAG, "\n--- 测试初始化 ---");
        
        try {
            // 手动初始化
            ProtoReflectionMapper.initializeMappings();
            
            // 检查是否初始化成功
            boolean isInitialized = ProtoReflectionMapper.isMappingsInitialized();
            Log.e(TAG, "初始化状态: " + (isInitialized ? "成功" : "失败"));
            
            // 获取支持的command数量
            int supportedCommandCount = ProtoReflectionMapper.getSupportedCommands().size();
            Log.e(TAG, "支持的command数量: " + supportedCommandCount);
            
        } catch (Exception e) {
            Log.e(TAG, "初始化测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试特定的command映射
     */
    private static void testSpecificCommands() {
        Log.e(TAG, "\n--- 测试特定command映射 ---");

        // 测试一些已知的command，包括之前失败的
        int[] testCommands = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 21};

        int successCount = 0;
        int totalCount = testCommands.length;

        for (int command : testCommands) {
            String serviceName = ProtoReflectionMapper.getServiceName(command);
            Class<?> commandClass = ProtoReflectionMapper.getCommandClass(command);

            if (commandClass != null) {
                successCount++;
                Log.e(TAG, "✅ Command " + command + " (" + serviceName + ") -> " + commandClass.getSimpleName());
            } else {
                Log.e(TAG, "❌ Command " + command + " (" + serviceName + ") -> 未找到类");
            }
        }

        Log.e(TAG, "映射成功率: " + successCount + "/" + totalCount + " (" + (successCount * 100 / totalCount) + "%)");
    }
    
    /**
     * 专门测试command 5 (GROUP)
     */
    private static void testGroupCommand() {
        Log.e(TAG, "\n--- 测试GROUP命令 (command=5) ---");
        
        try {
            // 获取服务名
            String serviceName = ProtoReflectionMapper.getServiceName(5);
            Log.e(TAG, "Command 5 服务名: " + serviceName);
            
            // 获取对应的Java类
            Class<?> groupClass = ProtoReflectionMapper.getCommandClass(5);
            if (groupClass != null) {
                Log.e(TAG, "✅ Command 5 对应的类: " + groupClass.getName());
                Log.e(TAG, "类的简单名称: " + groupClass.getSimpleName());
                
                // 检查类是否有预期的字段和方法
                testGroupClassStructure(groupClass);
                
            } else {
                Log.e(TAG, "❌ Command 5 未找到对应的Java类");
            }
            
            // 获取支持的type值
            java.util.Set<Integer> supportedTypes = ProtoReflectionMapper.getSupportedTypes(5);
            Log.e(TAG, "Command 5 支持的type数量: " + supportedTypes.size());
            
        } catch (Exception e) {
            Log.e(TAG, "测试GROUP命令时出错: " + e.getMessage());
        }
    }
    
    /**
     * 测试GroupPackets类的结构
     */
    private static void testGroupClassStructure(Class<?> groupClass) {
        Log.e(TAG, "\n--- 检查GroupPackets类结构 ---");
        
        try {
            // 检查是否有GroupType内部类
            Class<?>[] declaredClasses = groupClass.getDeclaredClasses();
            boolean hasGroupType = false;
            
            for (Class<?> innerClass : declaredClasses) {
                if (innerClass.getSimpleName().equals("GroupType")) {
                    hasGroupType = true;
                    Log.e(TAG, "✅ 找到GroupType内部类");
                    break;
                }
            }
            
            if (!hasGroupType) {
                Log.e(TAG, "❌ 未找到GroupType内部类");
            }
            
            // 检查是否有RspBody相关的内部类
            int rspBodyCount = 0;
            for (Class<?> innerClass : declaredClasses) {
                String className = innerClass.getSimpleName();
                if (className.endsWith("Rsp") || className.endsWith("RspBody")) {
                    rspBodyCount++;
                    Log.d(TAG, "找到RspBody类: " + className);
                }
            }
            
            Log.e(TAG, "RspBody相关类数量: " + rspBodyCount);
            
        } catch (Exception e) {
            Log.e(TAG, "检查类结构时出错: " + e.getMessage());
        }
    }
    
    /**
     * 测试RspBody类获取功能
     */
    private static void testRspBodyClassRetrieval() {
        Log.e(TAG, "\n--- 测试RspBody类获取 ---");
        
        // 测试一些常见的command和type组合
        int[][] testCases = {
            {5, 1},  // GROUP, GET_GROUP_LIST
            {5, 2},  // GROUP, GROUP_SYNC
            {5, 3},  // GROUP, CREATE
            {3, 1},  // GAMEMATCH, 某个type
            {2, 1},  // TMPROOM, 某个type
        };
        
        for (int[] testCase : testCases) {
            int command = testCase[0];
            int type = testCase[1];
            
            try {
                Class<?> rspBodyClass = ProtoReflectionMapper.getRspBodyClass(command, type);
                String serviceName = ProtoReflectionMapper.getServiceName(command);
                
                if (rspBodyClass != null) {
                    Log.e(TAG, "✅ Command " + command + " (" + serviceName + "), Type " + type + 
                          " -> " + rspBodyClass.getSimpleName());
                } else {
                    Log.e(TAG, "❌ Command " + command + " (" + serviceName + "), Type " + type + 
                          " -> 未找到RspBody类");
                }
                
            } catch (Exception e) {
                Log.e(TAG, "获取RspBody类时出错 (Command " + command + ", Type " + type + "): " + e.getMessage());
            }
        }
    }
    
    /**
     * 打印详细的映射信息（调试用）
     */
    public static void printDetailedMappings() {
        Log.e(TAG, "\n=== 详细映射信息 ===");
        ProtoReflectionMapper.printMappings();
    }
    
    /**
     * 测试特定的command和type组合
     */
    public static void testSpecificCase(int command, int type) {
        Log.e(TAG, "\n=== 测试特定案例: Command " + command + ", Type " + type + " ===");
        
        String serviceName = ProtoReflectionMapper.getServiceName(command);
        Class<?> commandClass = ProtoReflectionMapper.getCommandClass(command);
        Class<?> rspBodyClass = ProtoReflectionMapper.getRspBodyClass(command, type);
        
        Log.e(TAG, "服务名: " + serviceName);
        Log.e(TAG, "Command类: " + (commandClass != null ? commandClass.getName() : "未找到"));
        Log.e(TAG, "RspBody类: " + (rspBodyClass != null ? rspBodyClass.getName() : "未找到"));
        
        if (rspBodyClass != null) {
            Log.e(TAG, "✅ 成功找到对应的RspBody类");
        } else {
            Log.e(TAG, "❌ 未找到对应的RspBody类");
        }
    }
}
