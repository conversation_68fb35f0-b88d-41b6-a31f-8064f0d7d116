package com.wepie.debug

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import android.webkit.WebView
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.SwitchCompat
import com.bumptech.glide.request.target.ViewTarget
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.JsonUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.TextUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.lib.api.plugins.IDebugPluginApi
import com.huiwan.platform.ThreadUtil
import com.huiwan.store.PrefUtil
import com.huiwan.store.SdkBaseStore
import com.huiwan.store.database.DBUpdateUtil
import com.huiwan.store.database.WPStore
import com.huiwan.user.LoginHelper
import com.wepie.largeimage.hook.LargeImage
import com.wepie.largeimage.hook.LargeImageManager
import com.wepie.largeimage.hook.activity.LargeImageListActivity
import com.wepie.largeimage.hook.util.SystemUtil.getPageName
import com.wepie.liblog.main.HLog
import com.wepie.wespy.net.tcp.packet.HeadPackets
import wepie.skynet.debugtools.DebugPlugin
import wepie.skynet.debugtools.config.DebugConfig
import wepie.skynet.debugtools.config.request.IBusinessRequest

/**
 * 真实实现类
 * <AUTHOR>
 * @since 2023/4/4 09:49
 */
class DebugImpl : IDebugPluginApi {

    private val openImage = PrefUtil.getInstance().getBoolean(IMAGE_KEY, false)

    /**
     * 初始化 配置
     */
    override fun initConfig(context: Application) {
        initDebugPlugin(context)
    }

    /**
     * 初始化 webview
     */
    override fun initWebView(mWebView: WebView?) {
        mWebView?.let {
            DebugPlugin.getInstance().initWebView(it)
        }
    }

    override fun showCurPageMsg(key: String, value: String) {
        val map = mutableMapOf<String, String>()
        map[key] = value
        DebugPlugin.getInstance().config.addTempData(map)
    }

    override fun showCurPageMsg(params: Map<String?, String?>?) {
        params?.let {
            DebugPlugin.getInstance().config.addTempData(it)
        }
    }

    /**
     * 发起网络请求
     */
    override fun monitorRequestNet(url: String?, params: Map<String?, String?>?) {
        url?.let {
            DebugPlugin.getInstance().monitorRequestNet(it, params ?: mapOf())
        }
    }

    override fun monitorTcp(desc: String?, content: String?) {
        content?.let {
            DebugPlugin.getInstance().monitorTcp(desc, it)
        }
    }

    /**
     * 请求回来
     */
    override fun monitorResponseNet(success: Boolean, data: String?, url: String?) {
        url?.let {
            if (success) {
                DebugPlugin.getInstance().monitorResponseNet(data ?: "", it)
            } else {
                DebugPlugin.getInstance().monitorResponseErrorNet(data ?: "", it)
            }
        }
    }

    private val tcpList = listOf(
        "receive tcp msg head",
        "receive tcp msg body",
        "send tcp msg head",
        "send tcp msg body",
        "receive kcp msg head",
        "receive kcp msg body",
        "send kcp msg head",
        "send kcp msg body"
    )

    /**
     * 日志监控
     */
    override fun monitorLog(type: String?, tag: String?, console: String?) {
        console?.let {
            var desc = ""
            tcpList.forEach {
                if (console.contains(it)) {
                    desc = it
                }
            }
            if (!TextUtil.isEmpty(desc)) {
                if (console.contains("#")) {
                    val msg = console.substring(console.indexOf("#") + 1)
                    DebugPlugin.getInstance().monitorTcp(desc, msg)
                } else {
                    DebugPlugin.getInstance().monitorTcp(desc, console)
                }
            } else {
                DebugPlugin.getInstance().monitorLog(type ?: "", tag ?: "", it)
            }
        }
    }

    /**
     * 图片加载成功回调
     */
    override fun onResourceReady(resource: Any?, url: Any?, target: Any?) {
        if (!DebugPlugin.getInstance().isInit) {
            return
        }
        if (!openImage) {
            return
        }
        var width = 0
        var heigh = 0
        var packageName = ""
        if (target is ViewTarget<*, *>) {
            val view: View = target.view
            packageName = getPageName(view)
            width = view.width
            heigh = view.height
        }
        var imageUrl = ""
        if (url is String) {
            imageUrl = url
        }
        if (resource is Bitmap) {
            LargeImageManager.getInstance()
                .transform(imageUrl, resource, "Glide", width, heigh, packageName)
        } else if (resource is BitmapDrawable) {
            LargeImageManager.getInstance()
                .transform(imageUrl, resource, "Glide", width, heigh, packageName)
        }
    }

    override fun mockTcp(cmd: Int, type: Int, callback: Any?): Boolean {
        val cls = ProtoReflectionMapper.getRspBodyClass(cmd, type)
        Log.e("ProtoReflectionMapper", "mockTcp==${cmd}-${type}=${cls}")
        return false
    }

    companion object {

        private const val IMAGE_KEY = "image_key"

        /**
         * 初始化调试工具
         */
        fun initDebugPlugin(context: Application) {

            val appContext: Application = context
            LargeImage.getInstance().install(appContext)
                .setFileSizeThreshold(100.0).memorySizeThreshold = 400.0
            val translateTool = TranslateTool(TranslatorAliyun(AliyunDep()))

            val config = DebugConfig.Builder().setBusinessRequest(object : IBusinessRequest {
                override fun goSuperPage(activity: Activity) {
                    val cls = activity.resources.getString(R.string.super_activity)
                    activity.startActivity(Intent(activity, Class.forName(cls)))
                }

                override fun getDBName(): String {
                    WPStore.getInstance()
                    val uid = SdkBaseStore.getUid()
                    return "db$uid.db"
                }

                override fun getDBVersion(): Int {
                    return DBUpdateUtil.getDbVersion()
                }

                override fun getPackageName(): String {
                    return LibBaseUtil.getApplication().packageName
                }

                override fun getCommonParams(): HashMap<String, String> {
                    val map: HashMap<String, String> = HashMap()
                    map["我的uid"] = LoginHelper.getLoginUid().toString()
                    return map
                }

                override fun getBusinessViewList(
                    context: Context,
                    activity: Activity,
                ): MutableList<View> {
                    val list: MutableList<View> = ArrayList()
                    list.add(createImageListView(context, activity))
                    if (activity is AppCompatActivity) {
                        list.add(translateTool.genTranslateDebugView(context, activity))
                        list.add(LanguageStringTool.genView(context, activity))
                    }
                    return list
                }

            }).builder()
            DebugPlugin.getInstance().initConfig(config)
        }

        /**
         * 获取图片列表
         */
        private fun createImageListView(context: Context, activity: Activity): View {
            val openImage = PrefUtil.getInstance().getBoolean(IMAGE_KEY, false)
            val root = LinearLayout(context)
            root.layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
            root.orientation = LinearLayout.VERTICAL
            root.gravity = Gravity.CENTER

            val view = AppCompatButton(context)
            val params = MarginLayoutParams(ScreenUtil.dip2px(140f), ScreenUtil.dip2px(40f))
            params.topMargin = ScreenUtil.dip2px(10f)
            view.layoutParams = params
            view.text = "图片列表"
            view.setTextColor(Color.WHITE)
            view.gravity = Gravity.CENTER
            view.setBackgroundColor(ResUtil.getColor(R.color.color_accent))
            view.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
            view.setOnClickListener {
                val intent = Intent()
                intent.setClass(LargeImage.APPLICATION, LargeImageListActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                LargeImage.APPLICATION.startActivity(intent)
            }

            val view2 = SwitchCompat(context)
            val params2 = MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            view2.layoutParams = params2
            view2.isChecked = openImage
            view2.text = "图片列表开关"
            view2.gravity = Gravity.CENTER
            view2.setTextColor(Color.BLACK)
            view2.setBackgroundColor(0x00ccf9)
            view2.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    ToastUtil.debugShow("重启app，查看图片列表")
                } else {
                    ToastUtil.debugShow("图片列表，关闭成功")
                }
                PrefUtil.getInstance().setBoolean(IMAGE_KEY, isChecked)
                ThreadUtil.runOnUiThreadDelay(500) {
                    ApiService.of(HwApi::class.java).logoutRestart(activity)
                }
            }

            if (openImage) {
                root.addView(view)
                root.addView(view2)
            } else {
                root.addView(view2)
            }
            return root
        }

    }

    private class AliyunDep : TranslatorAliyun.Depends {
        override fun toJson(any: Any): String {
            return JsonUtil.toJson(any)
        }

        override fun <T> fromJson(any: String, c: Class<T>): T {
            return JsonUtil.fromJson(any, c)
        }

        override fun log(tag: String, msg: String, vararg arg: Any) {
            HLog.d(tag, msg, *arg)
        }
    }
}