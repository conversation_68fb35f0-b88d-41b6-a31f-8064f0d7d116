package com.wepie.lib.api.plugins.voice;

import com.huiwan.base.common.GlobalConfig;
import com.huiwan.lib.api.Api;

public interface IVoiceApi extends Api {
    BaseVoiceManager getVoiceManagerByType(int serviceType,int mediaType);
    BaseVoiceManager getDefaultVoiceManager();
    int getDefaultVoiceType();
    Config getDefaultConfig();
    boolean joinChannel(int voiceType, boolean highQuality, String rid, boolean openMic, boolean isBroadcaster);
    GlobalConfig.VoiceKInfo getVoiceKInfo();
    boolean isSupportVoiceType(int type);
    /**
     * 检测未说话时间。
     * volume 值经过调整，暂无别的过滤
     * @param volume 音量，int, 与内部协调
     */
    void onUserSpeakVolume(String tag, int volume);

    /**
     * 用户操作麦克风打开
     */
    void onMicOpen();

    /**
     * 服务器 push 需要处理挂房时的埋点
     */
    void checkTrackHangDuration();
}
