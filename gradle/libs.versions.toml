# 版本号为unspecified表示没有使用
[versions]
activityCompose = "1.9.1"
androidx-junit = "1.1.3"
androidx-collection = "1.4.4"
androidx-core-runtime = "2.1.0"
androidx-profileinstaller = "1.4.1"

composeBom = "2025.02.00"
guava = "31.1-android"

## 变更日志：https://square.github.io/okhttp/changelogs/changelog_4x/
okhttp = "4.12.0"
okio = "3.2.0"
gson = "2.11.0"
lottie = "6.2.0"

# test
espressoCore = "3.6.1"
junit = "4.13.2"
uiautomator = "2.3.0"
benchmarkMacroJunit4 = "1.3.3"
baselineprofile = "1.3.3"

# ui
recyclerview = "1.3.2"
constraintlayout = "2.1.4"
thinkingAnalyticsSDK = "3.0.2"
uiTooling = "1.7.8"
viewpager2 = "1.1.0"
material = "1.12.0"
baseUi = "dev-jk-5.7.5"

# 其他
shence = "4.4.0"
ex = "2.0.5"
thirdlib-acrcloud = "1.3.19"
thirdlib-shumei = "3.9.5.1"
wp-sec = "2.1.1-r1"

facebookVersion = "17.0.1"
twitter-version = "3.3.0"

flutter-ch = "1.5.0-build049-ar-release"

#阿里
alibaba-pdns = "2.2.0"
aliyun-oss = "2.9.5"
aliyun-libheif = "0.0.1"
aliyun-log = "2.6.2"

# skynet
skynet-apm = "0.0.6"
skynet-debugTools = "1.0.36"
skynet-largeimage = "1.2.10"
largeimagePlugin = "1.2.1"
skynetGradlePlugin = "1.0.0"
imageCompress-plugin = "1.0.3"
matrix-gradle-plugin = "1.1.1"
skynet-xml-proguard = "8.7.0-SNAPSHOT"
leakcanery = "2.14"

cronet = "18.0.1"
cronet-api = "124.0.6367.0"
installreferrer = "2.2"
exifinterface = "1.3.6"

# 变更日志：https://github.com/Tencent/MMKV/blob/master/CHANGELOG.md
mmkv = "1.3.9"
imagezoom = "1.0.4"
litr = "1.5.5"

# 变更日志：https://github.com/google/ExoPlayer/blob/release-v2/RELEASENOTES.md
exoplayer-version = "2.18.4"


# 客服
sobotsdk_x = "3.3.2"


# appsflyer
appsflyer = "6.14.1"
appsflyerOaid = "6.12.3"

# glide
glide = "4.16.0"
glide-compose = "1.0.0-alpha.1"
gif-drawable = "1.2.28"
lib-svga = "1.0.15"
wire-runtime = "2.3.0-RC1"
refresh-layout = "2.1.0"
largeImageView = "1.1.0"
libwebpVer = "1.0.12"
flexboxFix = "3.0.0-7"

coil = "2.4.0"
# proto
protobuf-javalite = "3.25.1"
protobufPlugin = "0.9.4"
libproto = "5.6.5-jk"
gmsPlugin = "4.4.2"

# google
playServicesAuth = "21.2.0"
billing = "7.0.0"
credentials = "1.3.0"
googleid = "1.1.1"
zxing = "3.3.3"

# google play core
asset-delivery = "2.2.2"
feature-delivery = "2.1.0"
review = "2.0.1"

# firbase
firebaseBom = "33.6.0"
firebaseCrashlyticsPlugin = "3.0.2"

#huawei
adsIdentifier = "3.4.62.300"
agconnectCore = "1.8.1.300"
huawei-iap = "6.13.0.300"
huawei-push = "6.12.0.300"
huawei-hwid = "6.12.0.300"
huawei-base = "6.12.0.300"
componentverifysdk = "13.3.1.301"

# eventbus
eventbus = "3.3.1"
eventBusAnnotationProcessor = "3.3.1"


# androidX
androidx = "1.7.0"
androidxLifecycle = "2.8.3"
lifecycleViewModelCompose = "2.6.2"
startup-runtime = "1.2.0"
dynamic-feature = "8.2.2"
androidx-webkit = "1.9.0"
work_ktx = "2.8.1"
viewbinding = "8.4.1"

# kotlin
kotlin = "2.0.20"
kotlinCore = "1.13.1"
kotlinxCoroutines = "1.7.3"

## 语音 sdk
trtcVersion = "11.7.1.16537"
## 变更日志：https://doc-zh.zego.im/article/12542
zegoVersion = "3.15.0.38714"

## class plugin
androidGradlePlugin = "8.7.2"
protobuf-gradle-plugin = "0.9.4"
agcp-plugin = "1.9.1.301"
google-services = "4.4.2"
sensorsdata-analytics-plugin = "4.0.1"
manifestplugin = "1.8.0"
vap = "1.7"
yidun_captcha = "3.6.2"
coordinatorlayout = "1.3.0"

language = "5.5.5-dev"

## ------------------------------------- libraries  --------------------------------------
[libraries]

## androidX
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activityCompose" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "androidx" }
androidx-collection = { module = "androidx.collection:collection", version.ref = "androidx-collection" }
androidx-compose-bom = { module = "androidx.compose:compose-bom", version.ref = "composeBom" }
androidx-core-runtime = { module = "androidx.arch.core:core-runtime", version.ref = "androidx-core-runtime" }
androidx-material3 = { module = "androidx.compose.material3:material3" }
androidx-profileinstaller = { module = "androidx.profileinstaller:profileinstaller", version.ref = "androidx-profileinstaller" }
androidx-ui = { module = "androidx.compose.ui:ui" }
androidx-ui-graphics = { module = "androidx.compose.ui:ui-graphics" }
androidx-ui-tooling = { module = "androidx.compose.ui:ui-tooling", version.ref = "uiTooling" }
androidx-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
viewpager2 = { module = "androidx.viewpager2:viewpager2", version.ref = "viewpager2" }
material = { module = "com.google.android.material:material", version.ref = "material" }
recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
startup-runtime = { module = "androidx.startup:startup-runtime", version.ref = "startup-runtime" }
exifinterface = { module = "androidx.exifinterface:exifinterface", version.ref = "exifinterface" }
work_ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "work_ktx" }
androidx-webkit = { module = "androidx.webkit:webkit", version.ref = "androidx-webkit" }
androidx-credentials = { group = "androidx.credentials", name = "credentials", version.ref = "credentials" }
credentials-play-services = { group = "androidx.credentials", name = "credentials-play-services-auth", version.ref = "credentials" }
androidx-viewbinding = { group = "androidx.databinding", name = "viewbinding", version.ref = "viewbinding" }

## 组件库
base-ui = { module = "com.wejoy.lib:base-ui", version.ref = "baseUi" }

## lifecycle
androidx-lifecycle-runtimektx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-livedata = { module = "androidx.lifecycle:lifecycle-livedata", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodelKtx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "androidxLifecycle" }
androidx-viewmodel-compose  = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycleViewModelCompose" }

## test
androidx-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espressoCore" }
androidx-junit = { module = "androidx.test.ext:junit", version.ref = "androidx-junit" }
junit = { module = "junit:junit", version.ref = "junit" }


facebook-login = { module = "com.facebook.android:facebook-login", version.ref = "facebookVersion" }
facebook-share = { module = "com.facebook.android:facebook-share", version.ref = "facebookVersion" }
twitter-core = { module = "com.twitter.sdk.android:twitter-core", version.ref = "twitter-version" }
twitter-composer = { module = "com.twitter.sdk.android:tweet-composer", version.ref = "twitter-version" }

## http
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
okio = { module = "com.squareup.okio:okio", version.ref = "okio" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
cronet = { module = "com.google.android.gms:play-services-cronet", version.ref = "cronet" }
cronet-api = { module = "org.chromium.net:cronet-api", version.ref = "cronet-api" }


# proto
protobuf = { module = "com.google.protobuf:protobuf-javalite", version.ref = "protobuf-javalite" }
libproto = { module = "com.wejoy.lib:libproto", version.ref = "libproto" }


# google
billingclient = { module = "com.android.billingclient:billing-ktx", version.ref = "billing" }
play-services-auth = { module = "com.google.android.gms:play-services-auth", version.ref = "playServicesAuth" }
googleid = { group = "com.google.android.libraries.identity.googleid", name = "googleid", version.ref = "googleid" }
zxing = { module = "com.google.zxing:core", version.ref = "zxing" }

google-asset-delivery = { module = "com.google.android.play:asset-delivery-ktx", version.ref = "asset-delivery" }
google-feature-delivery = { module = "com.google.android.play:feature-delivery-ktx", version.ref = "feature-delivery" }
google-review = { module = "com.google.android.play:review-ktx", version.ref = "review" }

# eventbus
eventBus = { module = "org.greenrobot:eventbus", version.ref = "eventbus" }
eventBusAnnotationProcessor = { module = "org.greenrobot:eventbus-annotation-processor", version.ref = "eventBusAnnotationProcessor" }


# glide
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glideCompiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
okhttp-integration = { module = "com.github.bumptech.glide:okhttp3-integration", version.ref = "glide" }
glide-compose = { module = "com.github.bumptech.glide:compose", version.ref = "glide-compose" }

coil = { module = "io.coil-kt:coil-compose", version.ref = "coil" }

lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }

# webp库
libwebp = { module = "com.wejoy.weplay:libwebp", version.ref = "libwebpVer" }

gif-drawable = { module = "pl.droidsonroids.gif:android-gif-drawable", version.ref = "gif-drawable" }
lib-svga = { module = "com.wejoy.lib:svga", version.ref = "lib-svga" }
# svga 序列化依赖 compile from https://git.17zjh.com/sgg-android/svga-lib
wire-runtime = { module = "com.squareup.wire:wire-runtime", version.ref = "wire-runtime" }

# appsflyer
appsflyer = { module = "com.appsflyer:af-android-sdk", version.ref = "appsflyer" }
appsflyer-oaid = { module = "com.appsflyer:oaid", version.ref = "appsflyerOaid" }
#thinkingAnalyticsSDK = { module = "cn.thinkingdata.android:ThinkingAnalyticsSDK", version.ref = "thinkingAnalyticsSDK" }
thinkingAnalyticsSDK = { module = "com.wejoy.thirdlib:ThinkingAnalyticsSDK", version.ref = "thinkingAnalyticsSDK" }

## 其他
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer" }
guava = { module = "com.google.guava:guava", version.ref = "guava" }
mmkv = { module = "com.tencent:mmkv", version.ref = "mmkv" }
imagezoom = { module = "it.sephiroth.android.library.imagezoom:library", version.ref = "imagezoom" }
largeImageView = { module = "com.wejoy.thirdlib:LargeImageView", version.ref = "largeImageView" }
sobotsdk-x = { module = "com.sobot.chat:sobotsdk_x", version.ref = "sobotsdk_x" }
ex = { module = "com.wejoy.lib:ex", version.ref = "ex" }
thirdlib-acrcloud = { module = "com.wejoy.thirdlib:acrcloud", version.ref = "thirdlib-acrcloud" }
thirdlib-shumei = { module = "com.wejoy.thirdlib:shumei", version.ref = "thirdlib-shumei" }
refresh-layout = { module = "com.wejoy.lib:refresh-layout", version.ref = "refresh-layout" }
#语音房抽奖功能定制版，不要使用这个定制版，使用上面这个。
vrlottery-flexbox = { module = "com.wejoy.lib:flexbox", version.ref = "flexboxFix" }
vap-player = { module = "com.wejoy.lib:vap", version.ref = "vap" }

# ali
aliyun-log = { module = "com.aliyun.openservices:aliyun-log-android-sdk", version.ref = "aliyun-log" }
alibaba-pdns = { module = "com.alibaba.pdns:alidns-android-sdk", version.ref = "alibaba-pdns" }
aliyun-libheif = { module = "com.aliyun:libheif", version.ref = "aliyun-libheif" }
aliyun-oss = { module = "com.aliyun.dpa:oss-android-sdk", version.ref = "aliyun-oss" }

qcloud-cos = { module = "com.qcloud.cos:cos-android-nobeacon", version = "5.9.+" }

## 语音 sdk
trtcSdk = { module = "com.tencent.liteav.customize:LiteAVSDK_Live", version.ref = "trtcVersion" }
zegoSdk = { module = "im.zego:express-private", version.ref = "zegoVersion" }

# 华为
ads-identifier = { module = "com.huawei.hms:ads-identifier", version.ref = "adsIdentifier" }
agconnect-core = { module = "com.huawei.agconnect:agconnect-core", version.ref = "agconnectCore" }
huawei-iap = { module = "com.huawei.hms:iap", version.ref = "huawei-iap" }
huawei-push = { module = "com.huawei.hms:push", version.ref = "huawei-push" }
huawei-hwid = { module = "com.huawei.hms:hwid", version.ref = "huawei-hwid" }
huawei-base = { module = "com.huawei.hms:base", version.ref = "huawei-base" }
componentverifysdk = { module = "com.huawei.hms:componentverifysdk", version.ref = "componentverifysdk" }

# exoplayer
litr = { module = "com.linkedin.android.litr:litr", version.ref = "litr" }
exoplayer-core = { module = "com.google.android.exoplayer:exoplayer-core", version.ref = "exoplayer-version" }
exoplayer-ui = { module = "com.google.android.exoplayer:exoplayer-ui", version.ref = "exoplayer-version" }
exoplayer-mediasession = { module = "com.google.android.exoplayer:extension-mediasession", version.ref = "exoplayer-version" }
exoplayer-cronet = { module = "com.google.android.exoplayer:extension-cronet", version.ref = "exoplayer-version" }


## kotlin
kotlin_core = { module = "androidx.core:core-ktx", version.ref = "kotlinCore" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm", version.ref = "kotlinxCoroutines" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinxCoroutines" }


## firebase
firebase-analytics = { module = "com.google.firebase:firebase-analytics" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging" }
firebase-crashlytics-ndk = { module = "com.google.firebase:firebase-crashlytics-ndk" }
firebase-storage = { module = "com.google.firebase:firebase-storage" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-config = { module = "com.google.firebase:firebase-config" }
firebase-perf = { module = "com.google.firebase:firebase-perf" }


## 安全库
security-dev = { module = "com.wejoy.lib:sec-dev", version.ref = "wp-sec" }
# 用android studio 直接跑 release 包时 apk 内无签名文件，这个被视为异常场景.
# 从而会导致 签名错误，这个情况 debug 包暂时不检测，可以考虑必须跑的时候，把 release 的版本换成 debug 的。
security-release = { module = "com.wejoy.lib:sec", version.ref = "wp-sec" }

wejoy-language = { module = "com.wejoy.lib:language", version.ref = "language" }

# Flutter, 编译教程 https://wepie.yuque.com/we_play/hy63pl/shln8g#obVEN
flutter-ch = { module = "com.wejoy.libwpflutter:flutter_release", version.ref = "flutter-ch" }

## class plugin
android-gradlePlugin = { module = "com.android.tools.build:gradle", version.ref = "androidGradlePlugin" }
protobuf-gradle-plugin = { module = "com.google.protobuf:protobuf-gradle-plugin", version.ref = "protobuf-gradle-plugin" }
agcp-plugin = { module = "com.huawei.agconnect:agcp", version.ref = "agcp-plugin" }
sensorsdata-analytics-plugin = { module = "com.sensorsdata.analytics.android:android-gradle-plugin2", version.ref = "sensorsdata-analytics-plugin" }
manifestplugin = { module = "com.wejoy.manifest:manifestplugin", version.ref = "manifestplugin" }

## Performance lib
skynet-mooner = { module = "com.wepie.skynet:mooner", version = "1.0.0" }
skynet-apm = { module = "com.wepie.skynet:apm", version.ref = "skynet-apm" }
skynet-debugTools = { module = "com.wepie.skynet:debugTools", version.ref = "skynet-debugTools" }
skynet-largeimage = { module = "com.wepie.skynet:largeimage", version.ref = "skynet-largeimage" }
leakcanary-plumber = { module = "com.squareup.leakcanary:plumber-android-startup", version.ref = "leakcanery" }

## Performance plugin
imagecompress-plugin = { module = "com.wepie.imagecompress:ImageCompress", version.ref = "imageCompress-plugin" }
matrix-gradle-plugin = { module = "com.wejoy.matrix:matrix-gradle-plugin", version.ref = "matrix-gradle-plugin" }
skynet-gradle-plugin = { module = "com.wepie.skynet:skynet-gradle-plugin", version.ref = "skynetGradlePlugin" }
xml-proguard-plugin = { module = "com.wepie.skynet:xml-proguard-plugin", version.ref = "skynet-xml-proguard" }
trace-plugin = { module = "com.wepie.skynet:trace-plugin", version.ref = "skynetGradlePlugin" }
largeimage-plugin = { module = "com.wejoy.largeimage:largeimage-plugin", version.ref = "largeimagePlugin" }

yidun_captcha = { module = "io.github.yidun:captcha", version.ref = "yidun_captcha" }
androidx-coordinatorlayout = { group = "androidx.coordinatorlayout", name = "coordinatorlayout", version.ref = "coordinatorlayout" }

[bundles]
firebases = ["firebase-analytics", "firebase-crashlytics", "firebase-messaging", "firebase-crashlytics-ndk", "firebase-storage"]
exoplayer = ["exoplayer-core", "exoplayer-ui", "exoplayer-mediasession"]
twitter = ["twitter-core", "twitter-composer"]

## ------------------------------------- plugins  --------------------------------------
[plugins]
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlyticsPlugin" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
protobuf = { id = "com.google.protobuf", version.ref = "protobufPlugin" }
gms = { id = "com.google.gms.google-services", version.ref = "gmsPlugin" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
dynamic-feature = { id = "com.android.dynamic-feature", version.ref = "dynamic-feature" }
androidTest = { id = "com.android.test", version.ref = "androidGradlePlugin" }
baselineprofile = { id = "androidx.baselineprofile", version.ref = "baselineprofile" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }

