package com.wepie.wespy.voiceroom

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ComposeShader
import android.graphics.LinearGradient
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.Shader
import android.graphics.Typeface
import android.os.Build
import android.util.AttributeSet
import android.view.Gravity
import com.huiwan.anim.schedule.Animate
import com.huiwan.anim.schedule.Scheduler.register
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.FontUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.decorate.NameTextView
import com.huiwan.widget.CustomTextView
import com.wepie.wespy.voiceroom.base.R
import java.lang.ref.WeakReference

/**
 * 房间id溜光组件
 * 注意⚠️：请勿设置singleLine = "true"，可能会导致没有溜光
 */
class RoomIdView : CustomTextView, Animate {

    private var inScheduler = false

    private var scene = 0

    private var needDoAnim = false

    internal var textStartPos = 0f
    private var translateX = 0f
    private var speed = 0f
    internal var measuredTextWidth = 300f
    private val matrix = Matrix()

    private var drawer: IFlashDrawer? = null

    private val isRtl = ScreenUtil.isRtl()

    private val originColor: Int

    private val flashWidth: Float

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.RoomIdView)
        scene = ta.getInt(R.styleable.RoomIdView_riv_scene, SCENE_VOICE_ROOM)
        ta.recycle()
        flashWidth = ScreenUtil.dip2px(14f).toFloat()

        originColor = currentTextColor
    }

    fun setId(rid: String) {
        setId(rid, 0)
    }

    fun setId(rid: String, level: Int) {
        text = ResUtil.getStr(R.string.voice_room_id_num, rid)
        setLevel(level)
    }

    fun setLevel(level: Int) {
        if (level <= 0) {
            FontUtil.setTextStyle(this, Typeface.NORMAL)
        } else {
            FontUtil.setTextStyle(this, Typeface.BOLD)
        }
        val color = ConfigHelper.getInstance().voiceRoomConfig.findRoomIdColor(level)
        val colorLevel = if (scene == SCENE_VOICE_ROOM) color.innerLevel else color.outerLevel
        val list = colorLevel.colorList
        if (color.level <= 0) {
            setTextColor(originColor)
        } else if (color.level > 2 || list.isEmpty()) {
            setTextColor(Color.WHITE)
        } else {
            setTextColor(ColorUtil.getColor(list[0]))
        }

        needDoAnim = color.level > 1
        if (needDoAnim) {
            val flashColorStr = colorLevel.flashColor
            post {
                updateShaper(list, flashColorStr)
                invalidate()
            }
        } else {
            cancelAnim()
        }
    }

    private fun updateShaper(colorStrList: List<String>, flashColorStr: String) {
        val colorArray: IntArray = if (colorStrList.isEmpty()) {
            intArrayOf(originColor)
        } else if (colorStrList.size > 3) {
            colorStrList.subList(0, 3).map { ColorUtil.getColor(it) }.toIntArray()
        } else {
            colorStrList.map { ColorUtil.getColor(it) }.toIntArray()
        }

        val paint: Paint = paint
        paint.color = originColor
        paint.setShadowLayer(0f, 0f, 0f, Color.TRANSPARENT)

        val flashColor = ColorUtil.getColor(flashColorStr, Color.YELLOW)

        drawer = if (colorArray.size <= 1) {
            SingleColorFlashDrawer(colorArray[0], flashColor, flashWidth)
        } else {
            MultiColorFlashDrawer(colorArray, flashColor, flashWidth)
        }
        startAnim()
    }

    private fun startAnim() {
        needDoAnim = true
        val text = text
        measuredTextWidth = paint.measureText(text, 0, text.length)
        speed = NameTextView.measureSpeed(measuredTextWidth)
        updateTextStartPos()
        updateFlash()
    }

    private fun cancelAnim() {
        paint.shader = null
        needDoAnim = false
    }

    private fun updateFlash() {
        if (needDoAnim && drawer != null) {
            if (isRtl) {
                translateX -= speed
                if (translateX < 0) {
                    translateX = width + flashWidth
                } else if (translateX < textStartPos) {
                    translateX = width - textStartPos + flashWidth
                }
            } else {
                translateX += speed
                if (translateX > width) {
                    translateX = -flashWidth
                } else if (translateX > textStartPos + measuredTextWidth) {
                    translateX = textStartPos - flashWidth
                }
            }
            matrix.reset()
            matrix.postTranslate(translateX, 0f)
            drawer?.onFlash(this, matrix)
        }
    }

    private fun updateTextStartPos() {
        val text = text
        measuredTextWidth = paint.measureText(text, 0, text.length)
        val gravity = gravity
        val start = gravity and Gravity.START == Gravity.START
        val centerH = gravity and Gravity.CENTER_HORIZONTAL == Gravity.CENTER_HORIZONTAL
        val end = gravity and Gravity.END == Gravity.END
        val left = gravity and Gravity.START == Gravity.START
        val right = gravity and Gravity.END == Gravity.END
        val useCenter = centerH && !start && !end && !left && !right
        textStartPos = if (useCenter && measuredTextWidth < width) {
            (width - measuredTextWidth) / 2
        } else {
            0f
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (isAnimRunning()) {
            if (!inScheduler) {
                register(this)
                inScheduler = true
            }
        }
    }

    override fun dispatchDraw(canvas: Canvas) {
        val sc: Int = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), paint)
        updateFlash()
        super.dispatchDraw(canvas)
        canvas.restoreToCount(sc)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updateTextStartPos()
    }

    override fun getFps(): Int = 30

    override fun isAnimRunning(): Boolean {
        return isAttachedToWindow && needDoAnim && drawer != null && width > 0 && height > 0
    }

    override fun onAnimSchedule() {
        invalidate()
    }

    override fun schedulerMayRemove(): Boolean {
        return !isAttachedToWindow || !isShown
    }

    override fun onSchedulerRemove() {
        inScheduler = false
    }

    companion object {
        const val SCENE_VOICE_ROOM = 0
        const val SCENE_USER_INFO = 1
    }
}

interface IFlashDrawer {
    fun onFlash(view: RoomIdView, matrix: Matrix)
}

private class SingleColorFlashDrawer(textColor: Int, flashColor: Int, flashWidth: Float) :
    IFlashDrawer {

    private var curUsingShader: Shader

    init {
        curUsingShader = LinearGradient(
            0f, 0f, flashWidth, flashWidth * 0.2f,
            intArrayOf(textColor, flashColor, flashColor, textColor),
            null, Shader.TileMode.CLAMP
        )
    }

    override fun onFlash(view: RoomIdView, matrix: Matrix) {
        curUsingShader.setLocalMatrix(matrix)
        view.paint.shader = curUsingShader
    }
}

private class MultiColorFlashDrawer(
    private val textColors: IntArray,
    private val flashColor: Int,
    private val flashWidth: Float
) : IFlashDrawer {

    private val curUsingShader: Shader

    private var multiColorShader: Shader? = null

    init {
        //xx dp 14 * 14 * 1.4 * (3 * 3) * 4 = 9878.4
        val bitmap: Bitmap = getComposeShaderBmp()
        curUsingShader = BitmapShader(bitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
    }

    private fun getComposeShaderBmp(): Bitmap {
        var bitmap: Bitmap? = composeShaderBmpRef?.get()
        if (bitmap != null) {
            return bitmap
        }
        bitmap = Bitmap.createBitmap(
            (flashWidth * 1.4).toInt(), flashWidth.toInt(), Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        val x1: Float = flashWidth * 0.2f
        val x2: Float = x1 + flashWidth
        paint.shader = LinearGradient(
            x1, 0f, x2, flashWidth * 0.2f,
            intArrayOf(0x00FFFFFF, flashColor, flashColor, flashColor, 0x00FFFFFF),
            null, Shader.TileMode.CLAMP
        )
        canvas.drawRect(0f, 0f, bitmap.width.toFloat(), bitmap.height.toFloat(), paint)
        composeShaderBmpRef = WeakReference(bitmap)
        return bitmap
    }

    override fun onFlash(view: RoomIdView, matrix: Matrix) {
        curUsingShader.setLocalMatrix(matrix)
        if (multiColorShader == null) {
            var shaderWidth: Float = view.measuredTextWidth
            if (view.width > 0 && view.measuredTextWidth > view.width) {
                shaderWidth = view.width.toFloat()
            }
            val shader: Shader = LinearGradient(
                view.textStartPos, 0f, view.textStartPos + shaderWidth, 0f,
                textColors, floatArrayOf(0f, 1F), Shader.TileMode.CLAMP
            )
            multiColorShader = if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N_MR1) {
                ComposeShader(shader, curUsingShader, PorterDuff.Mode.SRC_OVER)
            } else {
                shader
            }
        }
        view.paint.shader = multiColorShader
    }

    companion object {
        private var composeShaderBmpRef: WeakReference<Bitmap>? = null
    }

}