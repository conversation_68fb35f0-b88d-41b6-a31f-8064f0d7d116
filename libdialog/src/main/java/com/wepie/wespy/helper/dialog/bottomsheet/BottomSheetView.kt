package com.wepie.wespy.helper.dialog.bottomsheet

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.animation.addListener
import androidx.core.view.isVisible
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetBehavior.BottomSheetCallback
import com.huiwan.base.util.ScreenUtil

open class BottomSheetView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = -1
) : CoordinatorLayout(context, attrs, defStyleAttr) {

    sealed class BottomSheetState {

        object Disable : BottomSheetState()

        abstract class Enable(val matchParent: Boolean) : BottomSheetState() {
            internal lateinit var bottomSheetBehavior: BottomSheetBehavior<*>
        }

        class EnableOnlyExpand(matchParent: Boolean) : Enable(matchParent)
        class EnableWithCollapsed(val collapsedHeight: Int) : Enable(true) {
            fun expand2Collapsed() {
                if (bottomSheetBehavior.state == BottomSheetBehavior.STATE_COLLAPSED) {
                    return
                }
                bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
            }

            fun collapsed2Expand() {
                if (bottomSheetBehavior.state == BottomSheetBehavior.STATE_EXPANDED) {
                    return
                }
                bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            }

            fun addBottomSheetCallback(callback: BottomSheetCallback) {
                bottomSheetBehavior.addBottomSheetCallback(object : BottomSheetCallback() {
                    override fun onStateChanged(bottomSheet: View, newState: Int) {
                        callback.onStateChanged(bottomSheet, newState)
                    }

                    override fun onSlide(bottomSheet: View, slideOffset: Float) {
                        callback.onSlide(bottomSheet, slideOffset)
                    }

                })
            }
        }

    }

    private val bottomSheetList = mutableListOf<Pair<ViewGroup, Animator?>>()
    private var onHide = {}
    private lateinit var bottomSheetState: BottomSheetState

    init {
        clipChildren = false
        this.setOnClickListener {
            onHide.invoke()
        }
    }

    protected fun setBottomSheetState(state: BottomSheetState) {
        this.bottomSheetState = state
    }

    open fun setContentView(view: View, onHide: () -> Unit) {
        val bottomSheet = createBottomSheetView(view, onHide)
        bottomSheetList.add(Pair(bottomSheet, null))
        this.addView(bottomSheet)
        this.onHide = onHide
    }

    /**
     * 当已经setContentView后，通过push增加新的ui
     */
    protected fun push(enterView: View, currentSupplier: (ViewGroup) -> View, onEnd: () -> Unit) {
        val exitBottomSheet = bottomSheetList.last().first
        val exitView = currentSupplier.invoke(exitBottomSheet.getChildAt(0) as ViewGroup)
        val pairAnimator = createPushPopAnimator(enterView, exitView)
        val pushAnimator = pairAnimator.first
        pushAnimator.addListener(onEnd = {
            post {
                onEnd.invoke()
                exitBottomSheet.isVisible = false
            }
        })

        val popAnimator = pairAnimator.second
        popAnimator.addListener(onStart = {
            exitBottomSheet.isVisible = true
        })
        val bottomSheet = createBottomSheetView(enterView, onHide)
        bottomSheetList.add(Pair(bottomSheet, popAnimator))
        this.addView(bottomSheet)
        pushAnimator.start()
    }

    /**
     * 当已经push后，通过pop移除上层ui
     */
    fun pop() {
        val last = bottomSheetList.last()
        val popAnimator = last.second
        popAnimator?.addListener(onEnd = {
            bottomSheetList.removeLast()
            this.removeView(last.first)
        })
        popAnimator?.start()
    }

    fun addExtraScrollView(scrollView: View) {
        val last = bottomSheetList.last().first
        ((last.layoutParams as LayoutParams).behavior as? CustomBottomSheetBehavior)?.addExtraScrollView(
            scrollView
        )
    }

    fun removeExtraScrollView(scrollView: View) {
        val last = bottomSheetList.last().first
        ((last.layoutParams as LayoutParams).behavior as? CustomBottomSheetBehavior)?.removeExtraScrollView(
            scrollView
        )
    }

    /**
     * 创建BottomSheetView
     */
    private fun createBottomSheetView(view: View, onHide: () -> Unit): ViewGroup {
        val bottomSheet = FrameLayout(context)
        bottomSheet.setPadding(0, ScreenUtil.dip2px(12f), 0, 0)
        bottomSheet.clipChildren = false
        bottomSheet.clipToPadding = false
        val layoutParams = CoordinatorLayout.LayoutParams(
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                if (bottomSheetState is BottomSheetState.Enable &&
                    (bottomSheetState as BottomSheetState.Enable).matchParent
                ) {
                    ViewGroup.LayoutParams.MATCH_PARENT
                } else {
                    ViewGroup.LayoutParams.WRAP_CONTENT
                }
            )
        )
        if (bottomSheetState is BottomSheetState.Enable) {
            val behavior = CustomBottomSheetBehavior<ViewGroup>(context, null)
            (bottomSheetState as BottomSheetState.Enable).bottomSheetBehavior = behavior
            when (bottomSheetState) {
                is BottomSheetState.EnableOnlyExpand -> {
                    behavior.state = BottomSheetBehavior.STATE_EXPANDED
                    behavior.skipCollapsed = true
                    behavior.isHideable = true
                    behavior.addBottomSheetCallback(object :
                        BottomSheetBehavior.BottomSheetCallback() {
                        override fun onStateChanged(bottomSheet: View, newState: Int) {
                            if (newState == BottomSheetBehavior.STATE_HIDDEN ||
                                newState == BottomSheetBehavior.STATE_COLLAPSED
                            ) {
                                onHide.invoke()
                            }
                        }

                        override fun onSlide(bottomSheet: View, slideOffset: Float) = Unit

                    })
                }

                is BottomSheetState.EnableWithCollapsed -> {
                    val collapsedState = bottomSheetState as BottomSheetState.EnableWithCollapsed
                    behavior.state = BottomSheetBehavior.STATE_COLLAPSED
                    behavior.peekHeight = collapsedState.collapsedHeight
                    behavior.isHideable = true
                    behavior.addBottomSheetCallback(object :
                        BottomSheetBehavior.BottomSheetCallback() {
                        override fun onStateChanged(bottomSheet: View, newState: Int) {
                            if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                                onHide.invoke()
                            }
                        }

                        override fun onSlide(bottomSheet: View, slideOffset: Float) = Unit
                    })
                }

                else -> Unit
            }
            layoutParams.behavior = behavior
        }
        bottomSheet.layoutParams = layoutParams
        bottomSheet.addView(
            view,
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )
        return bottomSheet
    }

    /**
     * 创建push和pop的动画
     */
    private fun createPushPopAnimator(
        enterView: View,
        exitView: View
    ): Pair<Animator, Animator> {
        val duration = 250L
        val pushStartX = if (ScreenUtil.isRtl()) {
            -1f
        } else {
            1f
        } * ScreenUtil.getScreenWidth()

        val pushAnimator = ValueAnimator.ofFloat(0f, 1f)
        pushAnimator.duration = duration
        val exitViewHeight = exitView.height
        pushAnimator.addUpdateListener {
            val animatedValue = it.animatedValue as Float
            enterView.translationX = pushStartX * (1 - animatedValue)
            enterView.translationY =
                (enterView.measuredHeight - exitViewHeight) * (1 - animatedValue)
            exitView.translationY = (exitViewHeight - enterView.measuredHeight) * animatedValue
        }

        var popEndY = -1F
        val popAnimator = ValueAnimator.ofFloat(0f, 1f)
        popAnimator.duration = duration
        popAnimator.addUpdateListener {
            if (popEndY < 0) {
                popEndY = -exitView.translationY
            }
            val animatedValue = it.animatedValue as Float
            enterView.translationX = pushStartX * animatedValue
            enterView.translationY = popEndY * animatedValue
            exitView.translationY = -popEndY * (1 - animatedValue)
        }
        return Pair(pushAnimator, popAnimator)
    }
}