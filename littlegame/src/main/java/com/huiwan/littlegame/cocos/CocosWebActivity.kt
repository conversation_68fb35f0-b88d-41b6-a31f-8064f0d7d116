package com.huiwan.littlegame.cocos

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.MutableContextWrapper
import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.widget.FrameLayout
import androidx.core.view.isVisible
import com.huiwan.barrage.BarrageAnimView
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.InitializerManagerUtils
import com.huiwan.base.util.StatusBarUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.VibrateUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.component.gift.GiftAnimUtil
import com.huiwan.component.gift.show.GiftContentView
import com.huiwan.component.gift.show.GiftSendScene
import com.huiwan.component.gift.show.GiftShowInfo
import com.huiwan.component.gift.show.ICommonAnimData
import com.huiwan.component.gift.show.OnAnimListener
import com.huiwan.configservice.ConfigHelper
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.impl
import com.huiwan.lib.api.plugins.IAppNotify
import com.huiwan.lib.api.plugins.ICompetitionApi
import com.huiwan.littlegame.R
import com.huiwan.littlegame.aa.CocosRoomSendView
import com.huiwan.littlegame.aa.GiftShowConfigHelperLittleGame
import com.huiwan.littlegame.cocos.CocosLaunchInfo.Companion.ENTER_MODE_JUMP_GAME
import com.huiwan.littlegame.cocos.apis.JoinScene
import com.huiwan.littlegame.cocos.apis.registerRelaunchHelper
import com.huiwan.littlegame.model.CocosRoomMsg
import com.huiwan.littlegame.model.CocosSendInfo
import com.huiwan.littlegame.net.ChannelInfo
import com.huiwan.littlegame.util.CocosSoundUtil
import com.huiwan.littlegame.util.CocosVoiceUtil
import com.huiwan.littlegame.util.CommonUtil
import com.huiwan.littlegame.view.DialogShowUtil
import com.huiwan.littlegame.view.ICocosRoomSendView
import com.huiwan.littlegame.view.ISendView
import com.huiwan.littlegame.view.LandScapeSendView
import com.huiwan.littlegame.view.RoomSendCallback
import com.huiwan.littlegame.view.dialog.CocosShareInMainActivity
import com.huiwan.littlegame.view.dialog.IceBallDialogUtil
import com.huiwan.littlegame.view.loading.ILittleGameLoadingWidget
import com.huiwan.littlegame.view.loading.genLoadingView
import com.huiwan.media.VolumeUtil
import com.huiwan.user.LoginHelper
import com.huiwan.voiceservice.VoiceManager
import com.wejoy.littlegame.ILittleGameApi
import com.wejoy.littlegame.LittleGame
import com.wejoy.littlegame.LittleGame.subInitFromIntent
import com.wejoy.littlegame.LittleGameInfo
import com.welib.alinetlog.AliNetLogUtil
import com.wepie.liblog.main.FLog
import com.wepie.liblog.main.HLog
import com.wepie.libpermission.PermissionCallback
import com.wepie.startup.InitializerManager
import java.io.File

class CocosWebActivity : BaseActivity(), IAppNotify.INotifyPage, ILittleGameApi.IDialogManagePage {
    private lateinit var cocosWebView: CocosWebView
    private var loadingView: ILittleGameLoadingWidget? = null
    private lateinit var rootView: FrameLayout
    private var barrageAnimView: BarrageAnimView? = null
    private var sendView: ISendView? = null
    private var giftContentView: GiftContentView? = null
    private val presenter: CocosWebPresenter = CocosWebPresenter(this)
    private val appNotifyHandler: CocosAppNotifyHandler = CocosAppNotifyHandler()
    private lateinit var voiceChannelHelper: CocosVoiceChannelHelper
    private var isFinish = false
    private lateinit var vibrateUtil: VibrateUtil
    private var cocosStartLoadingTime = 0L
    private val showingDialogs = mutableListOf<Dialog>() // 当前正在展示的弹窗列表。
    var curLaunchInfo: CocosLaunchInfo? = null
        private set
    val curGameType: Int
        get() = curLaunchInfo?.gameType ?: 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ContextUtil.setKeepScreenOn(this, true)
        StatusBarUtil.initStatusBar(this, false)
        val root = findViewById<View>(android.R.id.content)
        if (root is FrameLayout) {
            rootView = root
        } else {
            rootView = FrameLayout(this)
            setContentView(rootView)
        }
        handlerIntent(intent)
        cocosTrackLaunch(curGameType)
        cocosWebView = openWebView()
        voiceChannelHelper = CocosVoiceChannelHelper(this, cocosWebView)
        initView()

        presenter.init()
        initGame()
        CocosSoundUtil.releaseAll()
        vibrateUtil = VibrateUtil(this)
        ApiService.of(ICompetitionApi::class.java).registerEnterCompetitionCallback(this)
    }

    private fun handlerIntent(intent: Intent) {
        try {
            val launchInfo = CocosLaunchInfo.fromIntent(intent)
            if (launchInfo.enterMode == ENTER_MODE_JUMP_GAME) {
                val success = subInitFromIntent(intent)
                if (!success) {
                    handleIntentError(IllegalArgumentException("GameInfoError"))
                    return
                }
                launchInfo.rid = LittleGame.gameInfo.rid
            }
            curLaunchInfo = launchInfo
            LittleGame.gameInfo.gameType = curGameType
            LittleGame.teamInfo.game_type = curGameType
        } catch (e: Exception) {
            FLog.e(e)
            handleIntentError(e)
        }
    }

    private fun handleIntentError(e: Exception) {
        HLog.e(TAG, HLog.USR, "parse from intent failed {}, {}", intent.extras, e)
        ToastUtil.show(R.string.cocos_little_game_config_error)
        finish()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handlerIntent(intent)
        cocosWebView.launchInfo = curLaunchInfo ?: CocosLaunchInfo()
        cocosWebView.callHandler("OnUpdateLoadData", "{}", null)
    }

    private fun openWebView(): CocosWebView {
        val webView: CocosWebView = CocosWebView.getWebViewFromCache(this, CocosWebViewJsbConfig())
        visitWebView(webView, LittleGame.gameInfo, curLaunchInfo, curGameType)
        webView.scene = SCENE_LITTLE_GAME
        logCocosStart()
        return webView
    }

    private fun logCocosStart() {
        cocosStartLoadingTime = System.currentTimeMillis()
        val map = mapOf(
            "name" to "cocos_web_start",
            "scene" to "little_game"
        )
        HLog.d(TAG, HLog.USR, "cocos loading start: {}", map)
        HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "", map)
    }

    private fun logLoadCocosSuccess() {
        if (cocosStartLoadingTime > 0) {
            // cocos中会有多个场景调用loadSuccess，但其实我们只关注首次加载url的耗时
            val map = mutableMapOf(
                "name" to "cocos_web_success",
                "scene" to "little_game",
                "duration" to (System.currentTimeMillis() - cocosStartLoadingTime).toString()
            )
            cocosStartLoadingTime = 0
            HLog.d(TAG, HLog.USR, "cocos loading success: {}", map)
            HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "", map)
        }
    }

    private fun initView() {
        rootView.addView(cocosWebView, 0, ViewGroup.LayoutParams(MATCH_PARENT, MATCH_PARENT))

        val loadingView = genLoadingView(this)
        rootView.addView(loadingView.androidView, MATCH_PARENT, MATCH_PARENT)
        this.loadingView = loadingView
    }

    private fun initLazyView() {
        if (barrageAnimView == null) {
            barrageAnimView = BarrageAnimView(this)
            rootView.addView(barrageAnimView, MATCH_PARENT, MATCH_PARENT)
        }

        if (giftContentView == null) {
            giftContentView = GiftContentView(this)
            rootView.addView(giftContentView, MATCH_PARENT, MATCH_PARENT)
        }
        if (sendView == null) {
            initSendView()
        }
    }

    private fun initSendView() {
        val view: View = if (LittleGame.gameInfo.isPortrait) {
            CocosRoomSendView(this).apply {
                layoutParams = ViewGroup.LayoutParams(MATCH_PARENT, MATCH_PARENT)
            }
        } else {
            LandScapeSendView(this).apply {
                layoutParams = FrameLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT, Gravity.BOTTOM)
            }
        }
        if (view is ISendView) {
            view.setInputLimit(30)
            view.registerRoomSendCallback(object : RoomSendCallback {
                override fun onSendMsg(sendInfo: CocosSendInfo?) {
                    sendInfo ?: return
                    presenter.sendMsg(sendInfo)
                }

            })
            sendView = view
        }
        view.isVisible = false
        rootView.addView(view)
    }

    private fun initGame() {
        ILittleGameApi::class.impl().notifyUpdateCocosGameStatus(true)
        loadingView?.update(curGameType, curLaunchInfo?.isCocosMatch == true)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        HLog.d(TAG, "onActivityResult: {}, {}", requestCode, resultCode)
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == CocosShareInMainActivity.COCOS_SHARE_FROM_MAIN_CODE &&
            resultCode == CocosShareInMainActivity.COCOS_SHARE_FROM_MAIN_CODE &&
            data != null
        ) {
            val type = data.getIntExtra("type", 0)
            callShareCallback(type, 200, cocosWebView)
        }
    }

    private fun refreshAgoraState() {
        CocosVoiceUtil.requestChannelWithWeb { data: ChannelInfo ->
            if (!LittleGame.gameInfo.isWatcher) {
                if (CommonUtil.isAgoraOn()) {
                    openAgora(CommonUtil.isMicConfigOn(), data)
                } else {
                    closeAgora()
                }
            } else {
                if (CommonUtil.isAgoraOn()) {
                    openAgora(false, data)
                } else {
                    closeAgora()
                }
                CommonUtil.closeMic()
            }
        }
    }

    private fun openAgora(openMic: Boolean, data: ChannelInfo) {
        val forbidVoice = CocosVoiceUtil.getForbidVoice()
        if (forbidVoice) {
            HLog.d("voiceManager", "openAgora return, data: {}", data)
            return
        }
        val successType = CommonUtil.changeChannel(
            CocosVoiceUtil.getCurVoiceType(),
            CocosVoiceUtil.getCurChannel(LittleGame.gameInfo.rid),
            openMic,
            LittleGame.gameInfo.isWatcher
        )
        if (successType == 0) {
            callJoinChannelSuccess(cocosWebView)
        }

    }

    private fun closeAgora() {
        CommonUtil.leaveChannel()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        loadingView?.resizeIfNeed()
    }

    fun showNormalBarrageMsg(msg: CocosRoomMsg) {
        callOnPushRoomMsg(msg, cocosWebView)
    }

    fun showGiftBarrage(msg: CocosRoomMsg) {
        GiftAnimUtil.getGiftApi().getBarrageContent(msg.giftInfo.toGiftShowInfo()) { text ->
            barrageAnimView?.startAnim(text, BarrageAnimView.TYPE_GIFT)
        }
    }

    fun hideLoading() {
        loadingView?.removeFromParentPending()
        logLoadCocosSuccess()
        loadingView = null
        initLazyView()
    }

    fun doShare(topic: String?) {
        val file = File(cocosWebView.path, "game.png")
        IceBallDialogUtil.showGameShareDialog(this, file.absolutePath, curGameType, topic)
    }

    fun shake(time: Int) {
        vibrateUtil.vibrate2(time)
    }

    fun showGiftAnim(showInfo: GiftShowInfo?) {
        if (showInfo == null) {
            return
        }
        giftContentView?.showGiftAnim(showInfo)
        if (!TextUtils.isEmpty(showInfo.barrageAnimStr)) {
            barrageAnimView?.startAnim(showInfo.barrageAnimStr, BarrageAnimView.TYPE_GIFT)
        }
    }

    fun showPropAnim(showInfo: ICommonAnimData?, funcId: String) {
        showInfo?.let {
            val animListener: OnAnimListener = object : OnAnimListener {
                override fun onAnimStart(showInfo: ICommonAnimData) {}

                override fun onAnimEnd(showInfo: ICommonAnimData) {
                    callOnPlayVideoEffectStatus(funcId, true, cocosWebView)
                }

                override fun onFailed(showInfo: ICommonAnimData?) {
                    callOnPlayVideoEffectStatus(funcId, false, cocosWebView)
                }
            }
            giftContentView?.showPropAnim(it, animListener)
        }
    }

    fun hideSendView() {
        sendView?.onHide()
    }

    fun clearEditText() {
        sendView?.clearEditText()
    }

    fun showSendView(showSourceSwitch: Boolean, defaultSource: Int) {
        if (sendView is ICocosRoomSendView) {
            (sendView as ICocosRoomSendView).onShow(showSourceSwitch, defaultSource)
        } else {
            sendView?.onShow()
        }
    }

    fun showSendViewOnce(
        showSourceSwitch: Boolean,
        defaultSource: Int,
        btnName: String,
        hint: String,
        callback: RoomSendCallback
    ) {
        if (sendView is ICocosRoomSendView) {
            (sendView as ICocosRoomSendView).onShowOnce(
                showSourceSwitch, defaultSource, btnName, hint, callback
            )
        } else {
            sendView?.onShow()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        clear()
        HLog.d(TAG, HLog.USR, "onDestroy")
        cocosWebView.destroy()
        if (cocosWebView.context is MutableContextWrapper) {
            (cocosWebView.context as MutableContextWrapper).baseContext = applicationContext
        }
    }

    override fun onPause() {
        super.onPause()
        if (isFinishing) {
            clear()
        }
    }

    private fun clear() {
        if (isFinish) return
        isFinish = true
        CommonUtil.leaveAndRelease()
        CocosSoundUtil.releaseAll()
        CocosVoiceUtil.clear()
        presenter.destroy()
        voiceChannelHelper.leaveChannel()
        if (ConfigHelper.getInstance().getGameConfig(curGameType).isSupportCocosMatch) {
            LittleGame.clear()
            curLaunchInfo = null
        } else {
            LittleGame.clearGameInfo()
        }
        CocosBridgeInterface.callJsGameExit(cocosWebView)
        ILittleGameApi::class.impl().notifyUpdateCocosGameStatus(false)
        cocosTrackClear()
    }

    fun callRefreshSelfInfo() {
        callRefreshSelfInfo(cocosWebView)
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        callActivityReturn(cocosWebView)
    }

    override fun supportFloatView(): Int {
        return 0
    }

    fun callRefreshUserVolume(json: String?) {
        callRefreshUserVolume(json, cocosWebView)
    }

    override fun filterStartup() {
        super.filterStartup()
        InitializerManagerUtils.wait(InitializerManager.FILTER_FOUR)
    }

    fun checkRefreshAgoraState() {
        if (curLaunchInfo?.enterMode == ENTER_MODE_JUMP_GAME) {
            refreshAgoraState()
            CocosSoundUtil.setVolume(
                VolumeUtil.getMediaVolume() / 100f,
                VolumeUtil.getGameBgmMediaVolume() / 100f
            )
        }
    }

    /**
     * 如果没有麦克风权限，则申请。
     * 申请成功后，重新加入频道，通知 cocos 内更新
     */
    fun checkRequestAudioPermission() {
        requestGameAudioPermission(this, object :
            PermissionCallback {
            override fun hasPermission(
                granted: MutableList<String>?,
                isAll: Boolean,
                alreadyHas: Boolean
            ) {
                if (!alreadyHas) {
                    // 前面没有权限，现在用户授权了。重新加入频道，通知 cocos 状态发生变化。
                    VoiceManager.getInstance().rejoinChannel()
                    callAgoraStatusChange(cocosWebView)
                }
            }

            override fun noPermission(denied: MutableList<String>?, quick: Boolean) {
                showCocosPermissionDenyTip(this@CocosWebActivity, quick)
            }
        })
    }

    fun showUserDialog(uid: Int, subSource: String) {
        val dialog = DialogShowUtil.showUserInfoDialogInWeb(this, uid, subSource) { callback ->
            callGetSendGiftInfo(cocosWebView) { rsp ->
                if (!rsp.isOk()) {
                    ToastUtil.show(rsp.msg)
                } else {
                    val showConfig =
                        GiftShowConfigHelperLittleGame.littleGame(LittleGame.gameInfo.rid, uid)
                    showConfig.sendScene = GiftSendScene.SceneCocosTeam.value
                    callback.invoke(
                        ILittleGameApi.LittleGameSendGiftConfig(
                            showConfig,
                            notifyUidList = rsp.data?.notifyUids
                                ?: listOf(LoginHelper.getLoginUid()),
                            trackInfo = rsp.data?.trackInfo ?: emptyMap(),
                            commonSend = LittleGame.gameInfo.rid <= 0
                        )
                    )
                }
            }
        }
        recordShowingDialog(dialog)
    }

    override fun canAppNotify(info: IAppNotify.IAppNotifyInfo, callback: (Boolean) -> Unit) {
        // 待获取 cocos 信息。
        appNotifyHandler.checkCanAppNotify(info, callback)
    }

    /**
     * 记录当前展示的弹窗
     */
    override fun recordShowingDialog(dialog: Dialog) {
        showingDialogs.add(dialog)
    }

    override fun clearRecordDialogs() {
        for (d in showingDialogs) {
            if (d.isShowing) {
                d.dismiss()
            }
        }
        showingDialogs.clear()
    }

    fun joinVoiceChannel(channelName: String, voiceType: Int, @JoinScene gameVoiceScene: Int) {
        voiceChannelHelper.joinChannel(channelName, voiceType, gameVoiceScene)
    }

    fun leaveVoiceChannel() {
        voiceChannelHelper.leaveChannel()
    }

    companion object {
        private const val TAG = "CocosWebActivity"

        init {
            registerRelaunchHelper()
        }

        @JvmStatic
        fun getIntent(context: Context, enterMode: Int): Intent {
            return Intent(context, CocosWebActivity::class.java).apply {
                putExtra(CocosLaunchInfo.ENTER_MODE, enterMode)
            }
        }

        private fun visitWebView(
            cocosWebView: CocosWebView,
            gameInfo: LittleGameInfo,
            curLaunchInfo: CocosLaunchInfo?,
            curGameType: Int
        ) {
            val path: String = getUnpackDir(curGameType, true)
            val url: String = wrapUrl(path)
            HLog.d(
                TAG,
                HLog.USR,
                "visit web view {}, {}, {}, {}",
                gameInfo.rid,
                gameInfo.hashCode(),
                cocosWebView.hashCode(),
                url
            )
            cocosWebView.gameInfo = gameInfo
            cocosWebView.path = path
            cocosWebView.startTimeMs = System.currentTimeMillis()
            cocosWebView.launchInfo = curLaunchInfo ?: CocosLaunchInfo()
            cocosWebView.visitUrl(url)
        }
    }
}