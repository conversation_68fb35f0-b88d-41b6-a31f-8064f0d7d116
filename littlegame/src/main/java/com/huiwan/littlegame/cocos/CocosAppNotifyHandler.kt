package com.huiwan.littlegame.cocos

import com.huiwan.lib.api.plugins.IAppNotify
import com.wejoy.littlegame.LittleGame

/**
 * 处理全服弹幕的通知。
 */
class CocosAppNotifyHandler {

    /**
     * 需求上调整为在组队中或匹配中或游戏中时都不展示全服弹幕
     */
    fun checkCanAppNotify(info: IAppNotify.IAppNotifyInfo, callback: (Boolean) -> Unit) {
        val inGame = LittleGame.gameInfo.rid > 0
        val inTeam = LittleGame.teamInfo.tid > 0

        if (inGame || inTeam) {
            callback.invoke(isSupportInCocosGame(info))
        } else {
            callback.invoke(true)
        }
    }


    companion object {
        @JvmStatic
        fun isSupportInCocosGame(info: IAppNotify.IAppNotifyInfo): Boolean {
            return info.getScene() == IAppNotify.SCENE_COCOS_SKIN || info.getScene() == IAppNotify.SCENE_ONLY_GAME
        }
    }
}