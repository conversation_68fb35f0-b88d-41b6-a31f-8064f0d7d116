package com.huiwan.lib.api.plugins

import com.huiwan.lib.api.Api

interface IAppNotify : Api {
    interface INotifyPage {
        /**
         * 当前界面是否支持全服通知的展示。
         * @param callback 通过异步回调来给出结果。
         */
        fun canAppNotify(info: IAppNotifyInfo, callback: (Boolean) -> Unit)

    }

    /**
     * 清除所有的全服弹幕
     * 一般是游戏页内触发。
     */
    fun clearAll()

    interface IAppNotifyInfo {
        fun getScene(): Int = 0
    }

    companion object {
        const val SCENE_DEFAULT = 0

        /**
         * 游戏装扮（需cocos展示）
         */
        const val SCENE_COCOS_SKIN = 1

        /**
         * 仅游戏场景 (本地场景，服务器没有对应的数据)
         */
        const val SCENE_ONLY_GAME = 2
    }
}