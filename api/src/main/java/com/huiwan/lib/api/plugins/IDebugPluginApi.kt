package com.huiwan.lib.api.plugins

import android.app.Application
import android.webkit.WebView
import com.huiwan.lib.api.Api

/**
 * debug 工具api
 * <AUTHOR>
 * @since 2023/4/3 22:20
 */
interface IDebugPluginApi : Api {
    /**
     * 初始化 配置
     */
    fun initConfig(context: Application)

    /**
     * 初始化 webView
     */
    fun initWebView(mWebView: WebView?)

    /**
     * 显示当前页面信息
     */
    fun showCurPageMsg(key: String, value: String)

    /**
     * 显示当前页面信息
     */
    fun showCurPageMsg(params: Map<String?, String?>?)

    /**
     * 发起网络请求
     * @param url 请求 url
     * @param params 请求参数
     */
    fun monitorRequestNet(url: String?, params: Map<String?, String?>?)

    /**
     * tcp 数据
     * @param content 内容
     */
    fun monitorTcp(desc: String?, content: String?)

    /**
     * 网络请求回来 成功 or 失败
     * @param success 请求是否成功
     * @param data 数据部分
     * @param url 请求 url
     */
    fun monitorResponseNet(success: Boolean, data: String?, url: String?)

    /**
     * 日志监控
     * @param type 类型  {@link Log.V}
     * @param tag 标识
     * @param console 输出内容
     */
    fun monitorLog(type: String?, tag: String?, console: String?)

    /**
     * 资源加载完成
     * @param resource 资源类型
     * @param url 地址
     * @param target
     */
    fun onResourceReady(resource: Any?, url: Any?, target: Any?)

    fun mockTcp(cmd: Int, type: Int, callback: Any?): Boolean
}