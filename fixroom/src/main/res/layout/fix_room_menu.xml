<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/sel_ffffff_corner8"
    android:orientation="vertical">

    <TextView
        android:id="@+id/menu_stand_up_tv"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        android:text="@string/fix_room_menu_spectate"
        android:textColor="#1B1D38"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/menu_game_voice_tv"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        android:text="@string/fix_room_menu_volume"
        android:textColor="#1B1D38"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/menu_game_explain_tv"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        android:text="@string/fix_room_menu_rules"
        android:textColor="#1B1D38"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/menu_game_exit_tv"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        android:text="@string/fix_room_menu_exit"
        android:textColor="#1B1D38"
        android:textSize="16sp" />

</LinearLayout>