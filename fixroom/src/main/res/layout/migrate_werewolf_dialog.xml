<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/migrate_dialog_content_lay"
        android:layout_width="303dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_ffffff_corner8"
        android:orientation="vertical"
        android:paddingTop="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:text="@string/migrate_dialog_title"
            android:textColor="@color/color_primary_dark"
            android:textSize="18dp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="250dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="14dp"
            android:lineSpacingExtra="2dp"
            android:text="@string/migrate_dialog_tx_1"
            android:textColor="@color/color_primary_dark"
            android:textSize="14dp" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="24dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/certificate_sure_btn"
                android:layout_width="120dp"
                android:layout_height="40dp"

                android:background="@drawable/sel_accent_corner100"
                android:gravity="center"
                android:text="@string/migrate_dialog_enter"
                android:textColor="#ffffff"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/certificate_cancel_btn"
                android:layout_width="120dp"
                android:layout_height="40dp"
                android:layout_marginStart="10dp"
                android:layout_marginLeft="12dp"
                android:background="@drawable/sel_stroke_accent_corner100"
                android:gravity="center"
                android:text="@string/migrate_dialog_cancel"
                android:textColor="@color/color_accent_ex"
                android:textSize="16dp" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>