package com.github.lzyzsd.jsbridge;

import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.webkit.WebMessageCompat;
import androidx.webkit.WebViewCompat;
import androidx.webkit.WebViewFeature;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 继承自系统WebView，在X5 WebView加载白屏后会切到这个WebView
 */

public class BridgeOriginWebView extends android.webkit.WebView implements BridgeInterface {

    private final Map<String, CallBackFunction> responseCallbacks = new HashMap<>();
    private final Map<String, BridgeHandler> messageHandlers = new HashMap<>();

    private List<Message> startupMessage = new ArrayList<>();

    private String jsObjectName = "";
    private boolean supportCompat = false;

    private long uniqueId = 0;

    private Handler handler;
    private JsMessageHandler jsMessageHandler;

    public BridgeOriginWebView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public BridgeOriginWebView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    public BridgeOriginWebView(Context context) {
        super(context);
        init();
    }

    private void init() {
        this.setVerticalScrollBarEnabled(false);
        this.setHorizontalScrollBarEnabled(false);
        getSettings().setJavaScriptEnabled(true);

        Looper looper = Looper.myLooper();
        if (looper == null) {
            looper = Looper.getMainLooper();
        }
        handler = new Handler(looper);
        jsMessageHandler = new JsMessageHandler(handler);
    }

    public void openMessageQueue(boolean open) {
        if (!open || !WebViewFeature.isFeatureSupported(WebViewFeature.WEB_MESSAGE_LISTENER)) {
            return;
        }
        jsObjectName = "app";
        // 添加 WebMessageListener
        Set<String> set = new HashSet<>();
        set.add("*");
        WebViewCompat.addWebMessageListener(this, jsObjectName, set, (view, message, sourceOrigin, isMainFrame, replyProxy) -> {
            // 收到网页消息，处理并回复
            if (message.getType() == WebMessageCompat.TYPE_STRING) {
                Message msg = Message.toObject(message.getData());
                if (BridgeUtil.CHECK_COMPAT.equals(msg.getHandlerName())) {
                    if (WebViewFeature.isFeatureSupported(WebViewFeature.POST_WEB_MESSAGE)) {
                        msg.setData(Boolean.TRUE.toString());
                        postMessage(msg);
                        supportCompat = true;
                    }
                } else {
                    handleMessage(msg);
                }
                msg.recycle();
            }
        });
    }

    private void doSend(String handlerName, String data, CallBackFunction responseCallback) {
        Message m = Message.obtain();
        if (!TextUtils.isEmpty(data)) {
            m.setData(data);
        }
        if (responseCallback != null) {
            String callbackStr = String.format(BridgeUtil.CALLBACK_ID_FORMAT, ++uniqueId + (BridgeUtil.UNDERLINE_STR + SystemClock.currentThreadTimeMillis()));
            responseCallbacks.put(callbackStr, responseCallback);
            m.setCallbackId(callbackStr);
        }
        if (!TextUtils.isEmpty(handlerName)) {
            m.setHandlerName(handlerName);
        }
        doQueueMessage(m);
        m.recycle();

    }

    public void queueMessage(Message m) {
        doQueueMessage(m);
        m.recycle();
    }

    private void doQueueMessage(Message m) {
        if (startupMessage != null) {
            startupMessage.add(m.clone());
        }
        if (handler.getLooper().getThread() == Thread.currentThread()) {
            dispatchMessage(m);
        }
    }

    private void dispatchMessage(Message m) {
        if (supportCompat) {
            postMessage(m);
            return;
        }
        String messageJson = m.toJson();
        //escape special characters for json string
        messageJson = messageJson.replaceAll("(\\\\)([^utrn])", "\\\\\\\\$1$2");
        messageJson = messageJson.replaceAll("(?<=[^\\\\])(\")", "\\\\\"");
        String javascriptCommand = String.format(BridgeUtil.JS_HANDLE_MESSAGE_FROM_JAVA, messageJson);
        this.evaluateJavascript(javascriptCommand, null);
    }

    public void supportHandler(String handlerName, ICallback<Boolean> function) {
        String javascriptCommand = String.format(BridgeUtil.JS_HANDLE_EXIST, handlerName);
        this.evaluateJavascript(javascriptCommand, value -> function.onCallBack("true".equals(value)));
    }

    private void handleMessage(Message m) {
        String responseId = m.getResponseId();
        // 是否是response
        if (!TextUtils.isEmpty(responseId)) {
            CallBackFunction function = responseCallbacks.remove(responseId);
            if (function != null) {
                String responseData = m.getResponseData();
                function.onCallBack(responseData);
            }
            return;
        }
        CallBackFunction responseFunction;
        // if had callbackId
        final String callbackId = m.getCallbackId();
        if (!TextUtils.isEmpty(callbackId)) {
            responseFunction = data -> {
                Message responseMsg = Message.obtain();
                responseMsg.setResponseId(callbackId);
                responseMsg.setResponseData(data);
                doQueueMessage(responseMsg);
                responseMsg.recycle();
            };
        } else {
            responseFunction = data -> {
                // do nothing
            };
        }
        BridgeHandler handler = null;
        if (!TextUtils.isEmpty(m.getHandlerName())) {
            handler = messageHandlers.get(m.getHandlerName());
        } else {
            // no handler found
        }
        if (handler != null) {
            handler.handler(m.getData(), responseFunction);
        }
    }

    private void fetchQueue() {
        this.evaluateJavascript(BridgeUtil.JS_FETCH_QUEUE_FROM_JAVA, null);
        responseCallbacks.put(BridgeUtil.FETCH_QUEUE, data -> {
            List<Message> list;
            try {
                list = Message.toArrayList(data);
            } catch (Exception e) {
                return;
            }
            if (list.isEmpty()) {
                return;
            }
            for (int i = 0; i < list.size(); i++) {
                Message m = list.get(i);
                handleMessage(m);
                m.recycle();
            }
        });
    }

    private void postMessage(Message msg) {
        WebMessageCompat webMessage = new WebMessageCompat(msg.toJson());
        WebViewCompat.postWebMessage(BridgeOriginWebView.this, webMessage, Uri.parse("*"));
    }

    public void loadUrl(String jsUrl, CallBackFunction returnCallback) {
        this.evaluateJavascript(jsUrl, null);
        responseCallbacks.put(BridgeUtil.parseFunctionName(jsUrl), returnCallback);
    }

    /**
     * register handler,so that javascript can call it
     */
    public void registerHandler(String handlerName, BridgeHandler handler) {
        if (handler != null) {
            messageHandlers.put(handlerName, handler);
        }
    }

    public boolean checkIsSupportBridge(String handlerName) {
        return messageHandlers.get(handlerName) != null;
    }

    /**
     * call javascript registered handler
     */
    public void callHandler(String handlerName, String data, CallBackFunction callBack) {
        doSend(handlerName, data, callBack);
    }

    public void clearMessage() {
        if (responseCallbacks != null) responseCallbacks.clear();
        if (messageHandlers != null) messageHandlers.clear();
        if (startupMessage != null) startupMessage.clear();
        if (handler != null) handler.removeCallbacksAndMessages(null);
        if (!TextUtils.isEmpty(jsObjectName)) {
            WebViewCompat.removeWebMessageListener(this, jsObjectName);
            jsObjectName = "";
        }
    }

    public void registerAndroidJs(@NonNull String name, @NonNull String path) {
        addJavascriptInterface(new BridgeJavascriptInterface(jsMessageHandler, path), name);
    }

    private class JsMessageHandler implements BridgeMessageInterface {
        private final Handler handler;

        public JsMessageHandler(Handler handler) {
            this.handler = handler;
        }

        @Override
        public Context getContext() {
            return BridgeOriginWebView.this.getContext();
        }

        @Override
        public void onJsLoad(String path) {
            String jsContent = BridgeUtil.assetFile2Str(getContext(), path);
            handler.post(() -> {
                loadUrl("javascript:" + jsContent);
                if (startupMessage != null) {
                    for (Message m : startupMessage) {
                        BridgeOriginWebView.this.dispatchMessage(m);
                    }
                }
                startupMessage = null;
            });
        }

        @Override
        public void dispatchMessage(Message m) {
            handler.post(() -> BridgeOriginWebView.this.dispatchMessage(m));
        }

        @Override
        public void handlerReturnData(String functionName, String data) {
            handler.post(() -> {
                CallBackFunction f = responseCallbacks.remove(functionName);
                if (f != null) {
                    f.onCallBack(data);
                }
            });
        }

        @Override
        public void flushMessageQueue() {
            handler.post(BridgeOriginWebView.this::fetchQueue);
        }
    }
}
