package com.wejoy.weplay.shumei

import android.content.Context
import android.os.SystemClock
import com.huiwan.base.util.StringUtil
import com.huiwan.platform.ThreadUtil
import com.ishumei.smantifraud.SmAntiFraud
import com.ishumei.smantifraud.SmAntiFraud.IServerSmidCallback
import com.ishumei.smantifraud.SmAntiFraud.SmOption
import com.wepie.liblog.main.HLog
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 数美 文档
 * https://help.ishumei.com/docs/tw/sdk/android/developDoc
 *
 * 隔离进程的兼容性存在问题。 后续再考虑怎么处理。
 * Android 当前版本，不会再收集所有的已安装应用
 */
object ShumeiUtil {
    /**
     * sdk 内使用 Smlog, 这里保持类似方便过滤
     */
    private const val TAG = "SmlogW"

    private const val PUBLIC_KEY =
        "MIIDOzCCAiOgAwIBAgIBMDANBgkqhkiG9w0BAQUFADA4MQswCQYDVQQGEwJDTjENMAsGA1UECwwEQ05DQjEaMBgGA1UEAwwRZS5iYW5rLmVjaXRpYy5jb20wHhcNMTgwMjExMDg0NTIyWhcNMzgwMjA2MDg0NTIyWjA4MQswCQYDVQQGEwJDTjENMAsGA1UECwwEQ05DQjEaMBgGA1UEAwwRZS5iYW5rLmVjaXRpYy5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCkF+2AicVKj7SaHw3dbJt3i6fkL1WfLw1WRqe8r8Cc7qJOshaqNvCzW1qRX6E5H/umtl1Uj99V07uewUFk96xY/+s/GuBnbGoSrcu3OAHDgEGuY5atZo+umIk7LufAif2VUcNGY3nWxGcig20ExO/6nAf/G3Xxo4QL8fBdPG/prOXxSvtJiPls1Qg9zzSgAH+HMCAINMsuJmzDQiTt6Me8k7YHts+jWQF7KF25plITcW1Qmy3Aw8qYjVhbHn8KTAEeuQhmM5RS6KP1Hu71q4DYOWcx44QThSbiAYwG1JQBBwM8XnBfVYMpr6Qi0owibNYoZ/S6xwfRFGB0W1HeG9WfAgMBAAGjUDBOMB0GA1UdDgQWBBT0iLEXY9HIKNy5DG4d72l+R7Nf1zAfBgNVHSMEGDAWgBT0iLEXY9HIKNy5DG4d72l+R7Nf1zAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBBQUAA4IBAQB5MWz1RGFG537rJCtHp+LqxR9iJSFsHiW3ZoLIAeyD0oJ69RcL2gE/TNWmE9zYUkd9TdNtXqxlNPpj1P1/+x781neWnGou/n/XFS82T5S339X3DIjHc/IqOzwnxEOKH2V0NmK9iKgx6H05Q9MMvUXFsL3QK2hDMAVY28roRiC4S1yfJJaA08DfvXZf6cVx1xfWl+ks57+3knkoWap1rjwh1RdGk5ChPbzD0AnAcWTMWRCbjuJnttlmWZnI1I6mhcQUKUEMoj8sR8m11YJ5woscYPsIle/rJOOosuMghczD1vRcg3eLUaWn1A5rsBa82RyxhiuYocEQVX59Hy6v3npT"

    /**
     * device id, 用于传递设备 id 信息。
     * 采集设备信息完成之后有值
     */
    @JvmStatic
    var deviceId = ""
        private set

    @JvmStatic
    var shumeiBoxData = ""

    @JvmStatic
    fun sdkVersion(): String {
        return SmAntiFraud.getSDKVersion()
    }

    private var callbackBlock: ((reportInSuccess: Boolean) -> Unit)? = null

    /**
     * 仅在主进程调用即可
     */
    @JvmStatic
    fun initConfig(
        context: Context,
        channel: String,
        domain: String,
        callback: (reportInSuccess: Boolean) -> Unit
    ) {
        callbackBlock = callback
        ThreadUtil.runInOtherThread {
            val s = System.currentTimeMillis()
            doInitConfig(context, channel, domain)
            HLog.d(TAG, "init spent {}", System.currentTimeMillis() - s)
        }
    }

    private fun doInitConfig(context: Context, channel: String, domain: String) {
        val start = SystemClock.elapsedRealtime()
        val option = SmOption()
        option.organization = "JTOfUpbmGwrqyZboee3C"
        option.appId = "NewWeplay"
        option.publicKey = PUBLIC_KEY
        option.url = "$domain/shumei_api/V3/device_print_report/android"
        option.confUrl = "$domain/shumei_api/cloud_conf_report"
        option.notCollect = noCollectFields()
        option.channel = channel

        val ref = AtomicBoolean(false)
        SmAntiFraud.registerServerIdCallback(object : IServerSmidCallback {
            override fun onSuccess(boxId: String?) {
                deviceId = boxId ?: SmAntiFraud.getDeviceId()
                shumeiBoxData = ""
                val spent = SystemClock.elapsedRealtime() - start
                HLog.d(TAG, HLog.USR, "BoxId_OK, spent={}, id={}", spent, boxId)
                HLog.aliPerformance(TAG, "init", true, spent, deviceId, emptyMap())
                callbackBlock?.invoke(true)
            }

            override fun onError(code: Int) {
                val spent = SystemClock.elapsedRealtime() - start
                val msg = StringUtil.formatS(
                    "{}, BoxId_Error, spent={} code={}, msg={}",
                    TAG,
                    spent,
                    code,
                    decodeErrorCode(code)
                )
                SmAntiFraud.getDeviceId {
                    if (it == null) {
                        HLog.aliPerformance(TAG, "init", false, spent, "shumei_id_null", emptyMap())
                        return@getDeviceId
                    }
                    if (it.length > 1024) {
                        // box data 一般在 5k 左右。
                        // id 在 30 多字节
                        // 加密后的 id 约 90 字节
                        shumeiBoxData = it
                    } else {
                        deviceId = it
                    }
                    HLog.d(TAG, HLog.USR, "shumeiBoxData[{}]: {}", it.length, it)
                    callbackBlock?.invoke(false)
                }
                HLog.d(TAG, HLog.USR, msg)
                HLog.aliPerformance(TAG, "init", false, spent, decodeErrorCode(code), emptyMap())
            }
        })
        val res = SmAntiFraud.create(context, option)
        ref.set(true)
        HLog.d(TAG, HLog.USR, "create_return={}", res)
        val data = SmAntiFraud.getDeviceId()
        if (data.length > 1024) {
            // box data 一般在 5k 左右。
            // id 在 30 多字节
            // 加密后的 id 约 90 字节
            shumeiBoxData = data
        } else {
            deviceId = data
        }
        HLog.d(TAG, HLog.USR, "shumeiBoxData[{}]: {}", data.length, data)
    }

    private fun decodeErrorCode(i: Int): String {
        return when (i) {
            -1 -> "ERROR_NO_NETWORK"
            -2 -> "ERROR_NO_RESPONSE"
            -3 -> "ERROR_SERVER_RESPONSE"
            else -> "ERROR_UNKNOWN"
        }
    }

    /**
     * 无需收集的字段列表
     */
    private fun noCollectFields(): Set<String> {
        val list: MutableList<String> = ArrayList()
        list.add("imsi") // imsi
        list.add("mac") // mac 地址
        list.add("imei") //imei
        list.add("oaid") // oaid
        list.add("protect") // 禁用 mprotect 函数
        return HashSet(list)
    }
}