// for log tag
def TAG = "[config.gradle] "
BuildProperties buildProperties = new BuildProperties(project)

def current_server = buildProperties.getString("BUILD_SERVER_CONFIG", "ar")
def taskReqName = getGradle().getStartParameter().getTaskRequests().toString()
def is_debug_compile = taskReqName.contains("Debug") || taskReqName.contains("debug")
def is_release_compile = taskReqName.contains("Release") || taskReqName.contains("release")
println(TAG + "server config:" + current_server + " is_debug_compile:" + is_debug_compile)

def build_from_ide = gradle.startParameter.projectProperties.get("android.injected.invoked.from.ide") == "true"
println(TAG + "build_from_ide: " + build_from_ide)
def is_use_pad = buildProperties.getBoolean("BUILD_PAD_CONFIG", false)
println(TAG + "use_pad:" + is_use_pad)

def use_debug_settings = buildProperties.getBoolean("COMPILE_DEBUG_SETTINGS", false)
println(TAG + "use_debug_settings:" + use_debug_settings)

def is_enable_minify_for_debug = buildProperties.getBoolean("BUILD_DEBUG_WITH_MINIFY", false)
println(TAG + "is_enable_minify_for_debug:" + is_enable_minify_for_debug)

def build_min_package = buildProperties.getBoolean("BUILD_MIN_PACKAGE", false)
println(TAG + "build_min_package:" + build_min_package)

ServerProperties serverProperties = new ServerProperties(project.rootProject, current_server)
def getVal = serverProperties.&getVal
def getList = serverProperties.&getList
def getInt = serverProperties.&getInt

def unity_version = buildProperties.getString("UNITY_VERSION", "")
if (unity_version == null || unity_version.length() == 0) {
    unity_version = getVal("UNITY_VERSION")
    if (is_release_compile && unity_version.contains("debug")) {
        throw new IllegalStateException("release version can not set " + unity_version)
    }
}
println(TAG + "unity_version:" + unity_version)

if (is_release_compile && libs.versions.libproto.get().contains("SNAPSHOT")) {
    throw new IllegalStateException("release version can not use SNAPSHOT for libproto")
}

Properties localProperties = ServerProperties.initProperties("${rootDir.getAbsolutePath()}/local.properties")
def localUseLib = localProperties.getProperty("aar.compile") == 'true'
println(TAG + "use lib: $localUseLib")
def version_code = buildProperties.getString("VERSION_CODE", "")
if (version_code == null || version_code.length() == 0) {
    version_code = getVal("VERSION_CODE")
}
def useDevSecLib = build_from_ide || localProperties.getProperty("sec-lib.dev") == "true"
if (useDevSecLib) {
    println("sec-lib debug enabled, you can debug in the android studio")
} else {
    println("add sec-lib.dev=true in your local.properties to enable debug")
}
println(TAG + "versionCode: ${version_code} versionName: ${getVersionName(getVal("VERSION_NAME"), is_debug_compile)}")

def language = getList("LANGUAGE_ORDER")
if (is_debug_compile && !use_debug_settings) {
    language = getList("ALL_LANGUAGE")
}
def mainDirs = getList("MAIN_DIR")
def srcDirs = new ArrayList(getList("SRC_DIR"))
def resDirs = new ArrayList(getList("RES_DIR"))
def assetsDirs = new ArrayList(getList("ASSETS_DIR"))
mainDirs.each {
    srcDirs.add("${it}/main/java")
    resDirs.add("${it}/main/res")
    assetsDirs.add("${it}/main/assets")
}

ext {

    // config server
    SERVER_CONFIG = current_server
    // compile something only in debug mode
    IS_DEBUG_COMPILE = is_debug_compile
    COMPILE_DEBUG_SETTINGS = is_debug_compile && use_debug_settings
    USE_PAD = is_use_pad
    BUILD_MIN_PACKAGE = build_min_package
    BUILD_FROM_IDE = build_from_ide
    TRANSLATION_ID = getVal("TRANSLATION_ID")

    CH_SERVER_CONFIG = "ch"
    US_SERVER_CONFIG = "us"
    JA_SERVER_CONFIG = "ja"
    AR_SERVER_CONFIG = "ar"
    KR_SERVER_CONFIG = "kr"
    USE_DEV_SEC_LIB = useDevSecLib

    java_version = JavaVersion.VERSION_17

    android = [
            compileSdkVersion   : 34,
            buildToolsVersion   : "34.0.0",
            minSdkVersion       : 23,
            targetSdkVersion    : 34,
            ndkVersion          : "25.1.8937393",
            enableMinifyForDebug: is_enable_minify_for_debug
    ]

    useLib = localUseLib

    //定义海外国内版，区分引用库等
    china = 'china'
    overseas = 'overseas'
    buildArea = overseas
    appConfig = [
            KEY_STORE               : getVal("KEY_STORE"),//签名
            SRC_DIR                 : srcDirs,//java 资源目录
            RES_DIR                 : resDirs, //资源文件夹
            ASSETS_DIR              : assetsDirs, //assets文件夹
            BASE_FILE_DIR           : getVal("BASE_FILE_DIR"),//FileConfig中文件夹基地址
            PHOTO_FILE_DIR          : getVal("PHOTO_FILE_DIR"),//保存图片的文件夹
            DOWNLOAD_DIR            : getVal("DOWNLOAD_DIR"),//手动修改 libProvide下载文件夹，与 BASE_FILE_DIR一致
            PKG_NAME                : getVal("PKG_NAME"),//http中的参数
            PUSH_CHANNEL            : getInt("PUSH_CHANNEL"),//不同包的PushChannel
            GAMEPROCESS_NAME        : getVal("GAMEPROCESS_NAME"),//游戏进程的名称
            WPLINK_SCHEME           : getVal("WPLINK_SCHEME"),//wplink的scheme
            WPLINK_HOST             : getVal("WPLINK_HOST"),//wplink的host
            FB_ACTIVITY_NAME        : getVal("FB_ACTIVITY_NAME"),
            FB_APP_ID               : getVal("FB_APP_ID"),
            FACEBOOK_CLIENT_TOKEN   : getVal("FACEBOOK_CLIENT_TOKEN"),
            FB_LOGIN_PROTOCOL_SCHEME: getVal("FB_LOGIN_PROTOCOL_SCHEME"),
            FB_PROVIDER_AUTHORITIES : getVal("FB_PROVIDER_AUTHORITIES"),
            FB_PAGE_ID              : getVal("FB_PAGE_ID"),
            TWITTER_KEY             : getVal("TWITTER_KEY"),
            TWITTER_LOGINL_SCHEME   : "twitterkit-" + getVal("TWITTER_KEY").toLowerCase(),
            PROD_DOMAIN             : getVal("PROD_DOMAIN"),
            DEBUG_DOMAIN            : getVal("DEBUG_DOMAIN"),
            LANGUAGE_ORDER          : formatLanguage(language),
            ALI_NET_LOG_END_POINT   : getVal("ALI_NET_LOG_END_POINT"),
            ALI_NET_LOG_PROJECT     : getVal("ALI_NET_LOG_PROJECT"),
            VERSION_NAME            : getVersionName(getVal("VERSION_NAME"), is_debug_compile),
            VERSION_CODE            : Integer.parseInt(version_code),
            COMMON_VERSION_NAME     : getVal("VERSION_NAME"),
            COMMON_VERSION_CODE     : Integer.parseInt(version_code),
            APP_NAME                : getVal("APP_NAME"),
            UNITY_RES_DOMAIN        : getVal("UNITY_RES_DOMAIN"),
            DEFAULT_TRACK_REGION    : getVal("DEFAULT_TRACK_REGION"),
            RES_CONFIG              : getResConfig(language, build_min_package),
            appAuthRedirectScheme   : "com.googleusercontent.apps.439155732395-j1ohrvq39afuaegu61vneq9a54gl41iq"
    ]
    if (project.hasProperty("TEST_VERSION")) {
        def v = project.property("TEST_VERSION")
        if (v != null && v.toString().length() > 0 && isNumeric(v)) {
            appConfig.VERSION_NAME = v
            println("use test version: ${appConfig.VERSION_NAME}")
        }
    }

    LOCAL_UNITY_VERSION = unity_version
    UNITY_VERSION = LOCAL_UNITY_VERSION
    packageName = getVal("PACKAGE_NAME")

    lProperties = localProperties
}

class ServerProperties {
    def baseProperties
    def serverProperties

    ServerProperties(project, serverName) {
        baseProperties = initPropertiesByFile(new File(project.rootDir, "prop/base_local.properties"))
        File file = new File(project.rootDir, "prop/" + serverName + "_local.properties")
        serverProperties = initPropertiesByFile(file)

    }

    def getVal(key) {
        def val = null
        if (serverProperties != null) {
            val = this.serverProperties.getProperty(key)
        }
        if (val == null) {
            val = this.baseProperties.getProperty(key)
        }
        if (val == null) {
            return null
        }
        return new String(val.getBytes("iso-8859-1"))
    }

    def getList(key) {
        def val = getVal(key)
        if (val == null) {
            return Collections.emptyList()
        }
        return Arrays.asList(val.split(","))
    }

    def getInt(key) {
        def val = getVal(key)
        if (val == null) {
            return 0
        }
        return Integer.parseInt(val)
    }

    static def initProperties(filePath) {
        File propertiesFile = new File(filePath)
        return initPropertiesByFile(propertiesFile)
    }

    static def initPropertiesByFile(propertiesFile) {
        Properties properties = new Properties()
        if (propertiesFile.exists()) {
            def is = new FileInputStream(propertiesFile)
            properties.load(is)
            is.close()
        }
        return properties
    }
}

class BuildProperties {

    private Project project

    BuildProperties(project) {
        this.project = project
    }

    def getBoolean(key, defaultVal) {
        return Boolean.parseBoolean(getVal(key, defaultVal).toString())
    }

    def getString(key, defaultVal) {
        return getVal(key, defaultVal).toString()
    }

    def getVal(key, defaultVal) {
        if (!project.hasProperty(key)) {
            return defaultVal
        }
        return project.property(key)
    }
}

def formatLanguage(List<String> list) {
    def lang = "{"
    for (int i = 0; i < list.size(); i++) {
        if (i != 0) {
            lang += ","
        }
        lang += "\"" + list.get(i) + "\""
    }
    return lang + "}"
}

def getVersionName(String name, boolean isDebug) {
    if (isDebug || isDebugBuildType()) {
        int j = 0
        for (int i = 0; i < name.length(); i++) {
            def c = name.charAt(i)
            if (c == '.') {
                j++
            }
            if (j > 2) {
                j = i
                break
            }
        }
        if (j > 2) {
            name = name.substring(0, j)
        }
        return name + ".dev"
    }
    return name
}

boolean isDebugBuildType() {
    for (String s : gradle.startParameter.taskNames) {
        if (s.contains("debug") | s.contains("Debug")) {
            return true
        }
    }
    return false
}

def isNumeric(str) {
    java.util.regex.Pattern pattern = Pattern.compile("\\d*")
    return pattern.matcher(str.replace(".", "")).matches()
}

def getResConfig(language, isMin) {
    def list = new ArrayList<String>()
    list.addAll(language)
    return list
}