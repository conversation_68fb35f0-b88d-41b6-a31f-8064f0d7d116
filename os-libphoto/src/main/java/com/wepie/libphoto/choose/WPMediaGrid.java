package com.wepie.libphoto.choose;

import static com.huiwan.base.str.ResUtil.getString;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.huiwan.base.common.MediaChooseLimit;
import com.huiwan.base.util.TimeUtil;
import com.wepie.libphoto.R;
import com.zhihu.matisse.internal.entity.IncapableCause;
import com.zhihu.matisse.internal.entity.Item;
import com.zhihu.matisse.internal.ui.widget.SquareFrameLayout;
import com.zhihu.matisse.internal.ui.widget.WPMediaCheckView;

public class WPMediaGrid extends SquareFrameLayout implements View.OnClickListener {
    private ImageView mThumbnail;
    private View mask;
    private WPMediaCheckView mCheckView;
    private ViewGroup mCheckLay;
    private ImageView mGifTag;
    private TextView mVideoDuration;
    private LinearLayout mVideoDurationLayout;

    private Item mMedia;
    private PreBindInfo mPreBindInfo;
    private OnMediaGridClickListener mListener;
    // 发送1分钟内的视频
    private boolean isSizeNormal = false;
    private boolean isFileSupport = false;
    private boolean canShowImage = false;
    private MediaChooseLimit mediaChooseLimit = new MediaChooseLimit(9, false);

    public WPMediaGrid(Context context) {
        super(context);
        init(context);
    }

    public WPMediaGrid(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.wp_media_grid_lay, this, true);

        mThumbnail = findViewById(com.zhihu.matisse.R.id.media_thumbnail);
        mCheckView = findViewById(com.zhihu.matisse.R.id.check_view);
        mCheckLay = findViewById(R.id.check_lay);
        mGifTag = findViewById(com.zhihu.matisse.R.id.gif);
        mVideoDuration = findViewById(com.zhihu.matisse.R.id.video_duration);
        mVideoDurationLayout = findViewById(R.id.video_duration_layout);
        mask = findViewById(R.id.mask);

        mThumbnail.setOnClickListener(this);
        mCheckLay.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (!canShowImage) {
            String msg;
            if (!isSizeNormal) {
                msg = getString(R.string.media_file_too_large);
            } else {
                msg = getString(R.string.error_file_type);
            }
            IncapableCause cause = new IncapableCause(msg);
            IncapableCause.handleCause(getContext(), cause);
        } else if (mListener != null) {
            if (v == mThumbnail) {
                mListener.onThumbnailClicked(mThumbnail, mMedia, mPreBindInfo.mViewHolder);
            } else if (v == mCheckLay) {
                mListener.onCheckViewClicked(mCheckView, mMedia, mPreBindInfo.mViewHolder);
            }
        }
    }

    public void preBindMedia(PreBindInfo info) {
        mPreBindInfo = info;
    }

    public void bindMedia(Item item) {
        mMedia = item;
        setGifTag();
        initCheckView();
        setImage();
        setMediaLimit();
        updateViewInfo();
    }

    public Item getMedia() {
        return mMedia;
    }

    private void setGifTag() {
        mGifTag.setVisibility(mMedia.isGif() ? View.VISIBLE : View.GONE);
    }

    private void initCheckView() {
        mCheckView.setCountable(mPreBindInfo.mCheckViewCountable);
    }

    public void setCheckEnabled(boolean enabled) {
        setAlpha(enabled ? 1 : 0.5f);
        mCheckView.setEnabled(enabled);
    }

    public void setCheckedNum(int checkedNum) {
        mCheckView.setCheckedNum(checkedNum);
    }

    public void setChecked(boolean checked) {
        mCheckView.setChecked(checked);
    }

    private void setImage() {
        RequestOptions requestOptions = RequestOptions
                .frameOf(1)
                .diskCacheStrategy(DiskCacheStrategy.RESOURCE);
        Glide.with(getContext()).
                asBitmap().
                override(mPreBindInfo.mResize, mPreBindInfo.mResize).
                load(mMedia.getContentUri()).placeholder(mPreBindInfo.mPlaceholder).
                apply(requestOptions).
                into(mThumbnail);
    }

    private void setMediaLimit() {
        if (null != mMedia && null != mediaChooseLimit) {
            long size = mMedia.size;
            isSizeNormal = true;
            isFileSupport = true;
            if (mMedia.isVideo()) {
                float dur = mMedia.duration / 1000f;
                if (dur > 0) {
                    isSizeNormal = !(dur > mediaChooseLimit.getVideoDuration() || size > (long) mediaChooseLimit.getVideoRawSizeLimit() * 1024 * 1024);
                } else {
                    isFileSupport = false;
                }
            } else {
                isSizeNormal = size <= (long) mediaChooseLimit.getOriginPicSizeLimit() * 1024 * 1024;
            }
        }
        canShowImage = isSizeNormal && isFileSupport;
    }

    private void updateViewInfo() {
        if (mMedia.isVideo()) {
            mVideoDurationLayout.setVisibility(View.VISIBLE);
            mVideoDuration.setText(TimeUtil.getMinuteSecond(mMedia.duration / 1000));
        } else {
            mVideoDurationLayout.setVisibility(View.GONE);
        }

        if (mediaChooseLimit.isSingleChat()) {
            mCheckLay.setVisibility(GONE);
        } else {
            if (canShowImage) {
                mCheckLay.setVisibility(VISIBLE);
                mask.setVisibility(View.GONE);
            } else {
                mask.setVisibility(View.VISIBLE);
                mCheckLay.setVisibility(GONE);
            }
        }
    }

    public void setOnMediaGridClickListener(OnMediaGridClickListener listener) {
        mListener = listener;
    }

    public void removeOnMediaGridClickListener() {
        mListener = null;
    }

    public interface OnMediaGridClickListener {

        void onThumbnailClicked(ImageView thumbnail, Item item, RecyclerView.ViewHolder holder);

        void onCheckViewClicked(WPMediaCheckView checkView, Item item, RecyclerView.ViewHolder holder);
    }

    public static class PreBindInfo {
        int mResize;
        Drawable mPlaceholder;
        boolean mCheckViewCountable;
        RecyclerView.ViewHolder mViewHolder;

        public PreBindInfo(int resize, Drawable placeholder, boolean checkViewCountable,
                           RecyclerView.ViewHolder viewHolder) {
            mResize = resize;
            mPlaceholder = placeholder;
            mCheckViewCountable = checkViewCountable;
            mViewHolder = viewHolder;
        }
    }

    public void setMediaChooseLimit(MediaChooseLimit mediaChooseLimit) {
        this.mediaChooseLimit = mediaChooseLimit;
    }
}
