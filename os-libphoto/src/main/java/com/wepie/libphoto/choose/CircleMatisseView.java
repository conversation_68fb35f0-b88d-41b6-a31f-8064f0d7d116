package com.wepie.libphoto.choose;

import android.content.Context;
import android.database.Cursor;
import android.os.Build;
import android.util.AttributeSet;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.common.MediaChooseLimit;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.widget.decoration.SpaceItemDecoration;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.liblog.main.HLog;
import com.wepie.libphoto.PhotoCallback;
import com.zhihu.matisse.internal.entity.Album;
import com.zhihu.matisse.internal.entity.Item;
import com.zhihu.matisse.internal.entity.SelectionSpec;
import com.zhihu.matisse.internal.model.AlbumMediaCollection;
import com.zhihu.matisse.internal.model.SelectedItemCollection;
import com.zhihu.matisse.internal.ui.MediaSelectionFragment;
import com.zhihu.matisse.internal.utils.UIUtils;

import java.util.Set;

public class CircleMatisseView extends FrameLayout implements
        AlbumMediaCollection.AlbumMediaCallbacks, ChoosePhotoAdapter.CheckStateListener,
        ChoosePhotoAdapter.OnMediaClickListener {

    private static final String TAG = "CircleMatisseView";
    public static final String EXTRA_ALBUM = "extra_album";
    private Context context;
    private Album album;

    private final AlbumMediaCollection mAlbumMediaCollection = new AlbumMediaCollection();
    private RecyclerView mRecyclerView;
    private ChoosePhotoAdapter mAdapter;
    private MediaSelectionFragment.SelectionProvider mSelectionProvider;
    private ChoosePhotoAdapter.CheckStateListener mCheckStateListener;
    private ChoosePhotoAdapter.OnMediaClickListener mOnMediaClickListener;
    private SelectedItemCollection mCollection;

    private MediaChooseLimit mediaChooseLimit = new MediaChooseLimit(9, false);

    public CircleMatisseView(@NonNull Context context) {
        super(context);
        this.context = context;
        initView();
    }

    public CircleMatisseView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        initView();
    }

    private void initView() {
        inflate(context, com.zhihu.matisse.R.layout.fragment_media_selection, this);
        mRecyclerView = findViewById(com.zhihu.matisse.R.id.recyclerview);
    }

    public void init(Album album, IMatisse iMatisse, PhotoCallback photoCallback) {
        if (!(context instanceof FragmentActivity)) return;
        this.album = album;
        mSelectionProvider = iMatisse;
        mCheckStateListener = iMatisse;
        mOnMediaClickListener = iMatisse;

        mCollection = mSelectionProvider.provideSelectedItemCollection();
        mCollection.setMediaChooseLimit(mediaChooseLimit);
        mAdapter = new ChoosePhotoAdapter(getContext(), mCollection
                , mRecyclerView);
        mAdapter.registerCheckStateListener(this);
        mAdapter.registerOnMediaClickListener(this);
        mAdapter.setPhotoCallback(photoCallback);
        mAdapter.setCameraCallback(iMatisse);
        mRecyclerView.setHasFixedSize(true);

        int spanCount;
        SelectionSpec selectionSpec = SelectionSpec.getInstance();
        if (selectionSpec.gridExpectedSize > 0) {
            spanCount = UIUtils.spanCount(getContext(), selectionSpec.gridExpectedSize);
        } else {
            spanCount = selectionSpec.spanCount;
        }
        mRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), spanCount) {
            @Override
            public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
                try {
                    super.onLayoutChildren(recycler, state);
                } catch (Exception e) {
                    HLog.d(TAG, HLog.USR, "onLayoutChildren exception={}", e);
                }
            }
        });

        int spacing = ScreenUtil.dip2px(4);
        mRecyclerView.addItemDecoration(new SpaceItemDecoration(spacing, spacing / 2, 0, spacing / 2, false));
        mRecyclerView.setAdapter(mAdapter);
        mAlbumMediaCollection.onCreate((FragmentActivity) context, this);
        mAlbumMediaCollection.load(album, selectionSpec.capture);

        //用于解决一个已选9图切相册，未能正确将图片置成半透明的bug
        ViewExKt.postAutoCancel(this, 500, () -> mAdapter.notifyDataSetChanged());
    }

    @Override
    public void onAlbumMediaLoad(Cursor cursor) {
        adapterAndroid14ClearOldItemAfterPermissionReload(cursor);
        mAdapter.swapCursor(cursor);
    }

    /**
     * 适配用户在Android 14+ 设备，重选图库图片，取消之前勾选的图片，回到选图页面，选中页码没有刷新问题
     * 方案是当cursor 变更时，检查是否之前有选中过图片，然后遍历cursor,检查选中的图片是否存在，不存在，则在选中记录中删除它.
     * 遍历完毕之后cursor 重新回到原位
     *
     * @param cursor
     */
    private void adapterAndroid14ClearOldItemAfterPermissionReload(Cursor cursor) {
        if (cursor != null && mCollection != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            try {
                Set<Item> items = mCollection.asSet();
                if (items != null && !items.isEmpty()) {
                    if (cursor.moveToFirst()) {
                        do {
                            Item cursorValue = Item.valueOf(cursor);
                            if (items.contains(cursorValue)) {
                                items.remove(cursorValue);
                            }
                            if (items.isEmpty()) {
                                break;
                            }
                        } while (cursor.moveToNext());
                        cursor.moveToFirst();
                    }
                    if (!items.isEmpty()) {
                        for (Item item : items) {
                            mCollection.remove(item);
                        }
                        onUpdate();
                    }
                }
            } catch (Exception e) {
                HLog.e(TAG, e, " adapterAndroid14ClearOldItemAfterPermissionReload error");
            }
        }
    }

    @Override
    public void onAlbumMediaReset() {
        mAdapter.swapCursor(null);
    }

    @Override
    public void onUpdate() {
        // notify outer Activity that check state changed
        if (mCheckStateListener != null) {
            mCheckStateListener.onUpdate();
        }
    }

    @Override
    public void onMediaClick(Album album, Item item, int adapterPosition) {
        if (mOnMediaClickListener != null) {
            if (this.album == null) return;
            mOnMediaClickListener.onMediaClick(this.album,
                    item, adapterPosition);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mAlbumMediaCollection.onDestroy();
    }

    public void refreshMediaGrid() {
        mAdapter.notifyDataSetChanged();
    }

    public void refreshSelection() {
        mAdapter.refreshSelection();
    }

    public void setMediaChooseLimit(MediaChooseLimit mediaChooseLimit) {
        this.mediaChooseLimit = mediaChooseLimit;
    }
}
