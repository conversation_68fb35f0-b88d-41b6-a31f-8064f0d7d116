package com.wepie.libphoto;

import static android.app.Activity.RESULT_OK;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.common.MediaChooseLimit;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.store.file.FileConfig;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.liblog.main.HLog;
import com.wepie.libphoto.clip.ClipActivity;
import com.wepie.libphoto.clip.MatisseObserver;
import com.wepie.libprovider.FileProvider7;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.internal.entity.Item;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class MatisseUtil {

    public static final String TAG = "MatisseUtil";
    private static final int MAX_GIF_SIZE = 1024 * 1024 * 5;
    private static String cameraPath = "";
    private static float clipPhotoRate = -1f;
    private static boolean clipGifAsJpg = true;
    private static PhotoCallback photoCallback;
    private static List<MatisseObserver> observerList = new LinkedList<>();

    private static Uri cameraUri = null;

    /**
     * @param activity
     * @param maxNum   最大选择数量
     * @param clipRate 剪裁长宽比，不用剪裁传-1，方形传1，常见其他比例 16f/9
     * @param callback
     */
    public static void launchMatisse(Activity activity, int maxNum, float clipRate, boolean isSelectHead, PhotoCallback callback) {
        clipPhotoRate = clipRate;
        photoCallback = callback;
        MediaChooseLimit config = ConfigHelper.getInstance().getConstConfig().getMsgFileUploadLimit();
        config.setMaxSelect(maxNum);
        config.setSelectHead(isSelectHead);
        config.setClipPhotoRate(clipRate);
        callback.onPrepare(config);
        WPChoosePhotoDialog.show(activity, config, TrackButtonName.FINISH, (itemList, origin) -> {
            List<Item> items = new ArrayList<>(itemList);
            if (clipPhotoRate > 0 && !items.isEmpty()) {//需要剪裁
                ClipActivity.gotoClipActivity(activity, items.get(0).path, clipPhotoRate);
            } else {
                invokeFinish(items);
            }
        });
    }

    public static void launchMatisse(Activity activity, int maxNum, float clipRate, PhotoCallback callback) {
        launchMatisse(activity, maxNum, clipRate, false, callback);
    }

    /**
     * @param activity
     * @param photoPath   相机拍照的图片缓存路径
     * @param reqeustCode 指定requestCode
     * @param clipRate    剪裁长宽比，不用剪裁传-1，方形传1，常见其他比例 16f/9
     * @param callback
     */
    public static void launchCameraActivity(Activity activity, String photoPath, int reqeustCode, float clipRate, PhotoCallback callback) {
        cameraPath = photoPath;
        clipPhotoRate = clipRate;
        photoCallback = callback;
        try {
            File file = new File(photoPath);
            if (!file.isAbsolute()) {
                throw new Exception("file is not valid -->" + photoPath);
            }

            if (!file.exists()) {
                boolean result = file.getParentFile().mkdirs();
                Log.e(TAG, "launchCameraActivity: result = " + result);
                if (!file.createNewFile()) {
                    throw new Exception("create file failed");
                }
            }

            Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);//action is capture
            intent.putExtra(MediaStore.EXTRA_OUTPUT, getCameraUri(activity, file));
            intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
            activity.startActivityForResult(intent, reqeustCode);
        } catch (Exception e) {
            e.printStackTrace();
            HLog.e(TAG, HLog.USR, "launchCameraActivity: ", e);
            ToastUtil.debugShow("fail to open camera");
        }
    }

    private static Uri getCameraUri(Context context, File file) {
        if (Build.VERSION_CODES.Q <= Build.VERSION.SDK_INT) {
            String status = Environment.getExternalStorageState();
            ContentResolver resolver = LibBaseUtil.getApplication().getContentResolver();
            if (status.equals(Environment.MEDIA_MOUNTED)) {
                cameraUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, new ContentValues());
            } else {
                cameraUri = resolver.insert(MediaStore.Images.Media.INTERNAL_CONTENT_URI, new ContentValues());
            }
        } else {
            cameraUri = FileProvider7.getUriForFile(context, file);
        }
        return cameraUri;
    }

    public static boolean onActivityResult(Activity activity, int requestCode, int resultCode, Intent data, int matisseCode, int cameraCode) {
        for (MatisseObserver observer : observerList) {
            observer.onActivityResult(requestCode, resultCode, data);
        }
        List<Item> items = new ArrayList<>();
        if (requestCode == matisseCode) {
            if (resultCode == RESULT_OK) {
                List<String> mSelected = Matisse.obtainPathResult(data);
                for (String path : mSelected) {
                    items.add(Item.build(path));
                }

                if (clipPhotoRate > 0 && items.size() > 0) {//需要剪裁
                    if (!clipGifAsJpg && WpImageLoader.isGif(items.get(0).path)) {
                        invokeFinish(items);
                    } else {
                        ClipActivity.gotoClipActivity(activity, items.get(0).path, clipPhotoRate);
                    }
                } else {
                    invokeFinish(items);
                }
            } else {
                clearCallback();
            }
            return true;
        } else if (requestCode == cameraCode) {
            if (resultCode == RESULT_OK) {
                try {
                    ContentResolver contentResolver = LibBaseUtil.getApplication().getContentResolver();
                    Uri res;
                    if (cameraUri != null) {
                        res = cameraUri;
                    } else {
                        res = getCameraUri(LibBaseUtil.getApplication(), new File(cameraPath));
                    }

                    if (null != data && null != data.getData()) {
                        res = data.getData();
                    }
                    String type = contentResolver.getType(res);
                    HLog.d(TAG, HLog.USR, "handle camera result! type=" + type);
                    if (null != type && type.contains("video")) {
                        handleVideoCapture(activity, type, res, items);
                    } else {
                        handlePictureCapture(activity, res, items);
                    }
                } catch (Exception e) {
                    clearCallback();
                    HLog.d(TAG, HLog.USR, "handle camera result error! e=" + e);
                }
            } else {
                clearCallback();
            }
            return true;
        } else if (requestCode == ConstData.CLIP_REQUEST_CODE) {
            if (resultCode == RESULT_OK) {
                String clipPath = data.getStringExtra(ConstData.IMAGE_PATH);
                items.add(Item.build(clipPath));
                invokeFinish(items);
            } else {
                clearCallback();
            }
            return true;
        }
        return false;
    }

    private static void handleVideoCapture(Activity activity, String mimeType, Uri cameraUri, List<Item> items) {
        Item item = Item.buildVideo(activity, mimeType, cameraUri);
        items.add(item);
        invokeFinish(items);
    }

    private static void handlePictureCapture(Activity activity, Uri cameraUri, List<Item> items) {
        try {
            String folderPath = FileConfig.getImageCacheDirPath();
            File folder = new File(folderPath);
            String p = new File(folder, String.valueOf(System.currentTimeMillis())).getAbsolutePath();
            copyUriToFile(cameraUri, p);
            items.add(Item.build(p));
            if (clipPhotoRate > 0) {//需要剪裁
                ClipActivity.gotoClipActivity(activity, items.get(0).path, clipPhotoRate);
            } else {
                invokeFinish(items);
            }
        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "camera onActivityResult error! msg=" + e);
            ToastUtil.debugShow("failed to dispose the file from camera");
            clearCallback();
        }
    }

    public static void registerObserver(MatisseObserver observer) {
        observerList.add(observer);
    }

    public static void unregisterObserver(MatisseObserver observer) {
        observerList.remove(observer);
    }

    private static void invokeFinish(List<Item> items) {
        if (photoCallback != null) {
            photoCallback.onFinish(items);
            photoCallback = null;
        }
    }

    private static void clearCallback() {
        photoCallback = null;
    }

    public static void copyUriToFile(Uri uri, String path) throws Exception {
        InputStream is = LibBaseUtil.getApplication().getContentResolver().openInputStream(uri);
        FileUtil.copyFile(is, path);
    }
}
