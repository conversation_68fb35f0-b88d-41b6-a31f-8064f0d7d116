package com.wepie.libphoto;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.provider.MediaStore;

import androidx.annotation.Nullable;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.activity.ActivityTask;
import com.huiwan.base.activity.ActivityTaskBuilderHolder;
import com.huiwan.base.common.MediaChooseLimit;
import com.huiwan.store.file.FileConfig;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.BitmapUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.constants.ActivityResultCode;
import com.huiwan.platform.ThreadUtil;
import com.wepie.libpermission.PermissionCallback;
import com.wepie.libpermission.PermissionUtil;
import com.wepie.libpermission.WPPermission;
import com.wepie.wespy.helper.dialog.PermissionDialog;
import com.zhihu.matisse.internal.entity.Item;

import java.util.ArrayList;
import java.util.List;

// Created by bigwen on 2018/9/10.
public class PhotoUtil {

    private static final String destPath = FileConfig.getTempFolderPath() + "temp.jpg";

    public static void launchSelectPhoto(final Activity activity, final int maxNum, final float clipRate, final PhotoCallback callback) {
        launchSelectPhoto(activity, maxNum, clipRate, false, callback);
    }

    public static void launchSelectPhoto(final Activity activity, final int maxNum, final float clipRate, boolean isSelectHead, final PhotoCallback callback) {
        requestPhotoPermission(activity, new PhotoPermissionCallBack() {
            @Override
            public void hasPermission(Context context, List<String> granted, boolean isAll, boolean alreadyHas) {
                MatisseUtil.launchMatisse(activity, maxNum, clipRate, isSelectHead, callback);
            }
        });
    }

    /**
     * 减小改动
     * web 需要有回调才能再次触发选择文件
     */
    public static void launchSelectPhotoForWeb(final Activity activity, final int maxNum, final float clipRate, final PhotoCallback callback) {
        PermissionUtil.requestOpenAlbum(activity, new PermissionCallback() {
            @Override
            public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                MatisseUtil.launchMatisse(activity, maxNum, clipRate, callback);
            }

            @Override
            public void noPermission(List<String> denied, boolean quick) {
                if (quick) {
                    PermissionDialog.showJumpPermissionDialog(activity, ResUtil.getStr(R.string.permission_select_photo_denied));
                } else {
                    ToastUtil.show(ResUtil.getStr(R.string.permission_select_photo_denied));
                }
                if (callback != null) {
                    callback.onFinish(new ArrayList<Item>());
                }
            }
        });
    }

    public static void launchCamera(Activity activity, float clipRate, PhotoCallback callback) {
        launchCamera(activity, clipRate, callback, null);
    }

    public static void launchCamera(Activity activity, float clipRate, PhotoCallback callback, CameraConfig config) {
        if (LibBaseUtil.getBaseConfig().isGooglePc) {
            ToastUtil.show(R.string.device_not_support_the_op);
            return;
        }
        WPPermission.with(activity)
                .requestDialogTip(ResUtil.getStr(R.string.permission_access_camera))
                .permission(Manifest.permission.CAMERA)
                .request(new PermissionCallback() {
                    @Override
                    public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                        if (isAll) {
                            launchCameraInternal(activity, clipRate, callback, config);
                        }
                    }

                    @Override
                    public void noPermission(List<String> denied, boolean quick) {
                        if (quick) {
                            PermissionDialog.showJumpPermissionDialog(activity, ResUtil.getStr(R.string.permission_access_camera_denied));
                        } else {
                            ToastUtil.show(ResUtil.getStr(R.string.permission_access_camera_denied));
                        }
                    }
                });
    }

    private static void launchCameraInternal(Activity activity, float clipRate, final PhotoCallback callback, CameraConfig config) {
        MatisseUtil.launchCameraActivity(activity, destPath, ActivityResultCode.CAMERA_REQUEST_CODE, clipRate, new PhotoCallback() {
            @Override
            public void onFinish(List<Item> items) {
                //解决连续拍照，文件路径重用导致的一些问题
                if (items == null || items.size() == 0) return;
                final List<Item> newItems = new ArrayList<>();
                Item item = items.get(0);
                final String cameraPath = item.path;
                final String newFilePath = FileConfig.getImageCacheDirPath() + "camera_" + System.currentTimeMillis() + ".jpg";

                ThreadUtil.runInOtherThread(new Runnable() {
                    @Override
                    public void run() {
                        if (!FileUtil.safeCopyFile(cameraPath, newFilePath)) {
                            ToastUtil.show(ResUtil.getStr(R.string.permission_handle_photo_failed));
                        } else {
                            BitmapUtil.rotateBitmapIfNeed(newFilePath, BitmapUtil.getOrientation(newFilePath));//旋转
                            ThreadUtil.runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    item.path = newFilePath;
                                    newItems.add(item);
                                    if (callback != null) callback.onFinish(newItems);
                                }
                            });
                        }
                    }
                });
            }
        });
    }

    public static void registerActivityLifeCallback() {
        ActivityTaskBuilderHolder.get().register(new ActivityTask.Builder() {
            @Override
            public ActivityTask build(Activity activity) {
                return new ActivityTask() {
                    @Override
                    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
                        MatisseUtil.onActivityResult(activity, requestCode, resultCode, data,
                                ActivityResultCode.PICK_IMG_REQUEST_CODE, ActivityResultCode.CAMERA_REQUEST_CODE);
                    }
                };
            }
        });

    }

    public static boolean onActivityResult(Activity activity, int requestCode, int resultCode, Intent data, int matisseCode, int cameraCode) {
        return MatisseUtil.onActivityResult(activity, requestCode, resultCode, data, matisseCode, cameraCode);
    }

    public static void avatarStorePickImage(Activity activity) {
        Intent getImageIntent = new Intent(Intent.ACTION_PICK, null);
        getImageIntent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");//这是图片类型
        activity.startActivityForResult(getImageIntent, ActivityResultCode.REQUEST_AVATAR_STORE_PICK_IMAGE);
    }

    public static void showPhotoChooseDialog(Context context, MediaChooseLimit config, String scene, WPChoosePhotoDialog.Callback callback) {
        Activity activity = ContextUtil.getActivityFromContext(context);
        requestPhotoPermission(activity, (context1, granted, isAll, alreadyHas) -> WPChoosePhotoDialog.show(activity, config, scene, callback));
    }

    public static void requestPhotoPermission(Activity activity, PhotoPermissionCallBack photoPermissionCallBack) {
        PermissionUtil.requestOpenAlbum(activity, new PermissionCallback() {
            @Override
            public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                if (null != photoPermissionCallBack) {
                    photoPermissionCallBack.hasPermission(activity, granted, isAll, alreadyHas);
                }
            }

            @Override
            public void noPermission(List<String> denied, boolean quick) {
                if (null != photoPermissionCallBack) {
                    photoPermissionCallBack.noPermission(activity, denied, quick);
                }
            }
        });
    }

    public interface PhotoPermissionCallBack {

        void hasPermission(Context context, List<String> granted, boolean isAll, boolean alreadyHas);

        default void noPermission(Context context, List<String> denied, boolean quick) {
            if (quick) {
                PermissionDialog.showJumpPermissionDialog(context, ResUtil.getStr(com.wepie.libphoto.R.string.permission_select_photo_denied));
            } else {
                ToastUtil.show(ResUtil.getStr(com.wepie.libphoto.R.string.permission_select_photo_denied));
            }
        }
    }

    public static class CameraConfig {
        public static final int CAPTURE_PICTURE = 1;
        public static final int CAPTURE_VIDEO = 2;
        public static final int CAPTURE_PICTURE_VIDEO = 3;
        public int captureType = CAPTURE_PICTURE;

        public CameraConfig(int captureType) {
            this.captureType = captureType;
        }

        public static Intent buildCaptureIntent(@Nullable CameraConfig config, Uri destUri) {
            Intent pictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            pictureIntent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
            pictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, destUri);
            Intent videoIntent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);
            int captureType = CAPTURE_PICTURE;
            if (null != config) {
                captureType = config.captureType;
            }
            Intent res;
            switch (captureType) {
                case CAPTURE_VIDEO:
                    res = videoIntent;
                    break;
                case CAPTURE_PICTURE_VIDEO:
                    Intent chooserIntent = Intent.createChooser(pictureIntent, "Capture Image or Video");
                    chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, new Intent[]{videoIntent});
                    res = chooserIntent;
                    break;
                default:
                    res = pictureIntent;
                    break;
            }
            return res;
        }
    }
}
