package com.wepie.libphoto;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.common.MediaChooseLimit;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.FontUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ViewUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.platform.ThreadUtil;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.liblog.main.HLog;
import com.wepie.libpermission.PermissionCallback;
import com.wepie.libpermission.PermissionUtil;
import com.wepie.libpermission.WPPermission;
import com.wepie.libphoto.choose.CircleMatisseView;
import com.wepie.libphoto.choose.IMatisse;
import com.wepie.libphoto.choose.WPAlbumAdapter;
import com.wepie.libphoto.choose.WPAlbumSpinner;
import com.wepie.libphoto.clip.ClipActivity;
import com.wepie.libphoto.clip.MatisseObserver;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.internal.entity.Album;
import com.zhihu.matisse.internal.entity.CaptureStrategy;
import com.zhihu.matisse.internal.entity.Item;
import com.zhihu.matisse.internal.entity.SelectionSpec;
import com.zhihu.matisse.internal.model.AlbumCollection;
import com.zhihu.matisse.internal.model.SelectedItemCollection;
import com.zhihu.matisse.internal.ui.AlbumPreviewActivity;
import com.zhihu.matisse.internal.ui.BasePreviewActivity;
import com.zhihu.matisse.internal.ui.SelectedPreviewActivity;
import com.zhihu.matisse.internal.utils.PathUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class WPChoosePhotoDialog extends FrameLayout implements AdapterView.OnItemSelectedListener,
        AlbumCollection.AlbumCallbacks, IMatisse, MatisseObserver {
    private final Context context;
    private TextView finishTv;
    private FrameLayout container;
    private TextView previewTv;
    private ImageView originIv;
    private TextView originTv;
    private TextView bottomFinishTv;
    private WPAlbumSpinner mAlbumsSpinner;
    private WPAlbumAdapter mAlbumsAdapter;
    private SelectedItemCollection mSelectedCollection;
    private SelectionSpec mSpec;
    private RelativeLayout bottomLay;
    private View backMaskView;
    //是否支持勾选原图
    private boolean enableOrigin = false;
    //是否已勾选原图
    private boolean useOrigin = false;
    private static final int REQUEST_CODE_PREVIEW = 23;
    private final AlbumCollection mAlbumCollection = new AlbumCollection();

    private Callback callback;
    private String finishText = ResUtil.getStr(R.string.circle_choose_finish);

    private static boolean isShowing = false;
    private String scene = "";
    private boolean needRefreshData = false;
    private static final String TAG = "WPChoosePhotoDialog";
    private MediaChooseLimit mediaChooseLimit = new MediaChooseLimit(9, false);

    public WPChoosePhotoDialog(@NonNull Context context) {
        super(context);
        this.context = context;
    }

    private void init() {
        if (!(context instanceof FragmentActivity)) return;
        isShowing = true;
        WpDragDialog dialog = new WpDragDialog(context);
        this.addView(dialog);
        ViewUtil.setTopMargins(dialog, ScreenUtil.dip2px(12F));
        ViewGroup view = (ViewGroup) dialog.setContentView(R.layout.discover_choose_photo_dialog, () -> {
            if (callback != null) callback.onFinish(new LinkedList<>(), useOrigin);
            return null;
        });
        AppCompatTextView spinnerTv = view.findViewById(R.id.discover_choose_spinner_tv);
        finishTv = view.findViewById(R.id.discover_choose_finish_tv);
        container = view.findViewById(R.id.discover_choose_container);
        TextView cancelTv = view.findViewById(R.id.discover_choose_cancel_tv);
        previewTv = view.findViewById(R.id.preview_tv);
        ViewGroup originLay = view.findViewById(R.id.origin_lay);
        originIv = view.findViewById(R.id.origin_iv);
        originTv = view.findViewById(R.id.origin_tv);
        bottomFinishTv = view.findViewById(R.id.bottom_sure_tv);
        bottomLay = view.findViewById(R.id.bottom_operate_lay);
        backMaskView = view.findViewById(R.id.back_mask_view);

        bottomFinishTv.setText(finishText);
        mSpec = SelectionSpec.getInstance();
        mSelectedCollection = new SelectedItemCollection(context);
        mSelectedCollection.onCreate(null);
        mAlbumsAdapter = new WPAlbumAdapter(context, null, false);
        mAlbumsSpinner = new WPAlbumSpinner(context);
        mAlbumsSpinner.setOnItemSelectedListener(this);
        mAlbumsSpinner.setSelectedTextView(spinnerTv);
        mAlbumsSpinner.setPopupAnchorView(spinnerTv);
        mAlbumsSpinner.setMaskView(backMaskView);
        mAlbumsSpinner.setAdapter(mAlbumsAdapter);
        mAlbumCollection.onCreate((FragmentActivity) context, this);
        mAlbumCollection.loadAlbums();

        //新ui这里的完成按钮干掉了
        finishTv.setVisibility(GONE);
        cancelTv.setVisibility(GONE);
        originLay.setVisibility(enableOrigin ? VISIBLE : INVISIBLE);

        finishTv.setOnClickListener(v -> onSureClick());

        bottomFinishTv.setOnClickListener(v -> onSureClick());
        cancelTv.setOnClickListener(v -> {
            if (callback != null) callback.onFinish(new LinkedList<>(), useOrigin);
        });
        previewTv.setOnClickListener(v -> onPreviewClick());
        originLay.setOnClickListener(v -> changeOriginState());


        onUpdate();
        PermissionUtil.initPhotoPermissionLayout(view, R.id.notice_lay, R.id.notice_set, false, new PermissionCallback() {
            @Override
            public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                mAlbumCollection.refreshAlbums();
            }
        }, v -> needRefreshData = true);
    }

    private ActivityTaskManager.ActivityTaskListener activityTaskListener = new ActivityTaskManager.ActivityTaskListener() {
        @Override
        public void onActivityResumed(@NonNull Activity activity) {
            super.onActivityResumed(activity);
            if (needRefreshData && mAlbumCollection != null) {
                mAlbumCollection.refreshAlbums();
                if (WPPermission.hasImageVideoPermission(context, false)) {
                    findViewById(R.id.notice_lay).setVisibility(View.GONE);
                }
                needRefreshData = false;
            }
        }
    };

    private void changeOriginState() {
        useOrigin = !useOrigin;
        refreshOriginState();
        refreshSize();
    }

    private void refreshOriginState() {
        originIv.setImageResource(useOrigin ? R.drawable.ic_multi_select : R.drawable.ic_un_select);
    }

    private void onPreviewClick() {
        Intent intent = new Intent(context, SelectedPreviewActivity.class);
        intent.putExtra(BasePreviewActivity.EXTRA_DEFAULT_BUNDLE, mSelectedCollection.getDataWithBundle());
        intent.putExtra(BasePreviewActivity.EXTRA_RESULT_ORIGINAL_ENABLE, useOrigin);
        intent.putExtra(BasePreviewActivity.EXTRA_CHOOSE_MEDIA_LIMIT, mediaChooseLimit);
        ((Activity) context).startActivityForResult(intent, REQUEST_CODE_PREVIEW);
    }

    private void onSureClick() {
        List<Item> res = mSelectedCollection.asListWithPath();
        if (callback != null) {
            Map<String, Object> map = new HashMap<>();
            map.put("status", useOrigin ? 1 : 0);
            map.put("scene", scene);
            ApiService.of(TrackApi.class).appClick(TrackScreenName.CHOOSE_PHOTO_WINDOW, TrackButtonName.SEND, map);
            callback.onFinish(res, useOrigin);
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        MatisseUtil.registerObserver(this);
        ActivityTaskManager.getInstance().registerActivityTaskListener(activityTaskListener);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        isShowing = false;
        MatisseUtil.unregisterObserver(this);
        ActivityTaskManager.getInstance().unregisterActivityTaskListener(activityTaskListener);
        mAlbumCollection.onDestroy();
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        mAlbumCollection.setStateCurrentSelection(position);
        mAlbumsAdapter.getCursor().moveToPosition(position);
        Album album = Album.valueOf(mAlbumsAdapter.getCursor());
        if (album.isAll() && SelectionSpec.getInstance().capture) {
            album.addCaptureCount();
        }
        onAlbumSelected(album);
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    @Override
    public void onAlbumLoad(final Cursor cursor) {
        mAlbumsAdapter.swapCursor(cursor);
        // select default album.
        ThreadUtil.runOnUiThread(() -> {
            cursor.moveToPosition(mAlbumCollection.getCurrentSelection());
            mAlbumsSpinner.setSelection(context,
                    mAlbumCollection.getCurrentSelection());
            Album album = Album.valueOf(cursor);
            if (album.isAll() && SelectionSpec.getInstance().capture) {
                album.addCaptureCount();
            }
            onAlbumSelected(album);
        });
    }

    @Override
    public void onAlbumReset() {
        mAlbumsAdapter.swapCursor(null);
    }

    private void onAlbumSelected(Album album) {
        container.setVisibility(View.VISIBLE);
        container.removeAllViews();
        CircleMatisseView circleMatisseView = new CircleMatisseView(context);
        circleMatisseView.setMediaChooseLimit(mediaChooseLimit);
        container.addView(circleMatisseView);
        circleMatisseView.init(album, this, items -> {
            if (callback != null) callback.onFinish(items, useOrigin);
        });
    }

    private void refreshSize() {
        List<String> paths = mSelectedCollection.asListOfString();
        if (paths == null || paths.size() == 0 || !useOrigin) {
            originTv.setText(R.string.origin);
            return;
        }
        long size = 0;
        try {
            for (String path : paths) {
                File file = new File(path);
                size += file.length();
            }
            String fileSize = FontUtil.INSTANCE.getMediaFileSize(size);
            String text = ResUtil.getResource().getString(R.string.origin_size, fileSize);
            originTv.setText(text);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onUpdate() {
        // notify bottom toolbar that check state changed.
        boolean selected = mSelectedCollection.count() > 0;
        finishTv.setClickable(selected);
        bottomFinishTv.setClickable(selected);
        refreshSize();
        refreshOriginState();
        finishTv.setTextColor(selected ? 0xff30d395 : 0xffaaabb3);
        bottomFinishTv.setTextColor(ResUtil.getColor(selected ? R.color.color_accent : R.color.color_accent_disable));
//        bottomFinishTv.setBackgroundResource(selected ? R.drawable.sel_30d395_corner20 : R.drawable.sel_f7f8fa_corner22);
        previewTv.setTextColor(selected ? 0xFF1B1D38 : 0xFFCCCDD6);
        previewTv.setClickable(selected);
        String num = "";
        String text = "";
        if (mSelectedCollection.count() > 0) {
            num = "(" + mSelectedCollection.count() + ")";
            text = ResUtil.getStr(R.string.circle_choose_finish_new, num);
        } else {
            text = ResUtil.getStr(R.string.circle_choose_finish);
        }
        finishTv.setText(text);
        bottomFinishTv.setText(text);
        if (mSpec.onSelectedListener != null) {
            mSpec.onSelectedListener.onSelected(
                    mSelectedCollection.asListOfUri(), mSelectedCollection.asListOfString());
        }
        if (mediaChooseLimit.isSingleChat()) {
            bottomLay.setVisibility(GONE);
        }
    }

    @Override
    public void onMediaClick(Album album, Item item, int adapterPosition) {
        // 如果选择头像，直接进入裁剪页
        if (mediaChooseLimit.isSelectHead()) {
            ClipActivity.gotoClipActivity(context, PathUtils.getPath(context, item.uri), mediaChooseLimit.getClipPhotoRate());
        } else {
            Intent intent = new Intent(context, AlbumPreviewActivity.class);
            intent.putExtra(AlbumPreviewActivity.EXTRA_MEDIA_ITEM, mediaChooseLimit);
            intent.putExtra(AlbumPreviewActivity.EXTRA_ALBUM, album);
            intent.putExtra(AlbumPreviewActivity.EXTRA_ITEM, item);
            intent.putExtra(BasePreviewActivity.EXTRA_DEFAULT_BUNDLE, mSelectedCollection.getDataWithBundle());
            intent.putExtra(BasePreviewActivity.EXTRA_RESULT_ORIGINAL_ENABLE, useOrigin);
            ((Activity) context).startActivityForResult(intent, REQUEST_CODE_PREVIEW);
        }
    }

    @Override
    public SelectedItemCollection provideSelectedItemCollection() {
        return mSelectedCollection;
    }

    public void init(boolean enableOrigin, Callback photoCallback) {
        this.callback = photoCallback;
        this.enableOrigin = enableOrigin;
        init();
    }

    public static Dialog show(Activity context, MediaChooseLimit config, String scene, Callback callback) {
        if (isShowing || null == config) {
            HLog.d(TAG, HLog.USR, "show error! isShowing=" + isShowing);
            return null;
        }
        HLog.d(TAG, HLog.USR, "show! scene=" + scene + ", config=" + config);
        context.getTheme().applyStyle(R.style.matisseStyle, false);
        Set<MimeType> mimeTypes;
        if (config.isSupportAllMedias()) {
            mimeTypes = MimeType.ofAll();
        } else {
            mimeTypes = MimeType.ofImage();
        }
        Matisse.from(context)
                .choose(mimeTypes)
                .showSingleMediaType(!config.isSupportAllMedias())
                .countable(true)
                .maxSelectable(config.getMaxSelect())
                .thumbnailScale(0.8f)
                .spanCount(4)
                .originalEnable(config.getEnableOrigin())
                .capture(config.getHaveCapture())
                .captureStrategy(new CaptureStrategy(true, "test"))
                .addFilter(new SizeFilter(1024 * 1024 * 5))
                .imageEngine(new GlideEngine());
        WPChoosePhotoDialog circleChoosePhotoDialog = new WPChoosePhotoDialog(context);

        BaseFullScreenDialog baseFullScreenDialog = new BaseFullScreenDialog(context, R.style.matisse_dialog);
        baseFullScreenDialog.setContentView(circleChoosePhotoDialog);
        baseFullScreenDialog.initBottomDialog();
        baseFullScreenDialog.setCanceledOnTouchOutside(true);
        circleChoosePhotoDialog.setMediaChooseLimit(config);
        circleChoosePhotoDialog.scene = scene;
        if (!TextUtils.isEmpty(config.getFinishText()))
            circleChoosePhotoDialog.finishText = config.getFinishText();
        circleChoosePhotoDialog.init(config.getEnableOrigin(), new Callback() {
            @Override
            public void onFinish(List<Item> items, boolean origin) {
                sortItems(items);
                if (callback != null) callback.onFinish(items, origin);
                baseFullScreenDialog.dismiss();
            }

            @Override
            public boolean onCameraClick() {
                baseFullScreenDialog.dismiss();
                if (callback != null) return callback.onCameraClick();
                return false;
            }

            @Override
            public void onDialogDismiss() {
                baseFullScreenDialog.dismiss();
            }
        });
        baseFullScreenDialog.getOnBackPressedDispatcher().addCallback(new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                if (callback != null) callback.onFinish(new ArrayList<>(), false);
                baseFullScreenDialog.dismiss();
            }
        });
        baseFullScreenDialog.show();
        return baseFullScreenDialog;
    }

    // 图片排在列表前面
    private static void sortItems(List<Item> items) {
        if (null != items) {
            Collections.sort(items, (o1, o2) -> {
                boolean flag1 = o1.isVideo();
                boolean flag2 = o2.isVideo();
                int res;
                if (flag1) {
                    res = 1;
                } else if (flag2) {
                    res = -1;
                } else {
                    res = 0;
                }
                return res;
            });
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE_PREVIEW) {
            if (resultCode != Activity.RESULT_OK)
                return;
            Bundle resultBundle = data.getBundleExtra(BasePreviewActivity.EXTRA_RESULT_BUNDLE);
            ArrayList<Item> selected = resultBundle.getParcelableArrayList(SelectedItemCollection.STATE_SELECTION);
            useOrigin = data.getBooleanExtra(BasePreviewActivity.EXTRA_RESULT_ORIGINAL_ENABLE, false);
            int collectionType = resultBundle.getInt(SelectedItemCollection.STATE_COLLECTION_TYPE,
                    SelectedItemCollection.COLLECTION_UNDEFINED);
            int maxSelect = data.getIntExtra(BasePreviewActivity.EXTRA_RESULT_MAX_SELECT, 9);
            if (data.getBooleanExtra(BasePreviewActivity.EXTRA_RESULT_APPLY, false)) {
                ArrayList<Item> selectedPaths = new ArrayList<>();
                if (selected != null) {
                    for (Item item : selected) {
                        item.path = PathUtils.getPath(context, item.getContentUri());
                        selectedPaths.add(item);
                    }
                }
                if (callback != null) {
                    callback.onFinish(selectedPaths, useOrigin);
                }
            } else {
                // 类似语音房发图片场景，只能选择一张照片，预览返回之后不需要更新相册选择数据
                if (maxSelect != 1 && selected != null) {
                    mSelectedCollection.overwrite(selected, collectionType);
                    View view = container.getChildAt(0);
                    if (view instanceof CircleMatisseView) {
                        ((CircleMatisseView) view).refreshMediaGrid();
                    }
                    onUpdate();
                }
            }
        } else if (requestCode == ConstData.CLIP_REQUEST_CODE) {
            if (resultCode == Activity.RESULT_OK) {
                callback.onDialogDismiss();
            }
        }
    }

    @Override
    public boolean onCameraClick() {
        if (callback != null) return callback.onCameraClick();
        return false;
    }

    public void setMediaChooseLimit(MediaChooseLimit mediaChooseLimit) {
        this.mediaChooseLimit = mediaChooseLimit;
    }

    public interface Callback {
        void onFinish(List<Item> items, boolean origin);

        default boolean onCameraClick() {
            return false;
        }

        default void onDialogDismiss() {
        }
    }
}
