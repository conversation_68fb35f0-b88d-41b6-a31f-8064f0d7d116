ELF          (        4   tB    4    (      4   4   4                              4  4           �=  �M  �M  ,  �           ,>  ,N  ,N               4  4  4  �   �         Q�td                            p@*  @*  @*  �  �        R�td�=  �M  �M  $  $           �      Android    r20                                                             5594570                                                                  GNU �,
�k������
�I�{�{1                              +              �             �             �             &             c             �             �             �             �             �             �             �             �             �             �             �                          
                                                    +             0             6             =             E             T             l             �  �  $   
 H  	  T   
 �  P       ���  Y      
 .  ]  �    
 �  �P       ���   P       u  P       :   �  :    
 |  P       ���   �  X    
 �   P       m  P       r   =  `    
 &  u  ,    
 �  �  h    
   $P  p      __cxa_atexit LIBC libc.so libhw-pcheck.so __cxa_finalize Java_com_huiwan_libpcheck_HwPCheckJniHelper_notifyCrash Java_com_huiwan_libpcheck_HwPCheckJniHelper_regSubSig _ZN11HwPCheckSub10domainNameE _ZN11HwPCheckSub11hwRegSubSigEv _ZN11HwPCheckSub11processNameE _ZN11HwPCheckSub13g_sub_old_actE _ZN11HwPCheckSub13isMainProcessEv _ZN11HwPCheckSub13recordSigInfoEiP7siginfoPv _ZN11HwPCheckSub14crashLogFolderE time _ZN11HwPCheckSub16getGmeTimeStringEPc __errno _ZN11HwPCheckSub19hwNotifyMainProcessEv close _ZN11HwPCheckSub20notifySubCrashToMainEPKcS1_ connect _ZN11HwPCheckSub21hw_sub_signal_handlerEiP7siginfoPv sigaction _ZN11HwPCheckSub9g_sub_actE snprintf __aeabi_memclr8 sprintf strlen __android_log_print __stack_chk_fail __stack_chk_guard fclose perror fopen socket fwrite strcpy gmtime write pthread_self __sF abort fflush fprintf __aeabi_memcpy __gnu_Unwind_Find_exidx dladdr libdl.so _edata __bss_start _end liblog.so libm.so                 �
IJ�QD                "   %   &   '   )       ,       -   .           /   I���[b��qX�	<+����|%�h�_�!j'��CE��,�}挍41K��%�5�8ev�#/���8��%   0                          -                       ,               (   
          +   '                        
   $         &   !                   *                      #   /             	                                                    %       .               "                                        )                                                                ��C                          c
             s         c
           �M     �M     �M     �M     �M     �M     �M      N     N     N     N     N     N     N     N      N     $N     (N     `O     dO     hO      P     P     HO  
  DO    \O    XO  #  @O  %  LO  &  <O  *  TO  +  PO  /  xO    |O    �O    �O    �O    �O    �O  
  �O  
  �O  	  �O    �O    �O    �O  .  �O    �O    �O    �O  -  �O  "  �O     �O    �O  )  �O    �O    �O    �O    �O    �O    �O    �O    �O    �O    �O    �O    �O    �-����������A   Ə�ʌ���� Ə�ʌ���� Ə�ʌ���� Ə�ʌ���� Ə�ʌ���� Ə�ʌ���� Ə�ʌ���� Ə�ʌ��� Ə�ʌ��� Ə�ʌ��� Ə�ʌ��� Ə�ʌ��� Ə�ʌ��� Ə�ʌ��� Ə�ʌ��� Ə�ʌ�|�� Ə�ʌ�t�� Ə�ʌ�l�� Ə�ʌ�d�� Ə�ʌ�\�� Ə�ʌ�T�� Ə�ʌ�L�� Ə�ʌ�D�� Ə�ʌ�<�� Ə�ʌ�4�� Ə�ʌ�,�� Ə�ʌ�$�� Ə�ʌ��� Ə�ʌ��� Ə�ʌ��� Ə�ʌ��� Ə�ʌ���� Ə�ʌ���� Ə�ʌ���� ��  �������@  �/�����  P��/�/� �� �� ��  �� �����������@  HIxDyD h
hhh�B��?  �?  е�HxD hh F��� !�B�b\1:*��  н н�?  ��-���FH�xD�� ���  �(F����(F�����, ��F@iI�� E��2 �lryDs@F�������  Q���� ����� ��?  
  ��-� ���mFCH,�
FxDO�a�� ���  G� F����<H=IxD�� #yD ��� � F2F�G F����7M&}D����F0F)F�G F����2I&2J@��5yDzD	hh)0F����-M -J#F}DzD)F����"�(! F���� F������!0F����$H#F$IxDyD hh0F�G"J )F3FzD����I0FyD����p�,�F(F��^�F(F"#F���� F�����I JyDzD��|�W��� �
��m�� ���X� �$?  ?  �  �  �>  �  �  �  �>  �  �  �  T  �  ��-� �FI�FJFyDzD 3F��D���`�H���b�I J3FyDzD��6�0FIF��\�M $H}DxD�� �	�(Y�B�Z�$00FIFBF�G4,�ѽ� � ��  *  �  &  B"  �=  ��M��H &IJxDyDzD�� �
h!h����  
L|D�T�& AF*F��"�56.��]���\=  V=  \=  �!  ���F hFF "���2(F�GI "yD	h`!F(h���2(F�G
I "yD	h`(h�h���2(F�GIyD	h`��@� ��<  �<  �<  ��M��F hFF "���2(F�GF(h!F "���2(F�GF0F]�����@���  ��-� ��F8H�FxD�� ���  6I6JyD�zD ���� ! "$����F0>�
�	n!HF��^�	� 1F����0F��@��6�*IF*J yD+FzD ���\��(FIF����0'�@F��"�F(FAF����(F����!I !JyDzD��D�  ���  Q� ��� ��� �I JyDzD��0�HxD�����h IJyDzD��$�
HxD����O��0��X<  ^  �  �  L  Y    �  �  w    �  �  h +	�#`#P�	�`pG��l�� м����  pG��L#�O��O�#`�� �pG��-��F�H�F�FxDFO�  h� h�  �_��p@��UF�E�� ��� �
� �_�`
�� F
! ��O� _�Ha-��D0��� �8 	( ����� �0HZ}��E��؀�� O�1��0\A�  * �̀ F ! #O�  ��� ��
A������ �� o�B F
!� ���O� ��H�  � ( ��� � F ! #O�  ��� F
! ������  ! #O� � ��8@��" F ! �O�}��)O�	 O����Qo����o��EsҊ� 2\	  *}���z� F ! #O�  �.��
Z��� �(\ъ� �������1\R		�@�B*L�1#A� B F! ���
O� 9�_�p<�� !0# � " F ���O� )�� !0#�� %  �E%Ҋ�
�
q\�	� �E� � ��� F
! �:�� �  �rU�� #0\ �� � 0@�2��	 
���	�� F! ��� F! �3� ��hQ���� ����� ��:  ��M�����hFF)
�1� -��0F)F"F ����C� (	�+�-�0F ���-� ���q0F"F �� (� �]���H@�^6IJxDKyDLzD h{D|D�� d � F���� F�������� �28  /  �  P  �  ��M�����hFF)
�9� , ���2h(F!F ���C� (	�+�,�(F �I��,� 	��� #��q(F ��� (� �]���HO�JvIJxDKyDLzD h{D|D�� d � F��n� F��p���t�7  �  =  �  G  ��-���FKHF�FxD)�� ���  �/� )o� .P��PF
! �E� (I�� &O� %������2F #� h !�PF ���x�����
 ����@	C6.���:�  %�F� ( ��PF
! ��йO�E���
��B��3F �h�*F@h��PF!�� ���H�5 (�� ���  Q���� ���p�.����� ��PF
! ��� (� ��
HO�nv
I
JxD
KyD
LzD h{D|D�� d � F���� F�������� � 7  :6  7    X  �  FF � ����FFF �+���P�� h�	 �� F ��F	 )� ��FF���FF�����M����	mF#H
��fxD�� ���  G�0F �B�
�  �`1F(F �S�nF(F1F ������ +�� ��H����   !F*F�G(��(�	(�	  � W���  Q�
�	m]�������
��`
�"F # �� ��5  ��-� ��FF<H�FFxD�� ���  � F ��
�	O��
�)j�BJШ�� O��&
и� �*i FO��1 �.�O� 

� Fo� �� FIF ��� �� +����H���� 0F)F"F�G(�(��	(������ FO��1 ����(a F �r���� ���� ���^�
HO�vI
JxD
KyD
LzD h{D|D�� d � F���� F���������4  84  5  ,  V  4  pG  �����m��
��eF(F �n��(F"F#��a�H@��%IJxDKyDLzD h{D|D�� T � F���� F���������3  �  �  �  ]  е���
IyDh!h�� ���F� )�  �"hQ��н����l3  е���
IyDh!h�� ���F� )�  �"hQ��н����83  �h�F GpG��oFF ��8�	 ��  ���4Ѐ�8���<���  ���/� ���/� ���/� ���/�е�FHJ #xD��H0zD0��J0�� � @"��"��P O��q���� F ! �,�  нP1  �3  ��M��F hF
F�h0F�G8�0h)F�h0F�G `   �H]���r�����M��F hF
F�h F�GH� h)F2Fi F�Gh� %�M� h ! %Bk F�G(F]���r�����M��F hF
FBi0F�G@�0h)F�i0F�G��    �H]��� �r�����M��F hF�FFBi F�GH� h1F*FCF��� F�G   �H]��� �r���h	jGе�FhJj!F�Gah   )�N�k`���pн  ��oFh�j�GH�� �t�����oF�� ���0��GI (� !F�� �t�����oFhRi�G��h�kG��oFh�j�G��h�kGpGpG9  ��� pG0 �0 �ո!� ���p����@	pG0 ��0 �W�е�F����  н F ��(� h!Bk F�G��� (����hp� ho��h F�G!h��h!iD Fo��G н
F ��qF(" �0 𧹐��pG��M����F�� �FF�hO��1�GF(F2FCF � ���]���  ��M����FH
FO��1xDh0h� h�h F�G � �E`h)F ���8��(�� F)F ���� ����1h��]�����,�0  0 ���!��HpG  �	�J�)�<0�)�
)�40�80�)� �  hpG���H@��TIJxDKyDMzD h{D}D�� E � F��t� F��x���|� ��/  �  �  �    ��K�)��cpG)��cpG
)�BcpG)��@�! pG���H@��TIJxDKyDMzD h{D}D�� E � F��:� F��<���@�/    �    �  ���F!� F���а��&ѕ�A ع ��A �H ��@���������B 0� ��B �� ������ ���`������� ���`�� ���H@��eIJxDKyDLzD h{D|D�� T � F���� F��������p.  m    o    ��M����F!� �FFF���а��(є�A ع ��A �H ��@���������B 0� ��B �� ������ ���`���t��� ���`�� X�]���H@��fIJxDKyDLzD h{D|D�� d � F���� F�������� ��-  �
  �  �  _  ���FH��xDh(h�  �� ��x������#F F����*hQ�8����@	��������T-  е�F �� F���@ ��е�F��A H���@�H � ���� ���B (��� ���@ �߻н��-��HF��FxDFh(h� F��4�x��k�
J0FAF���zD����� ��    �  �*hQ���� �����,  �  ���FIFyD
h)h� ! �iF��� � (�� �  )�!�+h��@�����^�b,  ��-� ��FaHF %xD�� ���  � !��Vhbh�� b��0� ���� (wаBu��	h��FP�)�h+� %h��F +Y�����FC�F3X0D���?�F��EV��C�E� O�cO� �3�O��O� ��f.�.� .H�:M�� � #}D�� � %�3M}D�� � %�7M}D�� �%��C������v	V�6� �3��F��x -B�FD��BH�BJD��V!��X>�%�� �%��lQ%��� �(F	��� �����H@�.6IxDJKyDLzD h{D|D�H@�56IJxDKyDLzD{D h|D�� d � F��� F������ �,  z+  �+  �*  �
  �
  l  �
  v+  �*  �
  �
  R  �
  ��M����L����|D%h,h��@A����@A��� �
��)h��]�����V� �H*  ��M��}i�h�� ����tF�� ��T^�5���FH�F��FDO�T�E�o��i�� ���]���  �a* �i��HxD���b � � � � � � � � � � � � � � b � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �  	�HxDpG���p($����  � � � � � � � � � � � � � � � � � � � � � � � � � � � 	�HxDpG�HxDpGvHxDpGuHxDpGuHxDpGtHxDpGtHxDpGsHxDpGsHxDpGrHxDpGrHxDpGqHxDpGqHxDpGpHxDpGpHxDpG`HxDpGnHxDpGmHxDpGmHxDpGlHxDpGlHxDpGkHxDpGkHxDpGjHxDpGjHxDpGiHxDpGiHxDpGhHxDpGhHxDpGgHxDpGgHxDpGfHxDpGfHxDpGeHxDpGeHxDpGdHxDpGdHxDpGcHxDpGcHxDpGbHxDpGbHxDpGaHxDpGaHxDpG`HxDpG`HxDpG_HxDpG_HxDpG^HxDpG_HxDpG^HxDpG^HxDpG]HxDpG]HxDpG\HxDpG\HxDpG[HxDpG[HxDpGZHxDpGZHxDpGYHxDpGYHxDpGXHxDpGXHxDpGWHxDpGWHxDpGVHxDpGVHxDpGUHxDpGUHxDpGTHxDpGTHxDpGSHxDpGSHxDpGRHxDpGRHxDpG*
  PHxDpGPHxDpGOHxDpGOHxDpG�  d  	         �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  }  {  y  w  u  s  q  o  m  k  i  g  e  c  a  _  ]  [  U  S  Q  O  %
   �����4О�<����/� ���/� ���/� ���/�xG�F ����������xG�F ������\���xG�F ����������xG�F ����������xG�F ������������   ������$���  H���  ����  ����  t���  ����  ���  N��   H���  d��   \����������  ���  ����  P���  �����������  �����������  `���  p������l���  ����  ����  $������(�����2��   \���  ����  ����  ���  8���  l������j���  ������������������������������  B������T���  ����  �����������  T���  ����  d���  ���  \���  h���  ����  ����  (���  ����  ,���  x���������   A����
�    E������    E������    E����p�    C�����    A�����    C������    E����p�    F������    C������    C������    F������    A�����    C�����    F������    A�����    A����
�    A����
�    A����
�    C������    C������    C������    C�����    A����
�    A����
�    C�����    C������    A�����    A�����    A�����    C�����    A�����    A����
�    A����
�    E������    A�����    F������    C�����    C�����    native-%04d%02d%02d%02d%02d%02d.log 	
error_code: %d,	
error_num: %d,	
signo:%d 	
thread: %ld 	
process: %s SIG_LOG error crash: %s
 %s%s write log file: %s
 w open file failed
 hw_sig_handle receive: %d hw_sig_handle notify main: %d notify main invoked socket create failed socket create failed
 try connect: %d, path: %s Connect fail %d Connect fail
 notify finished libunwind: %s %s:%d - %s
 _Unwind_Resume /Volumes/Android/buildbot/src/android/ndk-release-r20/external/libcxx/../../external/libunwind_llvm/src/Unwind-EHABI.cpp _Unwind_Resume() can't return _Unwind_VRS_Set unsupported register class _Unwind_VRS_Pop unwind_phase2 during phase1 personality function said it would stop here, but now in phase2 it did not stop here _Unwind_VRS_Get_Internal /Volumes/Android/buildbot/src/android/ndk-release-r20/external/libcxx/../../external/libunwind_llvm/src/UnwindCursor.hpp getRegister /Volumes/Android/buildbot/src/android/ndk-release-r20/external/libcxx/../../external/libunwind_llvm/src/Registers.hpp unsupported arm register setRegister getFloatRegister Unknown ARM float register setFloatRegister %s getInfoFromEHABISection unknown personality routine index inlined table detected but pr function requires extra words pc lr sp r0 r1 r2 r3 r4 r5 r6 r7 r8 r9 r10 r11 r12 s0 s1 s2 s3 s4 s5 s6 s7 s8 s9 s10 s11 s12 s13 s14 s15 s16 s17 s18 s19 s20 s21 s22 s23 s24 s25 s26 s27 s28 s29 s30 s31 d0 d1 d2 d3 d4 d5 d6 d7 d8 d9 d10 d11 d12 d13 d14 d15 d16 d17 d18 d19 d20 d21 d22 d23 d24 d25 d26 d27 d28 d29 d30 d31 unknown register                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              (            =  ?  A  O  U  [  k  q  w  �  �  �  �    }  �     lO          X           X              ���o      �           �  
   �  ���o�     @	     �     �     s                 �M              ���o   ���o�
  ���o�
  ���o   ���o  ���o                                                                                       �  a  �              h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
  h
   P  �P   Android (5220042 based on r346389c) clang version 8.0.7 (https://android.googlesource.com/toolchain/clang b55f2d4ebfd35bf643d27dbca1bb228957008617) (https://android.googlesource.com/toolchain/llvm 3c393fe7a7e13b0fba4ac75a01aa683d7a5b11cd) (based on LLVM 8.0.7svn)       	      GNU gold 1.12   A3   aeabi )   ARM v7 
A	
"& .shstrtab .note.android.ident .note.gnu.build-id .dynsym .dynstr .gnu.hash .gnu.version .gnu.version_d .gnu.version_r .rel.dyn .rel.plt .text .ARM.exidx .ARM.extab .rodata .fini_array .data.rel.ro .dynamic .got .data .bss .comment .note.gnu.gold-version .ARM.attributes                                                   4  4  �                           �  �  $                  2         �  �                 :         �  �  �                 B   ���o   �  �  �                F         @	  @	  \               L   ���o   �
  �
  `                Y   ���o   �
  �
                  h   ���o       @                w   	      X  X                  �   	   B   X  X                �         h
  h
  �                 �             ,                 �     p�   @*  @*  �  
             �         ,  ,  �                 �         �-  �-                   �         �M  �=                   �         �M  �=  H                  �         ,N  ,>                 �         <O  <?  �                  �          P   @                    �         P  @  �                  �      0       @  	                �              A                    �     p        0A  4                                dA                   