ELF                  4   ,"      4    (      4   4   4                              �  �           d  d.  d.  �  ,           l  l.  l.               4  4  4  �   �         P�tdh  h  h  \   \         Q�td                          R�tdd  d.  d.  �  �           �      Android    r20                                                             5594570                                                                  GNU ��c@�y!kh��
w��0�                              +              :              7             ^             �             �             �                          P             �             �             �             �             �             �             �             �             �             �             �                          "  0  �   
 e  �     
   0       ���  �
  7    
 V  �  G   
 #  �0       ���   0       �  0       K   �  W    
   0       ���      �    
 �   0       �  0       �   �  �    
 <  �
  V    
 �     �    
    0  p      __cxa_atexit LIBC libc.so libhw-pcheck.so __cxa_finalize __stack_chk_fail Java_com_huiwan_libpcheck_HwPCheckJniHelper_notifyCrash Java_com_huiwan_libpcheck_HwPCheckJniHelper_regSubSig _ZN11HwPCheckSub10domainNameE _ZN11HwPCheckSub11hwRegSubSigEv _ZN11HwPCheckSub11processNameE _ZN11HwPCheckSub13g_sub_old_actE time _ZN11HwPCheckSub13isMainProcessEv strlen _ZN11HwPCheckSub13recordSigInfoEiP7siginfoPv sprintf _ZN11HwPCheckSub14crashLogFolderE snprintf _ZN11HwPCheckSub16getGmeTimeStringEPc perror _ZN11HwPCheckSub19hwNotifyMainProcessEv connect _ZN11HwPCheckSub20notifySubCrashToMainEPKcS1_ close _ZN11HwPCheckSub21hw_sub_signal_handlerEiP7siginfoPv __errno _ZN11HwPCheckSub9g_sub_actE __android_log_print write fclose strcpy fopen socket fwrite gmtime memset pthread_self sigaction _edata __bss_start _end liblog.so libm.so libdl.so                �
IJ�QD                           !       $       %   &           '   I���[b��qX�	<+����|%�h�_�!j'��CE��,�}挍41K��%�5�8ev�#/���8��%   (                          %                       $         
                    #                                                                  "       '          &               
                                  	                                                        !                                                     ��C                          c
           d.     h.      0     �/    |/    �/    �/  "  �/  #  �/  '  �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/    �/  	  �/  
  �/  
  �/    �/        ��   ��       ��   h    �������   h   �������   h   �������   h   ������   h    ������    h(   ������$   h0   ������(   h8   �p�����,   h@   �`�����0   hH   �P�����4   hP   �@�����8   hX   �0�����<   h`   � �����@   hh   ������D   hp   � �����H   hx   �������L   h�   �������P   h�   �������T   h�   �������X   h�   ������\   h�   ������`   h�   ������d   h�   ������h   h�   �p���S���    [���"  ��l   �$�b�����[Ð������������Ð������������������������������S���    [��{"  �D$��t�Ѓ�[ÐS���    [��["  ����l   ��|���P�t$Q�������[ÐS���    [��+"  �������[�f��t& U��S������    [��"  ���������������1�0��������e�[]É���'    U��SV������    [���!  �������0�4$���������~ 1Ґ���������������<:tB9�|��1��e�^[]Ív ��'    U��SWV������    [��c!  �ue�   �D$���|$W�>�����W�E������l  H�PB�������0�p�p�pRQWV�,����� e�
   ;L$u�e�^_[]������
�������������U��SWV�����  �    [���   �ue�   ��$�  ����$�   h   j W��������������6�v�uPW������W�m�������������4�   �������PRQ�{�����W�B��������   ��  )�����������0VRQ�{�������(����� ���WPVj�r�����W�)�$�   )�$�   Ǆ$�       Ǆ$�       ���|$W�������W��������l  H�PB�������0�p�p�pRQW����$�   P������ W�)D$p)D$`)D$P)D$@)D$0)D$ )D$)$��������9�����$�   R�0Q�t$V�p�������>���VPWj��������R���PV��������t,�ƃ���$�   ��P������VjPW������V���������T���PWj�H�����e�   ;�$�  u�e�^_[]���������'    U��SWV������    [�ó  �}��f����� ���WP�L$Qj��������������0��V�g�������~G1ɐ��������������<:tA9�|��*��������V�0�"�����������WP�t$j���������uVW�������������   �E���D�����ዋ������PVj��j��������PVj�Q�X��������PVj�Q �F��������PVj�Q0�4��������PVj�QP�"��������PVj�Q`���������PVj�Q@���e�^_[]É���'    U��SWV������    [��c  ��������������F   ��������WVj��������GPVj��������G PVj��������G0PVj�������G@PVj�������GPPVj��������`WVj�������e�^_[]Í�    ��    U��SWV������    [�ó  �}�u���j �uV���  ������������j WV���  ������������j �uV���  �����������������������F   ��������WVj��������GPVj��������G PVj�������G0PVj�������G@PVj�������GPPVj��������`WVj�������e�^_[]Ít& U��SWV������    [�ó  �}���j �uW���  ���Ƌ��j �uW���  ��PV�T������e�^_[]Ð���&    U��SWV�����   �    [��P  e�   ��$�   ���������� ���P�L$Qj�|�����j jj������������   �Ƌ}�D$W�)D$`)D$P)D$@)D$0)D$ )D$�D$t    �D$p    �D$z    �D$v    ��WP�~�����f�D$ ��W�������ǃ����������uVP�t$$j��������D$WPV�K��������tr�}��W�G�����PWV�<�����V�C����������P�t$j������1�e�
   ;�$�   uZ�e�^_[]Ã�������P�t$j�\������������"������������0Q�t$j�8����������P�����������������f�����{������ ���2���{���{���h���{���{���{���{���D���{���{���{���{���{���{���{���{���{���{���{���{���{���{���V���native-%04d%02d%02d%02d%02d%02d.log 	
error_code: %d,	
error_num: %d,	
signo:%d 	
thread: %ld 	
process: %s SIG_LOG error crash: %s
 %s%s write log file: %s
 w open file failed
 hw_sig_handle receive: %d hw_sig_handle notify main: %d notify main invoked socket create failed socket create failed
 try connect: %d, path: %s Connect fail %d Connect fail
 notify finished           zR |�        ����7    A�B
G�l    <   ����V    A�B
H��J (   `    ����    A�B
I���oA   (   �   d���   A�B
L���A  $   �   X���G   A�B
I���:  $   �   �����    A�B
I����   $     ����    A�B
I����   $   0  ����W    A�B
I���J   (   X  ����   A�B
L���?A  $   �  �����   FJtx ?;*2$"        ;P���
   �����������l���X���������������H�������h����������0���h���X���h���������������                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 
  �     �/     �      l
           $
     H         ���o      �           p  
   C  ���o�     \     (     2     :                 d.              ���o   ���o�	  ���o�	  ���o   ���o
  ���o                                                                           l.          F  V  f  v  �  �  �  �  �  �  �  �      &  6  F  V  f  v  �  �  �  �   0   Android (5220042 based on r346389c) clang version 8.0.7 (https://android.googlesource.com/toolchain/clang b55f2d4ebfd35bf643d27dbca1bb228957008617) (https://android.googlesource.com/toolchain/llvm 3c393fe7a7e13b0fba4ac75a01aa683d7a5b11cd) (based on LLVM 8.0.7svn)       	      GNU gold 1.12    .shstrtab .note.android.ident .note.gnu.build-id .dynsym .dynstr .gnu.hash .gnu.version .gnu.version_d .gnu.version_r .rel.dyn .rel.plt .text .rodata .eh_frame .eh_frame_hdr .fini_array .dynamic .got .got.plt .data .bss .comment .note.gnu.gold-version                                                     4  4  �                           �  �  $                  2         �  �  �              :         p  p  C                 B   ���o   �  �  �                F         \  \  <               L   ���o   �	  �	  P                Y   ���o   �	  �	                  h   ���o   
  
                   w   	      $
  $
  H                �   	   B   l
  l
  �               �         0  0  �                �         �  �  	                 �         �  �  �                �         �  �  �                 �         h  h  \                  �         d.  d                   �         l.  l                 �         |/  |                    �         �/  �  l                  �          0                        �         0     �                  �      0          	                �              !                                  ,!  �                  