ELF          �          @       �1          @ 8  @                                 0      0                   �,      �,     �,           0                    -       -      -     �      �                                        $       $                    �      �      �      �       �              P�td   �      �      �      T       T              Q�td                                                  R�td   �,      �,     �,                                 GNU ĿP'V/N"�U#7�L��*�    %   .                  "       *                        )                            (   $                          
   !          #                                         &             -                 %             	   
              '                          ,               +                                                � I*�JNAX�               "   #   $   &       )       *   +           -   �4�I��wT<��qX�[b�X�y���	<+��|%�h�_�!j'��CE�쌍41,�}�K��%�5�8ev�"/����W�8��                                                   0                                   �                     �                     �                     z                     �                     l                     [                     G                     �                     �                     �                     �                     m                     �                     �                     s                     �                     y                     �                     �                                           '   �� 1             �    �      �         ��0                ��0             +    @      �      2   �� 1             �    $      �      �    L             :   �� 1             �     0            X    0                 x      `          ��0             �     0            �     �      �       +     0             U     T      $      	    h      `       z    �      x       &   �� 1             �     @0     �        __cxa_finalize __cxa_atexit Java_com_huiwan_libpcheck_HwPCheckJniHelper_notifyCrash Java_com_huiwan_libpcheck_HwPCheckJniHelper_regSubSig _ZN11HwPCheckSub10domainNameE _ZN11HwPCheckSub11hwRegSubSigEv _ZN11HwPCheckSub11processNameE _ZN11HwPCheckSub13g_sub_old_actE _ZN11HwPCheckSub13isMainProcessEv _ZN11HwPCheckSub13recordSigInfoEiP7siginfoPv _ZN11HwPCheckSub14crashLogFolderE _ZN11HwPCheckSub16getGmeTimeStringEPc _ZN11HwPCheckSub19hwNotifyMainProcessEv _ZN11HwPCheckSub20notifySubCrashToMainEPKcS1_ _ZN11HwPCheckSub21hw_sub_signal_handlerEiP7siginfoPv _ZN11HwPCheckSub9g_sub_actE __android_log_print __stack_chk_fail fclose fopen fwrite gmtime memset pthread_self sigaction snprintf sprintf strlen __errno connect perror socket strcpy liblog.so libm.so libdl.so libc.so _edata __bss_start __bss_start__ __bss_end__ __end__ _end libhw-pcheck.so LIBC                                                     �         c
    O      �,                  �,                  0            0     �/       &           �/       "           �/       -           �/       #           �/       (           �/                  /                  /                  /                   /                  (/                  0/                  8/       	           @/       
           H/                  P/                  X/                  `/                  h/       
           p/                  x/                  �/                  �/                  �/                  �/                  �/                  �/                  �/                  �/                  �/                  �{���  ��G�<� � � � Ր  ��G�"<� ֐  ��G�B<� ֐  ��G�b<� ֐  ��G��<� ֐  ��G��<� ֐  ��G��<� ֐  ��G��<� ֐  ��G�=� ֐  ��G�"=� ֐  ��G�B=� ֐  ��G�b=� ֐  ��G��=� ֐  ��G��=� ֐  ��G��=� ֐  ��G��=� ֐  ��G�>� ֐  ��G�">� ֐  ��G�B>� ֐  ��G�b>� ֐  ��G��>� ֐  ��G��>� ֐  ��G��>� ֐  ��G��>� ֐  ��G�?� ր  �   �����_����@  �  ��_�  ����  �B  �� �������  ��  ��G�)�G� @�!@�������{��C ��  ��G�@������� q+ T��	|@�jjh8_� q�  T �	�k��T� 2  �*�{A��B��_��� ��O��{��� �T�;Ո@�� �� �� �}���� �����$B)A)@)  �"� !&��������@��@�	�  T�{B��OA��� ��_�q������W��O��{��� ����U�;ը@���� *���2�*����������d@)  �!�&����*k�����u���� �����  �� ���3�!P'�b�����l����  �)�G��� �  �B�'�#@��'`�)� �!�`���1���  �s�'�  �B�'��2����E��� � o� ��S ���0���� �J���$B)A)@)  �"� !&���=��� � o������=�  ��G�  �!$(�� �@����� ���=/���  �B8(��2� ���!���  �!�(�� �A���� �� ���-���� ���� 2��@��������    �  �!�'�B�(��2����@���\�	��  T����{C��OB��WA��D��_������W���O��{��� ������ *  �  �!�'�B�(��2�*�����  ��G�@������ q� T��	|@��jh8_� q�  T �	�k��T
  �  ��G��� @�����  �  �!�'�B@)��2�*�����*������� Qm q� T	  �)�%��  +ih8J	�@ֈ  ��G��2@�"  �{B��OA��Wè�_ֈ  ��G��2@�  �  ��G�� 2%@�  �  ��G��25@�  �  ��G��2U@�
  �  ��G�� 2e@�  �  ��G�`�RE@������{B��OA��Wè` ��O���{��C ��  ��  �s�G��G��2�  �i �h ���G��2���������� ��2��������� 2����������2��������`�R����������2�������{A���� 2���O¨������O��{��� � @��������B���� � ?ֈ  ��G�����  ��@����B� ?ֈ  ��G�����  ��@����B� ?ֈ  ��  ��  ��G�s�G�)�G��2  �j �i ��  ���G��2����V����� ��2��R������ 2��N�������2��J�����`�R��F�������2��B��������{B��OA�� 2�C�;�����O��{��� � @��������B�� � ?ֈ@�� ������B��� ?��{B��OA�� ����C������C ��W	��O
��{����W�;��@���  �  �� �!�'�B�)��2������� 2� 2�*� 2%��� 1� T� �� * � o @�����<����� �%������ y���  �  � !�'�B�*��2�*������� ��*�*���� 1` T������� ��*�������*����  �  �!�'�B�+��2�����*�@���\�	� T�{K��OJ��WI��C@����_�  �  �!�'�B*��2����   � \*�  ���� @�  �  �!�'�B+��2����   � \+�����  �������� 	"native-%04d%02d%02d%02d%02d%02d.log 	
error_code: %d,	
error_num: %d,	
signo:%d 	
thread: %ld 	
process: %s SIG_LOG error crash: %s
 %s%s write log file: %s
 w open file failed
 hw_sig_handle receive: %d hw_sig_handle notify main: %d notify main invoked socket create failed socket create failed
 try connect: %d, path: %s Connect fail %d Connect fail
 notify finished    ;T   	   T���p   p����   �����   H����   ,����   ����  \���8  ����X  ����x             zR |           ����              4   ����`    L���        T    ���x    P����   $   t   x����   X�����
��     $   �   4����   P�����
�          �   �����    L����      �   ���$   P�����       ���`    P����� $   $  `����   X�����
��        �      Android    r20                                                             5594570                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             �             �             �             �             ?             �,                          (      ���o    �             �             8      
       T                           �.            @                           0
             X             �       	                            ���o           ���o    8      ���o           ���o    �      ���o                                                                                                                   p      p      p      p      p      p      p      p      p      p      p      p      p      p      p      p      p      p      p      p      p      p      p      p       -                                                      0     Android (5220042 based on r346389c) clang version 8.0.7 (https://android.googlesource.com/toolchain/clang b55f2d4ebfd35bf643d27dbca1bb228957008617) (https://android.googlesource.com/toolchain/llvm 3c393fe7a7e13b0fba4ac75a01aa683d7a5b11cd) (based on LLVM 8.0.7svn)  .shstrtab .note.gnu.build-id .gnu.hash .dynsym .dynstr .gnu.version .gnu.version_r .rela.dyn .rela.plt .text .rodata .eh_frame_hdr .eh_frame .note.android.ident .fini_array .dynamic .got .data .bss .comment                                                                                            $                              "             (      (      T                              ���o       �      �      �                             (             8      8      P                          0             �      �      T                             8   ���o       �      �      \                            E   ���o       8      8                                   T             X      X      �                            ^      B       0
      0
      @                          c             p      p      �                            h                         X                             n             h      h      �                             v             �      �      T                              �             P      P      H                             �             �      �      �                              �             �,     �,                                   �              -      -      �                           �             �.     �.                                  �              0      0                                    �             0     0                                   �      0               0                                                        1      �                              