ELF          >            @       02          @ 8  @         @       @       @       �      �                                           p      p                   �,      �<      �<      @      h                   �,      �<      �<                                                      �       �              P�td   �      �      �      �       �              Q�td                                                  R�td   �,      �<      �<      8      8                �      Android    r20                                                             5594570                                                                  GNU I�r�t�l7���_�b�                                                  +                      �                     �                     �                     %                     b                     �                     �                     �                     �                     �                     �                     �                     �                     �                     �                     �                     �                     �                     �                                          �   
 �      �      H   
 @      <         ��@              �   
 @             -   
 �      Y      #   ��0A              �     @             u     @             :    
 �      =          ��@              �    
 �      �       �     @             i    (@              r    
 �            &   
 `      E       �   
 �      �           P@      �        __cxa_atexit LIBC libc.so libhw-pcheck.so __cxa_finalize Java_com_huiwan_libpcheck_HwPCheckJniHelper_notifyCrash Java_com_huiwan_libpcheck_HwPCheckJniHelper_regSubSig _ZN11HwPCheckSub10domainNameE _ZN11HwPCheckSub11hwRegSubSigEv _ZN11HwPCheckSub11processNameE _ZN11HwPCheckSub13g_sub_old_actE _ZN11HwPCheckSub13isMainProcessEv _ZN11HwPCheckSub13recordSigInfoEiP7siginfoPv _ZN11HwPCheckSub14crashLogFolderE time _ZN11HwPCheckSub16getGmeTimeStringEPc strlen _ZN11HwPCheckSub19hwNotifyMainProcessEv close _ZN11HwPCheckSub20notifySubCrashToMainEPKcS1_ __errno _ZN11HwPCheckSub21hw_sub_signal_handlerEiP7siginfoPv perror _ZN11HwPCheckSub9g_sub_actE connect __android_log_print strcpy __stack_chk_fail socket fclose fopen write fwrite gmtime memset pthread_self sigaction snprintf sprintf _edata __bss_start _end liblog.so libm.so libdl.so                  � I
HJNAX�                          !       $       %   &           '   I���[b��qX�	<+����|%�h�_�!j'��CE��,�}挍41K��%�5�8ev�#/���8��%   (                          %                       $                          
   #                                                               "                          	                    
                    '   &                                                        !                                                     ��C                          c
           �<                    �<             �       @              @      ?                    �>                    ?                     ?         "            ?         #           ?         '           @?                    H?                    P?                    X?                    `?                    h?                    p?                    x?                    �?                    �?                    �?                    �?         	           �?                    �?                    �?         
           �?                    �?                    �?                    �?         
           �?                    �?                    �?                    �?                    �?                    �5�/  �%�/  �����%�/  h    ������%�/  h   ������%�/  h   ������%�/  h   �����%�/  h   �����%�/  h   �����%�/  h   �����%�/  h   �p����%�/  h   �`����%�/  h	   �P����%�/  h
   �@����%�/  h   �0����%z/  h   � ����%r/  h
   �����%j/  h   � ����%b/  h   ������%Z/  h   ������%R/  h   ������%J/  h   ������%B/  h   �����%:/  h   �����%2/  h   �����%*/  h   �����%"/  h   �p���H�=/  �t���@ �f.�     D  �����f.�     �H��t����     H��H�=����H��.  �:���f��     H��-  H�8H��-  H�0�'����    SH��-  H�H������H�����~#Hc�1�f.�     D  �<:tH��H9�|�[�1�[�ff.�     AVSH��I��dH�%(   H�D$H�\$H�������H��������l  P�H��D�@D�H�D�PH�5�  �    L��SAR����H��dH�%(   H;L$uH��[A^�����ffffff.�     UAWAVSH��  H���dH�%(   H��$�  L��$�   1��   L���f���D��KH�5u  1�L�����-���L�������H���M���H��Hc�H�<H�ǰ   H�5m  1������L�������Hc�H�� H�<H�ǰ   H�    �  H)�H�� H�,  H�H�8  1������L�58  H�9  �   1�L��L�������W�)�$�   )�$�   HǄ$�       H��H���Q���H���Y����l  P�H��D�@D�H�(�XH�5_  L��$�   �    L��US�0���H��W�)D$p)D$`)D$P)D$@)D$0)D$ )D$)$H�C+  H�H�5�  H��1�H��L�������H�|  �   1�L��H������H�5w  H������H��t0H��H��$�   H���q����   H��H��H�������H�������H�5  H�.  �   1�����dH�%(   H;�$�  uH�ĸ  [A^A_]��T���@ AWAVATSPI��I��A��H�5�  H��  �   1�D���b���H�K*  H�H���������~LH�1��     �<:tH��H9�|��/H�*  H�8H������H�5J  H��  �   1�D��� ���D��L���5���A���A��w"H�<  Jc�H���H��)  H�@�   �H��[A\A^A_�H��)  H�@(�   �aH��)  H�@H�   �OH��)  H�@h�   �=H��)  H���   �   �(H�k)  H���   �   �H�V)  H���   �   L��L��H��[A\A^A_���    AVSPH�-)  L�5.)  I�FA�   H�)  �   L��H���L���H�S �   L���;���H�S@�   L���*���H�S`�   L������H��H�ꀿ   L������H���   �   L�������H���   �   L��H��H��[A^����� AWAVSM��I��H��H��H�1���H  H�
E(  H�H�1�H��L����H  H�
2(  H�H�1�H��L����H  H�
(  H�H�%(  L�5&(  I�FA�   H�(  �   L��H���D���H�S �   L���3���H�S@�   L���"���H�S`�   L������H��H�ꀿ   L�������H���   �   L�������H���   �   L��H��[A^A_�����ffff.�     AWAVSI��H��H��H�1���H  I��H�1�H��L����H  L��H��[A^A_���� UAWAVATSH��   I��H��dH�%(   H�D$xH�5:  H��  �   1�������   �   1��B��������   ��H�|$W�D$^)D$P)D$@)D$0)D$ )D$)$H������f�$ H������D�`H�5�  H�v  E1��   1�I�؉��q���H���D����������t|L���������L��H��������������H�5p  H�^  �   1��)���dH�%(   H;D$xukD��H�Ā   [A\A^A_]�H�53  H��  �   1������H�=�  �(�����H�5	  H��  �   1������H�=�  �v���A�������i�������������������������������K�������������������!�����������������������������������������������������������6���native-%04d%02d%02d%02d%02d%02d.log 	
error_code: %d,	
error_num: %d,	
signo:%d 	
thread: %ld 	
process: %s SIG_LOG error crash: %s
 %s%s write log file: %s
 w open file failed
 hw_sig_handle receive: %d hw_sig_handle notify main: %d notify main invoked socket create failed socket create failed
 try connect: %d, path: %s Connect fail %d Connect fail
 notify finished               zR x�        p���              4   h���              L   `���              d   X���              |   P���              �   X���              �   `���E    A�AC 4   �   �����    BAD0��R8B@I0TABA0    L     ����<   ABB A(G�	����=�	A�	I�	�(A BBAA�	     L   T  ����Y   BBB A(A0�����(A BBBA0}(A BBB     $   �  �����    BAA ���AB,   �  p���   BBA ����BB      ,   �  P���=    BBA ���oBB       D   ,  `����   ABB B(A0G������0A(B BBAA�  $   t  �����   FJw� ?;*3$"        ;`���   d�����������|�����������������$�������4�������T�������t����������,���T���d����������������������,�������\�����������                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �             (?             @             
                           8             �       	              ���o                  �                           �      
       C      ���o    �	             p
             (             2             :                                         �<                                  ���o           ���o    �      ���o    �      ���o           ���o          ���o                                                                                                                                                           �<                      f      v      �      �      �      �      �      �      �      �                  &      6      F      V      f      v      �      �      �      �      �      �       @       Android (5220042 based on r346389c) clang version 8.0.7 (https://android.googlesource.com/toolchain/clang b55f2d4ebfd35bf643d27dbca1bb228957008617) (https://android.googlesource.com/toolchain/llvm 3c393fe7a7e13b0fba4ac75a01aa683d7a5b11cd) (based on LLVM 8.0.7svn)       	      GNU gold 1.12    .shstrtab .note.android.ident .note.gnu.build-id .dynsym .dynstr .gnu.hash .gnu.version .gnu.version_d .gnu.version_r .rela.dyn .rela.plt .text .rodata .eh_frame .eh_frame_hdr .fini_array .dynamic .got .got.plt .data .bss .comment .note.gnu.gold-version                                                                                             �                                           �      �      $                              2             �      �      �                          :             �      �      C                             B   ���o       �	      �	      �                             F             p
      p
      <                           L   ���o       �      �      P                            Y   ���o       �      �                                  h   ���o                                                w             8      8      �                            �      B       
      
      @                          �             P      P      �                            �             �      �      �                             �             h      h      �                            �             P      P      �                             �             �      �      �                              �             �<      �,                                   �             �<      �,                                  �             �>      �.      0                              �             (?      (/      �                              �              @       0                                    �             @      0                                    �      0               0      	                            �                      1                                                          01      �                              