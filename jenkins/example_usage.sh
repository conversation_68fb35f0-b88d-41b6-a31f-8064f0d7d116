#!/bin/bash

# OSS上传功能使用示例
# 展示如何配置和使用lint报告上传到阿里云OSS的功能

echo "📚 OSS上传功能使用示例"
echo "========================"

# 1. 设置环境变量示例
echo "1️⃣ 设置环境变量:"
echo ""
cat << 'EOF'
# 必需的环境变量
export OSS_ACCESS_KEY_ID="LTAI5t..."              # 你的阿里云AccessKey ID
export OSS_ACCESS_KEY_SECRET="your_secret_key"    # 你的阿里云AccessKey Secret

# 可选的环境变量 (有默认值)
export OSS_ENDPOINT="oss-cn-hangzhou.aliyuncs.com"  # OSS端点，默认杭州
export OSS_BUCKET="wepie-lint-reports"              # OSS存储桶名称
export OSS_BASE_PATH="lint-reports"                 # OSS中的基础路径
EOF

echo ""
echo "2️⃣ 安装ossutil工具:"
echo ""
echo "   ./jenkins/install_ossutil.sh"

echo ""
echo "3️⃣ 测试OSS上传功能:"
echo ""
echo "   ./jenkins/test_oss_upload.sh"

echo ""
echo "4️⃣ 运行lint检查并上传报告:"
echo ""
echo "   ./jenkins/lint_execution.sh /path/to/workspace branch_name channel_name job_id"

echo ""
echo "📋 示例命令:"
echo ""
echo "   # 设置环境变量"
echo "   export OSS_ACCESS_KEY_ID='your_key_id'"
echo "   export OSS_ACCESS_KEY_SECRET='your_secret'"
echo "   export OSS_BUCKET='my-lint-reports'"
echo ""
echo "   # 运行lint检查 (master分支, official渠道, job ID 123)"
echo "   ./jenkins/lint_execution.sh /var/jenkins/workspace master official 123"

echo ""
echo "🔍 预期结果:"
echo ""
echo "   ✅ Lint检查执行完成"
echo "   ☁️ 报告上传到OSS: oss://my-lint-reports/lint-reports/master/official/lint-report-master-official-20231201_143022-123.html"
echo "   📱 飞书通知发送成功，包含OSS访问链接"
echo "   📎 OSS访问链接: https://my-lint-reports.oss-cn-hangzhou.aliyuncs.com/lint-reports/master/official/lint-report-master-official-20231201_143022-123.html"

echo ""
echo "⚠️ 注意事项:"
echo ""
echo "   • 确保OSS存储桶已创建且有适当权限"
echo "   • AccessKey需要有OSS读写权限"
echo "   • 如果OSS上传失败，会自动降级使用Jenkins链接"
echo "   • 建议在生产环境中使用RAM子账号的AccessKey"

echo ""
echo "📖 更多信息请查看: jenkins/OSS_UPLOAD_README.md"
