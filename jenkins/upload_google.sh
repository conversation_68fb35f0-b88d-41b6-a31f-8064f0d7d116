#!/bin/bash
server_config="$1"
project_dir="$2"
branch="$3"
store="$4"
build_third_app="$5"
job_id="${6}"

case "$server_config" in
  "ch") package_name="com.wejoy.weplay" ;;
  "ja") package_name="com.wejoy.weplay.jp" ;;
  "ar") package_name="com.wejoy.jackaroo" ;;
  "kr") package_name="com.wejoy.weplay.kr" ;;
  "us") package_name="com.wejoy.weplay.us" ;;
  *)
    echo "❌ 未找到对应的包名"
    exit 1
    ;;
esac

echo "选择的包名: $package_name"
webhook="https://open.feishu.cn/open-apis/bot/v2/hook/8fcbb7c7-09c4-44fb-9fbb-da16ae86888b"

upload_app_dir=$project_dir/tools/aab_upload_google
app_path=$upload_app_dir/publish_app_jenkins
release_notes_path=$upload_app_dir/aab/release_notes.json
aab_file_dir=$project_dir/jenkins/output_with_jiagu

echo "上传 app 路径: ${app_path}"
echo "版本更新说明路径: ${release_notes_path}"
echo "aab 文件路径: ${aab_file_dir}"

# 检查 aab 文件是否存在
aab_files=($(find "$aab_file_dir" -type f -name "*.aab"))

if [[ ${#aab_files[@]} -eq 0 ]]; then
  echo "❌ 错误: 未在 ${aab_file_dir} 目录下找到任何 .aab 文件!"
  exit 1
else
  echo "✅ 发现以下 .aab 文件:"
  for file in "${aab_files[@]}"; do
    echo "  - $file"
  done
fi

# 检查 release_notes.json 文件是否存在
if [[ ! -f "$release_notes_path" ]]; then
  echo "❌ 错误: 未找到版本更新说明文件: ${release_notes_path}"
  exit 1
else
  echo "✅ 版本更新说明文件存在: ${release_notes_path}"
fi

echo "🎉 所有检查通过，准备上传 AAB 文件..."

if [[ ! -x "$app_path" ]]; then
  echo "⚠️  文件没有执行权限，正在尝试修复..."
  chmod +x "$app_path"
fi

arch=$(uname -m)
if [[ "$arch" == "x86_64" ]]; then
  echo "🖥️  运行环境：64 位架构 (x86_64)"
elif [[ "$arch" == "arm64" || "$arch" == "aarch64" ]]; then
  echo "🖥️  运行环境：ARM 架构 (arm64)"
else
  echo "❌ 错误：未知的 CPU 架构 $arch，无法保证 $app_path 能正确运行"
  exit 1
fi

"$app_path" "$package_name" "$aab_file_dir"

# ========== 飞书消息通知 ==========
if [[ -n "$webhook" ]]; then
  message="安装包已上传到谷歌后台"
  content="server_config=$server_config\nbranch=$branch\nstore=$store\nbuild_third_app=$build_third_app\njob_id=$job_id\naab_files:\n$(printf '%s\n' "${aab_files[@]}")"

  curl -s -X POST "$webhook" \
    -H 'Content-Type: application/json' \
    -d "{
          \"msg_type\": \"post\",
          \"content\": {
              \"post\": {
                  \"zh_cn\": {
                      \"title\": \"$message\",
                      \"content\": [[
                          {\"tag\": \"text\", \"text\": \"$content\"}
                      ]]
                  }
              }
          }
        }" | tee /tmp/feishu_resp.log

  echo "飞书响应: $(cat /tmp/feishu_resp.log)"
else
  echo "⚠️  未配置 webhook，跳过发送飞书通知"
  exit 1
fi
