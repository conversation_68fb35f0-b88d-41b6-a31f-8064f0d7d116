#!/bin/bash
echo "适配android 12 打包用 java 17"
export JAVA_HOME=/usr/local/jdk17.0.5
export CLASSPATH=.:${JAVA_HOME}/lib
export PATH=${JAVA_HOME}/bin:$PATH

WORKSPACE="$1"
branch="$2"
server_config="$3"
store="$4"
version_code="$5"
build_third_app="${6}"
upload_to_google="${7}"
job_id="${8}"

echo "$WORKSPACE $branch $server_config $store $version_code"

codeDir=$WORKSPACE/wejoy-android
cd $codeDir
sed 's/org.gradle.caching=true/org.gradle.caching=false/g' gradle.properties > gradle1.properties
mv -f gradle1.properties gradle.properties

echo "./gradlew clean"
./gradlew clean
buildChannel=""
if [[ $store =~ "huawei" ]]; then
  echo "./gradlew :wepie:bundleHuaweiRelease"
  buildChannel="Huawei-Release"
  ./gradlew :wepie:bundleHuaweiRelease -PBUILD_SERVER_CONFIG=${server_config} -PBUILD_PAD_CONFIG=false -PCOMPILE_DEBUG_SETTINGS=false -PVERSION_CODE=${version_code}
else
  buildChannel="Official-Release"
  echo "./gradlew :wepie:bundleOfficialRelease"
  ./gradlew :wepie:bundleOfficialRelease -PBUILD_SERVER_CONFIG=${server_config} -PBUILD_PAD_CONFIG=false -PCOMPILE_DEBUG_SETTINGS=false -PVERSION_CODE=${version_code}
fi

#清除修改的文件
git checkout .
git clean -df ./wepie_asset_pack

cd ${WORKSPACE}

OUT_DIR=$WORKSPACE/wejoy-android/jenkins/output_with_jiagu

#获取版本号，包名
echo "从gradle读取versionName"
gradle_file="$codeDir/project_info.txt"
versionName="0.0.1"
versionCode=1000
pkg="default"
while read line
do
    #echo "${line}"
    if [[ $line =~ "applicationId \"" ]];then
    	pkg=${line}
    fi
    if [[ $line =~ "versionName \"" ]];then
    	versionName=${line}
    	#防止被无关内容替换，这里break掉
    fi
    if [[ $line =~ "versionCode" ]];then
    	versionCode=${line}
    	#防止被无关内容替换，这里break掉
    fi
done < $gradle_file

symbol="\""
versionName=${versionName/versionName/}
versionCode=${versionCode/versionCode/}
pkg=${pkg/applicationId/}
versionName=${versionName//${symbol}/}
versionCode=${versionCode//${symbol}/}
pkg=${pkg//${symbol}/}
echo "包名：$pkg"
echo "版本号：$versionName"
echo "build：$versionCode"
echo "branch: $branch"
echo "服务器: $server_config"
echo "合规检查"
cd $codeDir
# 配置tools路径，appShark-wrapper会将appShark的jar包释放到这个路径
export TOOLS_DIR=/opt/weplay_android
java -jar ./tools/AppShark-wrapper.jar -config ./tools/config/config.json5 \
                                   -input $codeDir \
                                   -buildId $BUILD_ID \
                                   -branch $branch \
                                   -baseBranch os-jackaroo-release \
                                   -appId 7a9b3c8d5e6f1a2b \
                                   -buildChannel $buildChannel
bash $WORKSPACE/wejoy-android/jenkins/wejoy-jk-bb.sh weplay ${branch} $pkg $versionName $versionCode $store


if [[ "$upload_to_google" == "true" && "$build_third_app" == "false" && "$store" == "google" ]]; then
  echo "上传到Google内部测试！"
  bash "$WORKSPACE"/wejoy-android/jenkins/upload_google.sh "${server_config}" "$WORKSPACE"/wejoy-android "${branch}" "${store}" "${build_third_app}" "${job_id}"
else
  echo "条件不满足，跳过上传到Google内部测试！"
fi