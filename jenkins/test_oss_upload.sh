#!/bin/bash

# OSS上传功能测试脚本
# 用于验证lint报告上传到阿里云OSS的功能是否正常工作

set -e

echo "🧪 开始测试OSS上传功能..."

# 检查必要的环境变量
check_env_vars() {
    echo "📋 检查环境变量配置..."
    
    local missing_vars=()
    
    if [ -z "$OSS_ACCESS_KEY_ID" ]; then
        missing_vars+=("OSS_ACCESS_KEY_ID")
    fi
    
    if [ -z "$OSS_ACCESS_KEY_SECRET" ]; then
        missing_vars+=("OSS_ACCESS_KEY_SECRET")
    fi
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        echo "❌ 缺少必要的环境变量:"
        for var in "${missing_vars[@]}"; do
            echo "   - $var"
        done
        echo ""
        echo "请设置以下环境变量:"
        echo "export OSS_ACCESS_KEY_ID='your_access_key_id'"
        echo "export OSS_ACCESS_KEY_SECRET='your_access_key_secret'"
        echo "export OSS_ENDPOINT='oss-cn-hangzhou.aliyuncs.com'  # 可选"
        echo "export OSS_BUCKET='wepie-lint-reports'              # 可选"
        exit 1
    fi
    
    echo "✅ 环境变量检查通过"
    echo "   OSS_ACCESS_KEY_ID: ${OSS_ACCESS_KEY_ID:0:8}***"
    echo "   OSS_ACCESS_KEY_SECRET: ${OSS_ACCESS_KEY_SECRET:0:8}***"
    echo "   OSS_ENDPOINT: ${OSS_ENDPOINT:-oss-cn-hangzhou.aliyuncs.com}"
    echo "   OSS_BUCKET: ${OSS_BUCKET:-wepie-lint-reports}"
}

# 创建测试用的HTML文件
create_test_file() {
    echo "📝 创建测试HTML文件..."
    
    TEST_FILE="/tmp/test-lint-report.html"
    
    cat > "$TEST_FILE" << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lint报告测试文件</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .content { margin-top: 20px; }
        .success { color: #28a745; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 OSS上传测试报告</h1>
        <p class="info">这是一个用于测试OSS上传功能的示例HTML文件</p>
    </div>
    
    <div class="content">
        <h2>测试信息</h2>
        <ul>
            <li><strong>生成时间:</strong> $(date)</li>
            <li><strong>测试目的:</strong> 验证lint报告能否成功上传到阿里云OSS</li>
            <li><strong>文件大小:</strong> 约2KB</li>
        </ul>
        
        <h2>功能验证</h2>
        <p class="success">✅ 如果你能看到这个页面，说明OSS上传功能工作正常！</p>
        
        <h2>技术细节</h2>
        <ul>
            <li>使用ossutil或curl进行上传</li>
            <li>支持自动降级到Jenkins链接</li>
            <li>集成飞书通知功能</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    echo "✅ 测试文件创建完成: $TEST_FILE"
    echo "   文件大小: $(du -h "$TEST_FILE" | cut -f1)"
    
    echo "$TEST_FILE"
}

# 测试OSS连接
test_oss_connection() {
    echo "🔗 测试OSS连接..."
    
    local endpoint="${OSS_ENDPOINT:-oss-cn-hangzhou.aliyuncs.com}"
    local bucket="${OSS_BUCKET:-wepie-lint-reports}"
    
    # 测试网络连接
    if ! ping -c 1 "$endpoint" &> /dev/null; then
        echo "❌ 无法连接到OSS端点: $endpoint"
        return 1
    fi
    
    echo "✅ OSS端点连接正常: $endpoint"
    
    # 如果ossutil可用，测试认证
    if command -v ossutil &> /dev/null; then
        echo "🔑 测试OSS认证..."
        
        # 创建临时配置
        local config_file="/tmp/ossutil_test_config"
        ossutil config -e "$endpoint" -i "$OSS_ACCESS_KEY_ID" -k "$OSS_ACCESS_KEY_SECRET" -L CH --config-file "$config_file" &> /dev/null
        
        # 测试列举存储桶
        if ossutil ls "oss://$bucket/" --config-file "$config_file" &> /dev/null; then
            echo "✅ OSS认证成功，存储桶访问正常"
        else
            echo "⚠️ OSS认证可能有问题，但会尝试继续测试"
        fi
        
        rm -f "$config_file"
    else
        echo "⚠️ ossutil不可用，将使用curl方式测试"
    fi
}

# 执行上传测试
test_upload() {
    echo "📤 开始上传测试..."
    
    local test_file="$1"
    local test_key="test-uploads/oss-upload-test-$(date +%Y%m%d_%H%M%S).html"
    
    # 导入lint_execution.sh中的上传函数
    source "$(dirname "$0")/lint_execution.sh"
    
    # 执行上传
    echo "正在上传测试文件到: $test_key"
    local upload_result
    upload_result=$(upload_to_oss "$test_file" "$test_key")
    local upload_status=$?
    
    if [ $upload_status -eq 0 ]; then
        echo "✅ 上传测试成功!"
        echo "📎 访问链接: $upload_result"
        
        # 测试访问链接
        echo "🌐 测试访问链接..."
        if curl -s --head "$upload_result" | head -n 1 | grep -q "200 OK"; then
            echo "✅ 链接访问正常"
        else
            echo "⚠️ 链接可能需要签名访问或存在其他问题"
        fi
        
        return 0
    else
        echo "❌ 上传测试失败"
        return 1
    fi
}

# 清理测试文件
cleanup() {
    echo "🧹 清理测试文件..."
    
    if [ -f "/tmp/test-lint-report.html" ]; then
        rm -f "/tmp/test-lint-report.html"
        echo "✅ 本地测试文件已清理"
    fi
    
    # 注意: 这里不清理OSS上的测试文件，以便验证上传结果
    echo "ℹ️ OSS上的测试文件保留，可手动清理"
}

# 主测试流程
main() {
    echo "🚀 OSS上传功能测试开始"
    echo "================================"
    
    # 检查环境变量
    check_env_vars
    echo ""
    
    # 测试OSS连接
    test_oss_connection
    echo ""
    
    # 创建测试文件
    local test_file
    test_file=$(create_test_file)
    echo ""
    
    # 执行上传测试
    if test_upload "$test_file"; then
        echo ""
        echo "🎉 OSS上传功能测试完成 - 成功!"
        echo "================================"
        echo "✅ 所有测试通过，OSS上传功能正常工作"
        echo "📋 现在可以在lint_execution.sh中使用OSS上传功能了"
    else
        echo ""
        echo "💥 OSS上传功能测试完成 - 失败!"
        echo "================================"
        echo "❌ 测试失败，请检查配置和网络连接"
        echo "📋 建议检查:"
        echo "   1. OSS AccessKey权限"
        echo "   2. 存储桶是否存在"
        echo "   3. 网络连接是否正常"
        echo "   4. ossutil是否正确安装"
    fi
    
    # 清理
    cleanup
    
    echo ""
    echo "📚 更多信息请查看: jenkins/OSS_UPLOAD_README.md"
}

# 捕获退出信号，确保清理
trap cleanup EXIT

# 运行主程序
main "$@"
