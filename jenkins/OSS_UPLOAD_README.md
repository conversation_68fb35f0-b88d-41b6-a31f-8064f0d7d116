# Lint报告上传阿里云OSS功能说明

## 📋 功能概述

本功能实现了将Android Lint检查报告自动上传到阿里云OSS存储服务，提供更稳定和持久的报告访问方式。

## 🔧 配置要求

### 1. 环境变量配置

在Jenkins或CI/CD环境中设置以下环境变量：

```bash
# 必需配置
export OSS_ACCESS_KEY_ID="your_access_key_id"           # 阿里云AccessKey ID
export OSS_ACCESS_KEY_SECRET="your_access_key_secret"   # 阿里云AccessKey Secret

# 可选配置 (有默认值)
export OSS_ENDPOINT="oss-cn-hangzhou.aliyuncs.com"      # OSS端点，默认杭州
export OSS_BUCKET="wepie-lint-reports"                  # OSS存储桶名称
export OSS_BASE_PATH="lint-reports"                     # OSS中的基础路径
```

### 2. 阿里云OSS准备

1. **创建OSS存储桶**：
   - 登录阿里云控制台
   - 创建名为 `wepie-lint-reports` 的存储桶（或自定义名称）
   - 设置适当的访问权限（建议：私有读写，通过签名URL访问）

2. **创建AccessKey**：
   - 在阿里云控制台创建AccessKey
   - 确保该AccessKey有OSS的读写权限

### 3. 安装ossutil工具

运行安装脚本：
```bash
chmod +x jenkins/install_ossutil.sh
./jenkins/install_ossutil.sh
```

或者手动安装：
```bash
# Linux x64
wget https://gosspublic.alicdn.com/ossutil/1.7.16/ossutil64
chmod +x ossutil64
sudo mv ossutil64 /usr/local/bin/ossutil

# macOS
wget https://gosspublic.alicdn.com/ossutil/1.7.16/ossutilmac64
chmod +x ossutilmac64
sudo mv ossutilmac64 /usr/local/bin/ossutil
```

## 📁 文件存储结构

上传到OSS的文件将按以下结构组织：

```
OSS存储桶/
└── lint-reports/                    # 基础路径
    └── {branch}/                    # 分支名称
        └── {channel}/               # 渠道名称
            └── lint-report-{branch}-{channel}-{timestamp}-{job_id}.html
```

示例：
```
wepie-lint-reports/
└── lint-reports/
    ├── master/
    │   ├── official/
    │   │   ├── lint-report-master-official-20231201_143022-123.html
    │   │   └── lint-report-master-official-20231201_150315-124.html
    │   └── google/
    │       └── lint-report-master-google-20231201_144500-125.html
    └── develop/
        └── official/
            └── lint-report-develop-official-20231201_141200-126.html
```

## 🚀 使用方式

### 自动上传

当运行 `lint_execution.sh` 脚本时，如果配置了OSS环境变量，脚本会自动：

1. 执行Lint检查
2. 生成HTML报告
3. 上传报告到OSS
4. 发送飞书通知（包含OSS链接）

### 手动测试

可以单独测试OSS上传功能：

```bash
# 设置环境变量
export OSS_ACCESS_KEY_ID="your_key"
export OSS_ACCESS_KEY_SECRET="your_secret"
export OSS_BUCKET="your-bucket"

# 运行脚本
./jenkins/lint_execution.sh /path/to/workspace branch_name channel_name job_id
```

## 📊 上传策略

### 双重保障机制

1. **优先使用ossutil**：
   - 如果ossutil可用且配置正确，优先使用
   - 提供更好的性能和错误处理

2. **备用curl方案**：
   - 如果ossutil不可用，自动切换到curl直接上传
   - 使用OSS REST API进行上传

3. **降级到Jenkins**：
   - 如果OSS上传完全失败，使用原有的Jenkins artifact链接
   - 确保功能不会因OSS问题而中断

### 上传文件命名

文件名格式：`lint-report-{branch}-{channel}-{timestamp}-{job_id}.html`

- `branch`: Git分支名称
- `channel`: 构建渠道（如official、google等）
- `timestamp`: 时间戳（格式：YYYYMMDD_HHMMSS）
- `job_id`: Jenkins任务ID

## 🔍 故障排查

### 常见问题

1. **OSS上传失败**：
   ```
   警告: 阿里云OSS访问密钥未配置，将跳过OSS上传
   ```
   - 检查环境变量是否正确设置
   - 验证AccessKey是否有效

2. **ossutil不可用**：
   ```
   警告: ossutil命令不可用，将尝试使用curl直接上传
   ```
   - 运行安装脚本：`./jenkins/install_ossutil.sh`
   - 或手动安装ossutil

3. **权限问题**：
   ```
   ❌ curl上传失败，HTTP状态码: 403
   ```
   - 检查AccessKey权限
   - 确认存储桶访问策略

### 调试模式

在脚本中添加调试输出：
```bash
set -x  # 在脚本开头添加，显示详细执行过程
```

### 日志查看

脚本会输出详细的上传过程日志：
- ✅ 成功标记
- ❌ 错误标记  
- ⚠️ 警告标记
- 📎 链接信息

## 🔒 安全考虑

1. **AccessKey安全**：
   - 使用最小权限原则
   - 定期轮换AccessKey
   - 不要在代码中硬编码密钥

2. **存储桶权限**：
   - 建议设置为私有访问
   - 通过签名URL提供临时访问

3. **网络安全**：
   - 使用HTTPS传输
   - 考虑使用VPC内网端点

## 📈 监控和维护

1. **定期检查**：
   - 监控OSS存储使用量
   - 清理过期的报告文件

2. **成本优化**：
   - 设置生命周期规则自动删除旧文件
   - 考虑使用低频访问存储类型

3. **备份策略**：
   - 重要报告可以设置跨区域复制
   - 定期备份关键配置

## 📞 技术支持

如有问题，请联系：
- 开发团队
- 运维团队
- 查看Jenkins构建日志获取详细错误信息
