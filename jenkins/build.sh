#!/bin/bash
echo "适配android 12 打包用 java 17"
export JAVA_HOME=/usr/local/jdk17.0.5
export CLASSPATH=.:${JAVA_HOME}/lib
export PATH=${JAVA_HOME}/bin:$PATH

WORKSPACE="$1"
branch="$2"
channel="$6"
unity_version="$7"
server_config="$8"
app_test_version="$9"
min_package="${12}"
upload_qiniu="${13}"
job_id="${14}"

echo "$WORKSPACE $branch"
echo "app_test_version=$app_test_version"
echo "channel=${channel}"
echo "min_package=${min_package}"
echo "公网上传=${upload_qiniu}"
echo "job_id=${job_id}"

codeDir=$WORKSPACE/wejoy-android

cd $codeDir
if [ "$branch" != "os-ar-dev" ] && [ "$branch" != "os-jackaroo-dev" ]
then
   sed 's/android.enableR8.fullMode=false/android.enableR8.fullMode=true/g' gradle.properties > gradle1.properties
   mv -f gradle1.properties gradle.properties
   echo 'android.enableR8.fullMode=true'
fi

git clean -df .
./gradlew clean
./gradlew ":wepie:bundle${channel}Debug" -PCOMPILE_DEBUG_SETTINGS=true -PUNITY_VERSION=${unity_version} -PBUILD_SERVER_CONFIG=${server_config} -PTEST_VERSION=${app_test_version} -PBUILD_DEBUG_WITH_MINIFY=${minify} -PBUILD_PAD_CONFIG=false -PBUILD_MIN_PACKAGE=${min_package}

# 使用检查脚本执行lint，确保每天只执行一次
chmod 777 $codeDir/jenkins/lint_execution.sh
sh $codeDir/jenkins/lint_execution.sh $WORKSPACE $branch $channel $job_id "${channel}Debug"
cd $codeDir
git clean -df ./wepie_asset_pack

#清除修改的文件
git checkout .

cd ${WORKSPACE}

OUT_DIR=$WORKSPACE/wejoy-android/jenkins/output_with_jiagu


#获取版本号，包名
echo "从gradle读取versionName"
gradle_file="$codeDir/project_info.txt"
versionName="0.0.1"
versionCode=1000
pkg="defalut"
unityVersion=""
while read line
do
    #echo "${line}"
    if [[ $line =~ "applicationId \"" ]];then
    	pkg=${line}
    fi
    if [[ $line =~ "versionName \"" ]];then
    	versionName=${line}
    	#防止被无关内容替换，这里break掉
    fi
    if [[ $line =~ "versionCode" ]];then
    	versionCode=${line}
    	#防止被无关内容替换，这里break掉
    fi

    if [[ $line =~ "unityVersion" ]];then
    	unityVersion=${line}
    	#防止被无关内容替换，这里break掉
    fi
done < $gradle_file

symbol="\""
versionName=${versionName/versionName/}
versionCode=${versionCode/versionCode/}
pkg=${pkg/applicationId/}
unityVersion=${unityVersion/unityVersion/}

versionName=`echo ${versionName//${symbol}/} | sed "s/^[ \t]*//g"`
versionCode=`echo ${versionCode//${symbol}/} | sed "s/^[ \t]*//g"`
pkg=`echo ${pkg//${symbol}/} | sed "s/^[ \t]*//g"`
unityVersion=`echo ${unityVersion//${symbol}/} | sed "s/^[ \t]*//g"`

echo "包名: $pkg"
echo "版本号: $versionName"
echo "build: $versionCode"
echo "unity: $unityVersion"
echo "server_config: $server_config"

#aab转换apk
aab_path="$codeDir/wepie/build/outputs/bundle/${channel}Debug/wepie-$channel-debug.aab"
echo "aab_path=$aab_path"
bundle_dir="$codeDir/pack_apk/bundletools"
bundle_tool=`find $bundle_dir -name "bundletool-*" -print`
if [ -d ${bundle_tool} ]
then
    echo "找不到 ${bundle_tool}"
    exit 1
fi
cd "$bundle_dir"
java -jar $bundle_tool build-apks --bundle $aab_path --output "${codeDir}/wepie/build/app.apks" --ks=../../keystores/wejoy-u.keystore --ks-pass=pass:87542701 --ks-key-alias=android --key-pass=pass:87542701 --mode=universal
mv "${codeDir}/wepie/build/app.apks" "${codeDir}/wepie/build/app.zip"
unzip "${codeDir}/wepie/build/app.zip" -d "${codeDir}/wepie/build"
universal_apk_path="${codeDir}/wepie/build/universal.apk"
echo "$universal_apk_path"
apkpath="$codeDir/wepie/build/outputs/apk/$channel/debug"
time=$(date "+%Y%m%d%H%M%S")
apk_name="wepie-${channel}-debug_${versionName}_${versionCode}_${time}.apk"
mkdir -p $apkpath
cp -rf "$universal_apk_path" "${apkpath}/${apk_name}"

cd $codeDir
bash $codeDir/jenkins/upload_test.sh $apkpath $branch $channel $unityVersion true $pkg $versionName $versionCode $server_config $upload_qiniu