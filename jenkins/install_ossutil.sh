#!/bin/bash

# 安装阿里云OSS工具ossutil的脚本
# 用于支持lint报告上传到阿里云OSS功能

set -e

echo "开始安装阿里云OSS工具 ossutil..."

# 检测系统架构
ARCH=$(uname -m)
OS=$(uname -s)

echo "检测到系统: $OS, 架构: $ARCH"

# 根据系统和架构选择下载链接
if [ "$OS" = "Linux" ]; then
    if [ "$ARCH" = "x86_64" ]; then
        DOWNLOAD_URL="https://gosspublic.alicdn.com/ossutil/1.7.16/ossutil64"
        BINARY_NAME="ossutil64"
    elif [ "$ARCH" = "aarch64" ] || [ "$ARCH" = "arm64" ]; then
        DOWNLOAD_URL="https://gosspublic.alicdn.com/ossutil/1.7.16/ossutilarm64"
        BINARY_NAME="ossutilarm64"
    else
        DOWNLOAD_URL="https://gosspublic.alicdn.com/ossutil/1.7.16/ossutil32"
        BINARY_NAME="ossutil32"
    fi
elif [ "$OS" = "Darwin" ]; then
    if [ "$ARCH" = "arm64" ]; then
        DOWNLOAD_URL="https://gosspublic.alicdn.com/ossutil/1.7.16/ossutilmac64-arm"
        BINARY_NAME="ossutilmac64-arm"
    else
        DOWNLOAD_URL="https://gosspublic.alicdn.com/ossutil/1.7.16/ossutilmac64"
        BINARY_NAME="ossutilmac64"
    fi
else
    echo "❌ 不支持的操作系统: $OS"
    exit 1
fi

# 创建临时目录
TEMP_DIR="/tmp/ossutil_install"
mkdir -p "$TEMP_DIR"
cd "$TEMP_DIR"

echo "📥 正在下载 ossutil..."
echo "下载链接: $DOWNLOAD_URL"

# 下载ossutil
if command -v wget &> /dev/null; then
    wget -O "$BINARY_NAME" "$DOWNLOAD_URL"
elif command -v curl &> /dev/null; then
    curl -L -o "$BINARY_NAME" "$DOWNLOAD_URL"
else
    echo "❌ 错误: 需要 wget 或 curl 来下载文件"
    exit 1
fi

# 检查下载是否成功
if [ ! -f "$BINARY_NAME" ]; then
    echo "❌ 下载失败"
    exit 1
fi

echo "✅ 下载完成"

# 添加执行权限
chmod +x "$BINARY_NAME"

# 安装到系统路径
INSTALL_PATH="/usr/local/bin/ossutil"

echo "📦 正在安装到 $INSTALL_PATH..."

# 检查是否有权限写入 /usr/local/bin
if [ -w "/usr/local/bin" ]; then
    cp "$BINARY_NAME" "$INSTALL_PATH"
else
    echo "需要管理员权限来安装到 /usr/local/bin"
    sudo cp "$BINARY_NAME" "$INSTALL_PATH"
fi

# 验证安装
if command -v ossutil &> /dev/null; then
    echo "✅ ossutil 安装成功!"
    echo "版本信息:"
    ossutil --version
else
    echo "❌ ossutil 安装失败"
    exit 1
fi

# 清理临时文件
cd /
rm -rf "$TEMP_DIR"

echo ""
echo "🎉 ossutil 安装完成!"
echo ""
echo "使用说明:"
echo "1. 配置OSS访问密钥:"
echo "   export OSS_ACCESS_KEY_ID='your_access_key_id'"
echo "   export OSS_ACCESS_KEY_SECRET='your_access_key_secret'"
echo ""
echo "2. 配置OSS端点和存储桶 (可选):"
echo "   export OSS_ENDPOINT='oss-cn-hangzhou.aliyuncs.com'"
echo "   export OSS_BUCKET='your-bucket-name'"
echo ""
echo "3. 现在可以使用 lint_execution.sh 脚本上传报告到OSS了"
echo ""
