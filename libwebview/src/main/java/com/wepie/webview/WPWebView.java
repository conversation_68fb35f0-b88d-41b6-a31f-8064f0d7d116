package com.wepie.webview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.webkit.CookieManager;
import android.webkit.DownloadListener;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.github.lzyzsd.jsbridge.BridgeHandler;
import com.github.lzyzsd.jsbridge.BridgeOriginWebView;
import com.github.lzyzsd.jsbridge.BridgeUtil;
import com.github.lzyzsd.jsbridge.CallBackFunction;
import com.github.lzyzsd.jsbridge.Message;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.lib.api.plugins.IWebProxyApi;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUtil;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.webview.intercept.WebRequest;
import com.wepie.webview.intercept.WebResponse;
import com.wepie.webview.intercept.WebViewCacheInterceptorInst;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by bigwen on 2019-12-18.
 */
public class WPWebView extends FrameLayout {

    private static final String BRIDGE_NAME = "AndroidJsBridge";
    private static final String TAG = "WPWebView";

    public BridgeOriginWebView originWebView;
    private WebSettings settings;
    private boolean clearHistory = false;
    private long initTime = 0L;
    private boolean isOnPageFinishedCalled = false;

    private final CheckTask checkTask = new CheckTask();

    public WPWebView(@NonNull Context context) {
        super(context);
    }

    public WPWebView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public void init(boolean useX5) {
        removeAllViews();
        initTime = System.currentTimeMillis();
        try {
            originWebView = new BridgeOriginWebView(getContext().getApplicationContext());
            WebView.setWebContentsDebuggingEnabled(LibBaseUtil.buildDebug());
            addView(originWebView);
            checkTask.init();
            WPWebViewHelper.init(originWebView);
        } catch (Exception e) {
            originWebView = null;
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (originWebView != null && originWebView.getParent() == null) {
            addView(originWebView);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (originWebView != null) {
            removeView(originWebView);
        }
        checkTask.check();
    }

    public void hideWebBackground() {
        if (originWebView != null) {
            setBackgroundColor(Color.TRANSPARENT);
            originWebView.setBackgroundColor(Color.TRANSPARENT);
        }
    }

    public void stopLoading() {
        if (originWebView != null) {
            originWebView.stopLoading();
        }
    }

    public void destroy() {
        if (originWebView != null) {
            originWebView.destroy();
            checkTask.onCall(0b1111110);
        }
    }

    public boolean canGoBack() {
        if (originWebView != null) {
            return originWebView.canGoBack();
        }
        return false;
    }

    public void goBack() {
        if (originWebView != null) {
            originWebView.goBack();
        }
    }

    public String getUrl() {
        if (originWebView != null) {
            return originWebView.getUrl();
        }
        return "";
    }

    public String getTitle() {
        if (originWebView != null) {
            return originWebView.getTitle();
        }
        return "";
    }

    public WebSettings getSettings() {
        if (settings != null) {
            return settings;
        }
        if (originWebView != null) {
            settings = originWebView.getSettings();
        }
        return settings;
    }

    public void setDatabaseEnabled(boolean b) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setDatabaseEnabled(b);
        }
    }

    public void setDatabasePath(String dir) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setDatabasePath(dir);
        }
    }

    public void setDomStorageEnabled(boolean b) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setDomStorageEnabled(b);
        }
    }

    public void setCacheMode(int cacheMode) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setCacheMode(cacheMode);
        }
    }

    public void setJavaScriptEnabled(boolean b) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setJavaScriptEnabled(b);
        }
    }

    public void setAllowFileAccess(boolean b) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setAllowFileAccess(b);
        }
    }

    public void setMixedContentMode(int mixedContentAlwaysAllow) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            if (originWebView != null) {
                getSettings().setMixedContentMode(mixedContentAlwaysAllow);
            }
        }
    }

    public String getUserAgentString() {
        String ua = "";
        if (originWebView != null) {
            ua = getSettings().getUserAgentString();
        }
        return ua;
    }

    public String getUserAgentString(Context context) {
        String ua = "";
        try {
            ua = WebSettings.getDefaultUserAgent(context);
        } catch (Exception e) {
            FLog.e(new Throwable(e));
        }
        return ua;
    }

    public void setUserAgent(String s) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setUserAgentString(s);
        }
    }

    public void loadUrl(String url, HashMap<String, String> extraHeaders, boolean clearHistory) {
        this.clearHistory = true;
        if (originWebView != null) {
            if (isNeedCheck()) {
                if (checkDomain(url)) {
                    originWebView.loadUrl(url, extraHeaders);
                } else {
                    String errorUrl = interceptUrl(ConfigHelper.getInstance().getCallbackUrlsFullScreen());
                    originWebView.loadUrl(errorUrl);
                    HLog.d(TAG, HLog.USR, "checkDomain error, url={}", url);
                }
            } else {
                originWebView.loadUrl(url, extraHeaders);
            }
        }
    }

    public void loadUrl(String url, boolean clearHistory) {
        this.clearHistory = true;
        if (originWebView != null) {
            originWebView.loadUrl(url);
        }
    }

    /**
     * @param oriUrl
     * @return url
     */
    private String interceptUrl(String oriUrl) {
        WebApi.UrlInterceptor interceptor = ApiService.of(WebApi.class).getUriInterceptor();
        String url = oriUrl;
        if (interceptor != null) {
            url = interceptor.intercept(oriUrl);
        }
        return url;
    }

    private boolean isNeedCheck() {
        boolean domainBlock = PrefUtil.getInstance().getBoolean(PrefUtil.DOMAIN_BLOCK, true);
        return !LibBaseUtil.buildDebug() || domainBlock;
    }

    /**
     * 判断url是否在白名单内
     *
     * @param inputUrl url
     * @return boolean 是否在白名单内
     */
    private boolean checkDomain(String inputUrl) {
        if (!inputUrl.startsWith("http://") && !inputUrl.startsWith("https://")) {
            return false;
        }
        List<String> validUrlList = new ArrayList<>(ConfigHelper.getInstance().getWhiteHosts());
        try {
            Uri url = Uri.parse(inputUrl);
            String inputDomain = url.getHost();
            if (TextUtils.isEmpty(inputDomain)) {
                HLog.d(TAG, HLog.USR, "checkDomain, getHost empty, url={}", inputUrl);
                return false;
            }
            for (String whiteDomain : validUrlList) {
                if (inputDomain.endsWith("." + whiteDomain) || inputDomain.equals(whiteDomain))
                    return true;
            }
        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "checkDomain Exception, url={}, e={}", inputUrl, e);
            return false;
        }
        return false;
    }

    public void setWebChromeClient(final WPWebChromeClient webChromeClient) {
        if (originWebView != null) {
            originWebView.setWebChromeClient(new WebChromeClient() {
                @Override
                public void onReceivedTitle(WebView view, String title) {
                    super.onReceivedTitle(view, title);
                    if (webChromeClient != null)
                        webChromeClient.onReceivedTitle(WPWebView.this, title);
                }

                //For Android  >= 4.1
                public void openFileChooser(android.webkit.ValueCallback<Uri> valueCallback, String acceptType, String capture) {
                    if (webChromeClient != null)
                        webChromeClient.openFileChooser(valueCallback, acceptType, capture);
                }

                // For Android >= 5.0
                @Override
                public boolean onShowFileChooser(WebView webView, android.webkit.ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
                    if (webChromeClient != null) {

                        return webChromeClient.onShowFileChooser(WPWebView.this, filePathCallback, fileChooserParams);
                    }
                    return super.onShowFileChooser(webView, filePathCallback, fileChooserParams);
                }
            });
        }
    }

    public void setWebViewClient(final WPWebViewClient client, final DataCallback<String> callback) {
        if (originWebView != null) {
            originWebView.setWebViewClient(new WebViewClient() {
                @Override
                public boolean shouldOverrideUrlLoading(WebView view, String url) {
                    if (client != null) {
                        return client.shouldOverrideUrlLoading(WPWebView.this, url);
                    }
                    return false;
                }

                @Override
                public void onLoadResource(WebView view, String url) {
                    if (client != null) {
                        client.onLoadResource(WPWebView.this, url);
                    }
                    super.onLoadResource(view, url);
                    if (callback != null) {
                        callback.onCall(url);
                    }
                }

                @Override
                public void onPageStarted(WebView view, String url, Bitmap favicon) {
                    super.onPageStarted(view, url, favicon);
                    if (client != null) {
                        client.onPageStarted(WPWebView.this, url, favicon);
                    }
                }

                @Override
                public void onPageFinished(WebView view, String url) {
                    if (clearHistory) {
                        clearHistory = false;
                        originWebView.clearHistory();
                    }
                    if (client != null) {
                        client.onPageFinished(WPWebView.this, url);
                    }
                    super.onPageFinished(view, url);
                    if (!isOnPageFinishedCalled) {
                        logWebviewTime(url);
                        isOnPageFinishedCalled = true;
                    }
                }

                @Nullable
                @Override
                public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                    if (!"GET".equals(request.getMethod())) {
                        return null;
                    }
                    IWebProxyApi webProxyApi = ApiService.of(IWebProxyApi.class);
                    if (webProxyApi != null && webProxyApi.shouldProxy(request.getUrl()) == true) {
                        WebRequest webRequest = new WebRequest(request);
                        WebResponse response = WebViewCacheInterceptorInst.INSTANCE.interceptRequest(webRequest);
                        if (response != null) {
                            return response.getResponse();
                        }
                    }
                    return null;
                }
            });
        }

    }

    private void logWebviewTime(String url) {
        if (originWebView == null) {
            return;
        }
        String script = "(function(){return JSON.stringify(window.performance.timing);})();";
        originWebView.evaluateJavascript(script, value -> {
            // 这里回调的value其实就是window.performance.timing这个对象对应的json字符串
            value = value.replace("\"", "");
            value = value.replace("\\", "");
            PerformanceTiming timing = JsonUtil.parseJson(value, PerformanceTiming.class);
            if (timing != null) {
                Map<String, String> map = new HashMap<>();
                map.put("name", "webview");
                map.put("url", url);
                map.put("webview_init", String.valueOf(timing.navigationStart - initTime));                 // WebView初始化
                map.put("dns_lookup", String.valueOf(timing.domainLookupEnd - timing.domainLookupStart));   // dns查询
                map.put("tcp_connect", String.valueOf(timing.connectEnd - timing.connectStart));            // tcp连接
                map.put("request_res", String.valueOf(timing.responseEnd - timing.requestStart));           // 资源请求
                map.put("build_dom", String.valueOf(timing.domContentLoadedEventEnd - timing.responseEnd)); // 脚本解析、执行，DOM构建
                map.put("duration", String.valueOf(timing.domContentLoadedEventEnd - initTime));            // 首屏时长，从WebView开始初始化到DOM构建完成
                IWebProxyApi webProxyApi = ApiService.of(IWebProxyApi.class);
                if (webProxyApi != null) {
                    Boolean proxy = webProxyApi.shouldProxy(Uri.parse(url));
                    map.put("proxy_h5_request", String.valueOf(proxy));
                    if (proxy == Boolean.TRUE) {
                        ToastUtil.debugShow("客户端代理WebView请求");
                    }
                }
                HLog.d(TAG, HLog.USR, "WebView cost: {}", map);
                HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "", map);
            }
        });
    }

    public void setDownloadListener(final WPDownloadListener downloadListener) {
        if (originWebView != null) {
            originWebView.setDownloadListener(new DownloadListener() {
                @Override
                public void onDownloadStart(String url, String userAgent, String contentDisposition, String mimetype, long contentLength) {
                    if (downloadListener != null)
                        downloadListener.onDownloadStart(url, userAgent, contentDisposition, mimetype, contentLength);
                }
            });
        }
    }

    @SuppressLint("JavascriptInterface")
    public void addJavascriptInterface(Object object, String name) {
        if (originWebView != null) {
            originWebView.addJavascriptInterface(object, name);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public void evaluateJavascript(String script, ValueCallback<String> stringValueCallback) {
        if (originWebView != null) {
            originWebView.evaluateJavascript(script, stringValueCallback);
        }
    }


    public void loadUrl(String js) {
        if (originWebView != null) {
            originWebView.loadUrl(js);
        }
    }

    public void registerHandler(String name, BridgeHandler bridgeHandler) {
        if (originWebView != null) {
            originWebView.registerHandler(name, bridgeHandler);
        }
    }

    public void callHandler(String handlerName, String data, CallBackFunction callBack) {
        if (originWebView != null) {
            originWebView.callHandler(handlerName, data, callBack);
        }
    }

    public void addJsBridge() {
        if (originWebView != null) {
            originWebView.registerAndroidJs(BRIDGE_NAME, BridgeUtil.ANDROID_JS);
        }
    }

    public void clearMessage() {
        if (originWebView != null) {
            originWebView.clearMessage();
            checkTask.onCall(0b11111101);
        }
    }

    public void enableSlowWholeDocumentDraw() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                WebView.enableSlowWholeDocumentDraw();
            }
        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "enableSlowWholeDocumentDraw e = {}", e);
            FLog.e(e);
        }
    }

    public void removeJavascriptInterface(String... interfaceList) {
        try {
            if (originWebView != null) {
                for (String s : interfaceList) {
                    originWebView.removeJavascriptInterface(s);
                }
            }
        } catch (Exception e) {
            //ignore
        }
    }

    public void setSupportMultipleWindows(boolean support) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setSupportMultipleWindows(support);
        }
    }

    public void setJavaScriptCanOpenWindowsAutomatically(boolean flag) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setJavaScriptCanOpenWindowsAutomatically(flag);
        }
    }

    public void setBuiltInZoomControls(boolean enabled) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setBuiltInZoomControls(enabled);
        }
    }

    public void setSupportZoom(boolean support) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setSupportZoom(support);
        }
    }

    public void setDisplayZoomControls(boolean enabled) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setDisplayZoomControls(enabled);
        }
    }

    public void setTextZoom(int textZoom) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setTextZoom(textZoom);
        }
    }

    public void setSaveFormData(boolean save) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setSaveFormData(save);
        }
    }

    public void setUseWideViewPort(boolean use) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setUseWideViewPort(use);
        }
    }

    public void setLoadWithOverviewMode(boolean overview) {
        WebSettings settings = getSettings();
        if (settings != null) {
            settings.setLoadWithOverviewMode(overview);
        }
    }

    public void setAllowUniversalAccessFromFileURLs(boolean flag) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            if (originWebView != null) {
                getSettings().setAllowUniversalAccessFromFileURLs(flag);
            }
        }
    }

    public void setAcceptThirdPartyCookies(boolean accept) {
        if (originWebView != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                CookieManager.getInstance().setAcceptThirdPartyCookies(originWebView, accept);
            }
        }
    }

    public void sendMessage(Message msg) {
        if (originWebView != null) {
            originWebView.queueMessage(msg);
        }
    }

    private static class CheckTask implements Runnable {

        private int checkFlags = 0;
        private Throwable initCause = null;

        public CheckTask() {
        }

        public void init() {
            if (LibBaseUtil.buildDebug()) {
                checkFlags = 0b11;
                initCause = new Throwable();
            }
        }

        @Override
        public void run() {
            if (checkFlags != 0) {
                FLog.e(new IllegalStateException("WebView use error, flag:0b" + Integer.toBinaryString(checkFlags), initCause));
            }
        }

        public void check() {
            if (checkFlags != 0 && LibBaseUtil.buildDebug()) {
                ThreadUtil.runOnUiThreadDelay(200, this);
            }
        }

        public void onCall(int flags) {
            checkFlags = checkFlags & flags;
        }
    }
}
