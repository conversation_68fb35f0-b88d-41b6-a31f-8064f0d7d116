package com.huiwan.libtcp.base

import android.os.SystemClock
import com.huiwan.base.common.GlobalConfig.ConfigItem
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ApplicationUtil
import com.huiwan.base.util.NetWorkUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.libtcp.R
import com.huiwan.libtcp.TcpMainUtil
import com.huiwan.libtcp.huiwan.ConnectorStatUtil
import com.welib.alinetlog.AliNetLogUtil
import com.wepie.liblog.main.FLog
import com.wepie.liblog.main.HLog
import org.json.JSONObject
import java.io.IOException
import java.net.ConnectException
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.NoRouteToHostException
import java.nio.channels.SocketChannel
import java.nio.channels.UnresolvedAddressException

/**
 * tcp网络底层请求类
 *
 * <AUTHOR>
 */
abstract class HWTCPSocketThread(tag: String, configItem: ConfigItem) :
    BaseDisposer<TCPChannel>(tag, configItem) {
    private var hasReconnectCounts = 0
    private var lastReconnectTimes = 0L

    @Volatile
    private var isRunning = true

    override fun start() {
        TCPThreadPool.get().execute {
            HLog.d(TAG, HLog.USR, "run!")
            val start = SystemClock.elapsedRealtime()
            try {
                socketConnect()
                ConnectorStatUtil.logConnTime(SystemClock.elapsedRealtime() - start, true)
            } catch (e: Exception) {
                ConnectorStatUtil.logConnTime(SystemClock.elapsedRealtime() - start, false)
                onConnectFail(configItem.host, configItem.port, e)
            }
            doNextSteps()
        }
    }

    // handle socket steps after socket connected
    private fun doNextSteps() {
        if (isConnected()) {
            onConnected(false)
        } else {
            socketReconnect(false, configItem)
        }
    }

    @Throws(Exception::class)
    private fun socketConnect() {
        updateConnectStatus(SOCKET_CONNECTING)
        onConnecting()
        HLog.d(TAG, HLog.USR, Thread.currentThread().name + ", socketConnect! " + getHostMsg())
        try {
            val channel = SocketChannel.open(
                InetSocketAddress(
                    configItem.ip ?: configItem.host,
                    configItem.port
                )
            )
            val isConnected: Boolean = try {
                channel.isConnected
            } catch (e: Exception) {
                false
            }
            if (isConnected) {
                val tcpChannel = TCPChannel(channel)
                selector.register(tcpChannel)
                tcpChannel.callback = this
                this.channel = tcpChannel
            }
        } catch (exception: Exception) {
            if (exception is UnresolvedAddressException || exception is ConnectException || exception is NoRouteToHostException) {
                if (ApplicationUtil.isProcessForeground()) {
                    HLog.aliLog(AliNetLogUtil.PORT.tcp, "socketConnect", AliNetLogUtil.TYPE.err,
                        "isNetwork:${NetWorkUtil.isNetworkConnected()}"
                                + " connectType:${NetWorkUtil.getNetWorkConnectionType()}" + exception)
                }
            } else {
                FLog.e(exception)
            }
        }

        HLog.d(TAG, HLog.USR, "socketConnect  success! ${getHostMsg()}")
        updateConnectStatus(SOCKET_CONNECTED)
    }

    protected fun updateConnectStatus(status: Int) {
        socketStatus = status
        if (isConnected()) {
            hasReconnectCounts = 0
            lastReconnectTimes = 0L
        }
    }

    private fun isConnecting(): Boolean {
        return socketStatus == SOCKET_CONNECTING
    }

    private fun threadSleep() {
        try {
            Thread.sleep((1000 * 1).toLong())
        } catch (e2: InterruptedException) {
            Thread.interrupted()
        }
    }

    // handle socket ioException
    override fun handleSocketIOException(e: Exception) {
        handleThrowable(e)
    }

    override fun handleError(e: Error) {
        handleThrowable(e)
    }

    private fun handleThrowable(throwable: Throwable) {
        if (isConnected()) {
            socketDisconnect()
        }
        updateConnectStatus(SOCKET_UN_CONNECTED)
        onDisconnected()
        socketReconnect(false, configItem)
    }

    private fun canReconnect(forceReconnect: Boolean, preConfig: ConfigItem?): Boolean {
        var flag = false
        val nowTime = SystemClock.elapsedRealtime()
        do {
            if (isConnecting() || !isRunning) {
                break
            }
            if (forceReconnect && null != preConfig && preConfig == configItem) {
                flag = true
                break
            }
            if (isConnected()) {
                break
            }
            if (hasReconnectCounts <= MAX_RECONNECT_TIMES) {
                flag = true
                break
            }
            if (nowTime - lastReconnectTimes >= RECONNECT_INTERVAL) {
                flag = true
            }
        } while (false)
        HLog.d(TAG, Thread.currentThread().name + ", canReconnect， flag=" + flag + ", forceReconnect=" + forceReconnect
                + ", lastReconnectTimes=" + lastReconnectTimes + " hasReconnectTimes=" + hasReconnectCounts)
        return flag
    }

    fun heartBeatSocketReconnect(forceReconnect: Boolean, preConfig: ConfigItem?) {
        TCPThreadPool.get().execute {
            socketReconnect(forceReconnect, preConfig)
        }
    }

    @Synchronized
    fun socketReconnect(forceReconnect: Boolean, preConfig: ConfigItem?) {
        if (!canReconnect(forceReconnect, preConfig)) {
            return
        }
        updateConnectStatus(SOCKET_CONNECTING)
        while (isRunning) {
            threadSleep()
            val start = SystemClock.elapsedRealtime()
            try {
                lastReconnectTimes = SystemClock.elapsedRealtime()
                hasReconnectCounts++
                HLog.d(TAG, HLog.USR, "socketReconnect! ${getHostMsg()}")
                socketDisconnect()
                updateConfigIfNeed(forceReconnect)
                if (hasReconnectCounts > MAX_RECONNECT_CONFIG_COUNTS && tag.contains("CONN")) {
                    onChangeKcp("CONN connect fail, change kcp", true)
                    isRunning = false
                    break
                }
                socketConnect()
                ConnectorStatUtil.logConnTime(SystemClock.elapsedRealtime() - start, true)
                HLog.d(TAG, HLog.USR, "socketReconnect! success ${getHostMsg()}")
                break
            } catch (e: Exception) {
                ConnectorStatUtil.logConnTime(SystemClock.elapsedRealtime() - start, false)
                if (preConfig != null) {
                    onConnectFail(preConfig.host, preConfig.port, e)
                } else {
                    if (configItem != null) {
                        onConnectFail(configItem.host, configItem.port, e)
                    } else {
                        onConnectFail("", 0, e)
                        HLog.d(TAG, HLog.USR, "socketReconnect! error, config is null.")
                    }
                }
            }
        }
        if (isConnected() && isRunning) {
            onConnected(true)
        }
    }

    @Throws(IOException::class)
    private fun socketDisconnect() {
        updateConnectStatus(SOCKET_UN_CONNECTED)
        HLog.d(TAG, HLog.USR, "socketDisconnect! ${getHostMsg()}")
        channel?.let {
            selector.cancel(it)
        }
        channel = null
        HLog.d(TAG, HLog.USR, "socketDisconnect success!")
    }

    override fun disconnect() {
        isRunning = false
        TCPThreadPool.get().execute {
            try {
                socketDisconnect()
            } catch (e: IOException) {
                TcpMainUtil.error("$TAG socketDisconnect fail!  ${getHostMsg()}    $e")
            }
        }
    }

    override fun socketStatusMsg(status: JSONObject) {
        val obj = JSONObject()
        try {
            val socket = this.channel?.getSocket()
            obj.put("socket ", socket)
            obj.put("host", getHostMsg())
            obj.put("socketStatus", socketStatus)
            val address: InetAddress? = socket?.inetAddress
            if (null != address) {
                try {
                    obj.put("reachable", address.isReachable(10 * 1000))
                } catch (e: Exception) {
                    obj.put("reachable error", e.toString())
                }
            }
        } catch (e: Exception) {
            //ignore
        }
        status.put("status", obj)
    }

    private fun updateConfigIfNeed(forceReconnect: Boolean) {
        // 心跳连续失败forceReconnect=true，或者连续连接失败hasReconnectCounts超过阈值时，更新备用链路
        val flag = hasReconnectCounts > MAX_RECONNECT_CONFIG_COUNTS || forceReconnect
        if (flag) {
            configItem = configItem.backUpConfig
            TAG = "$tag(${configItem.host}, ${configItem.port})"
        }
        HLog.d(TAG, HLog.USR, "updateConfigIfNeed! hasReconnectTimes=$hasReconnectCounts, forceReconnect=$forceReconnect, configItem=$configItem")
    }

    interface WriteCallback : ISocketDisposer.WriteCallback

    class SimpleWriteCallback : WriteCallback {
        override fun onWriteSuccess() = Unit
        override fun onWriteFailed() {
            ToastUtil.show(ResUtil.getString(R.string.room_tcp_write_fail_msg))
        }
    }

    abstract fun onConnected(reConnected: Boolean)
    abstract fun onConnecting()
    abstract fun onConnectFail(host: String?, port: Int, e: Exception?)

    companion object {

        private val selector: TCPSelector by lazy {
            TCPSelector.get(TCPThreadPool.get().service)
        }
    }

}