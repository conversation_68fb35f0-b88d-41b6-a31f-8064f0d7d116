package com.huiwan.libtcp.base

import android.os.SystemClock
import android.util.Log
import com.huiwan.base.common.GlobalConfig.ConfigItem
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.libtcp.TcpMainUtil
import com.huiwan.libtcp.config.TCPConfig.PACKET_HEAD_LEN
import com.welib.alinetlog.AliNetLogUtil
import com.wepie.liblog.main.HLog
import java.io.IOException
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

abstract class BaseDisposer<T : Channel>(
    val tag: String,
    var configItem: ConfigItem
) : IStateCallback<T>, ISocketDisposer {

    var TAG: String = "$tag(${configItem.ip ?: configItem.host}, ${configItem.port})"

    @Volatile
    private var socketStatus = SOCKET_CONNECTING

    @Volatile
    private var readRunnable: ChannelReadRunnable<T>? = null

    @Volatile
    private var writeRunnable: ChannelWriteRunnable<T>? = null

    protected var channel: T? = null

    //upload log interval time
    private var lastUploadTime = 0L

    protected fun getHostMsg(): String = "${configItem.host}:${configItem.port}"

    override fun start() = Unit

    override fun getSocketStatus(): Int = socketStatus

    protected fun setSocketStatus(status: Int) {
        this.socketStatus = status
    }

    override fun write(
        encoder: ISocketDataEncoder,
        writeCallback: ISocketDisposer.WriteCallback?
    ) {
        if (encoder.isCancelled()) {
            HLog.d(TAG, "write cancelled when offer")
            return
        }
        if (!isConnected()) {
            writeCallback?.onWriteFailed()
            return
        }
        val runnable = getWriteRunnable()

        if (runnable == null) {
            writeCallback?.onWriteFailed()
            return
        }
        runnable.write(channel, WriteData(encoder, writeCallback))
    }

    override fun onChannelRead(channel: T) {
        if (channel.isConnected() && readRunnable == null) {
            synchronized(ChannelReadRunnable::class.java) {
                if (readRunnable == null) {
                    readRunnable = ChannelReadRunnable(this)
                }
            }
        }

        readRunnable?.notifyRun()
    }

    override fun onChannelDisConnect() = Unit

    override fun onDisconnected() {
        readRunnable = null
        writeRunnable = null
    }

    open fun isConnected(): Boolean {
        return channel?.isConnected() ?: false
    }

    // handle socket ioException
    protected open fun handleSocketIOException(e: Exception) {
    }

    protected open fun handleError(e: Error) {

    }

    private fun invokeWriteCallback(cb: ISocketDisposer.WriteCallback?, success: Boolean) {
        if (cb == null) return
        TcpMainUtil.runMain {
            if (success) {
                cb.onWriteSuccess()
            } else {
                cb.onWriteFailed()
            }
        }
    }

    protected fun getWriteRunnable(): ChannelWriteRunnable<T>? {
        if (writeRunnable == null) {
            synchronized(ChannelWriteRunnable::class.java) {
                if (writeRunnable == null) {
                    writeRunnable = ChannelWriteRunnable(this)
                }
            }
        }
        return writeRunnable
    }

    // upload error msg
    private fun uploadErrorMsg(port: AliNetLogUtil.PORT, msg: String) {
        TcpMainUtil.error(msg)
        val nowTime = SystemClock.elapsedRealtime()
        if (nowTime - lastUploadTime >= UPLOAD_INTERVAL) {
            TcpMainUtil.upload(port, AliNetLogUtil.TYPE.err, msg)
            lastUploadTime = nowTime
        }
    }

    private fun formatErrorMsg(tag: String, e: Throwable): String {
        return "$TAG\t$tag host:${configItem.host} port:${configItem.port}\t ${e.stackTraceToString()}"
    }

    abstract class AbsChannelRunnable<T : Channel>(val thread: BaseDisposer<T>) : Runnable {
        val isRunning = AtomicBoolean(false)

        override fun run() {
            try {
                val channel = thread.channel ?: return
                if (!channel.isConnected()) {
                    return
                }
                doRun(channel)
            } catch (e: IOException) {
                onException(e)
            } catch (err: Error) {
                onError(err)
            } finally {
                isRunning.set(false)
            }
        }

        fun notifyRun() {
            if (isRunning.get()) {
                return
            }
            isRunning.compareAndSet(false, true)
            TCPThreadPool.get().execute(this)
        }

        protected abstract fun doRun(channel: T)

        protected open fun onException(e: Exception) {
            thread.handleSocketIOException(e)
        }

        protected open fun onError(e: Error) {
            thread.handleError(e)
        }
    }


    private class ChannelReadRunnable<T : Channel>(thread: BaseDisposer<T>) :
        AbsChannelRunnable<T>(thread) {
        private val headBuff = ByteBuffer.allocate(PACKET_HEAD_LEN)
        private var contentBuffer = ByteBuffer.allocate(1024)

        override fun doRun(channel: T) {
            do {
                var buff = headBuff
                headBuff.clear()
                var size = channel.read(buff)
                if (size <= 0) {
                    return
                }
                buff.order(ByteOrder.LITTLE_ENDIAN)
                buff.flip()
                val bodyLen = buff.int
                if (bodyLen < 0 || bodyLen > 10 * 1024 * 1024) {
                    throw IOException("get head error bodyLen:$bodyLen")
                }
                if (contentBuffer.capacity() < bodyLen) {
                    contentBuffer = ByteBuffer.allocate(((bodyLen - 1) / 1024 + 1) * 1024)
                } else {
                    contentBuffer.clear()
                }
                buff = contentBuffer
                buff.limit(bodyLen)
                size = channel.read(buff)
                buff.flip()
                if (size != bodyLen) {
                    throw IOException("get head error bodyLen:$bodyLen")
                }
                thread.onPacket(contentBuffer)
            } while (channel.hasNext())
        }

        override fun onException(e: Exception) {
            super.onException(e)
            thread.uploadErrorMsg(
                AliNetLogUtil.PORT.tcpReadError,
                thread.formatErrorMsg("readSocket", e)
            )
        }

        override fun onError(e: Error) {
            super.onError(e)
            thread.uploadErrorMsg(
                AliNetLogUtil.PORT.tcpReadError,
                thread.formatErrorMsg("readSocket", e)
            )
        }
    }

    class ChannelWriteRunnable<T : Channel>(thread: BaseDisposer<T>) :
        AbsChannelRunnable<T>(thread) {
        private val queue: LinkedBlockingQueue<WriteData> =
            LinkedBlockingQueue()

        private val MAX_BUFF_SIZE = Int.MAX_VALUE - 8

        private val bufferLocal: ThreadLocal<ByteBuffer> = ThreadLocal()

        override fun doRun(channel: T) {
            var data: WriteData? = null
            try {
                data = queue.poll(200, TimeUnit.MILLISECONDS)
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
            while (data != null) {
                onWrite(data, channel)
                data = queue.poll()
            }
        }

        override fun onException(e: Exception) {
            super.onException(e)
            thread.uploadErrorMsg(
                AliNetLogUtil.PORT.tcpReadError,
                thread.formatErrorMsg("writeSocket", e)
            )
        }

        override fun onError(e: Error) {
            super.onError(e)
            thread.uploadErrorMsg(
                AliNetLogUtil.PORT.tcpReadError,
                thread.formatErrorMsg("writeSocket", e)
            )
        }

        private fun onWrite(d: WriteData, channel: T) {
            try {
                if (d.encoder.isCancelled()) {
                    HLog.d(thread.TAG, "write cancelled when poll")
                    return
                }
                val size = d.encoder.size()
                var buffer = bufferLocal.get() ?: ByteBuffer.allocate(size)
                    .also { bufferLocal.set(it) }
                if (size > buffer.capacity()) {
                    buffer = growBuff(buffer.capacity(), size)
                }
                buffer.clear()
                buffer.order(ByteOrder.LITTLE_ENDIAN)
                val ret = d.encoder.writeTo(buffer)
                buffer.flip()
                if (ret) {
                    thread.invokeWriteCallback(d.cb, channel.write(buffer))
                } else {
                    thread.invokeWriteCallback(d.cb, false)
                }
            } catch (e: Exception) {
                thread.invokeWriteCallback(d.cb, false)
                throw e
            }
        }

        fun write(channel: T?, data: WriteData) {
            if (channel?.isConnected() != true) {
                data.cb?.onWriteFailed()
                return
            }
            queue.offer(data)
            notifyRun()
        }

        private fun growBuff(oldCapacity: Int, minCapacity: Int): ByteBuffer {
            var newCapacity = oldCapacity + (oldCapacity shr 1)
            if (newCapacity - minCapacity < 0) {
                newCapacity = minCapacity
            }
            if (newCapacity - MAX_BUFF_SIZE > 0) {
                newCapacity = if (minCapacity > MAX_BUFF_SIZE) Int.MAX_VALUE else MAX_BUFF_SIZE
            }
            return ByteBuffer.allocate(newCapacity).also { bufferLocal.set(it) }
        }
    }

    data class WriteData(
        val encoder: ISocketDataEncoder,
        val cb: ISocketDisposer.WriteCallback?
    )

    companion object {
        const val SOCKET_CONNECTING = 1
        const val SOCKET_CONNECTED = 2
        const val SOCKET_UN_CONNECTED = 3

        //avoid frequent reconnect
        const val MAX_RECONNECT_TIMES = 5

        // one config can reconnect times threshold
        val MAX_RECONNECT_CONFIG_COUNTS =
            if (ApiService.of(HwApi::class.java).backupAddrCount > 0) {
                ApiService.of(HwApi::class.java).backupAddrCount
            } else {
                2
            }
        const val RECONNECT_INTERVAL = 2 * 1000
        private const val UPLOAD_INTERVAL = 1 * 1000L
    }
}