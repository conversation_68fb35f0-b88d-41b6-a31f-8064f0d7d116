package com.huiwan.libtcp;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.BuildConfig;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.activity.ActivityTask;
import com.huiwan.base.activity.ActivityTaskBuilderHolder;
import com.huiwan.base.common.GlobalConfig;
import com.huiwan.base.str.ResUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.lib.api.plugins.IDebugPluginApi;
import com.huiwan.libtcp.base.ByteArrayEncoder;
import com.huiwan.libtcp.base.HWTCPSocketThread;
import com.huiwan.libtcp.base.ISocketDisposer;
import com.huiwan.libtcp.base.ProtoDataEncoder;
import com.huiwan.libtcp.base.SimpleProtoDataEncoder;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.depend.ServerConfigCallback;
import com.huiwan.libtcp.depend.TcpAddressInterface;
import com.huiwan.libtcp.huiwan.ConnectorStatUtil;
import com.huiwan.libtcp.huiwan.HuiwanSeqCallbackManager;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.libtcp.model.ServerConfig;
import com.huiwan.libtcp.sender.HeartBeatController;
import com.huiwan.libtcp.thread.ChatSocketThread;
import com.huiwan.libtcp.thread.HeartbeatThread;
import com.huiwan.libtcp.thread.VoiceRoomSocketThread;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.cancellable.Cancellable;
import com.wejoy.weplay.ex.cancellable.LifeCancellable;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.net.tcp.packet.HeadPackets;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class TcpConnect {
    private static final String TAG = "TcpConnect";
    private static boolean isAppRestored = false;

    private static volatile TcpConnect instance;
    private final HeartbeatThread heartbeatThread;

    private HWTCPSocketThread chatSocketThread;
    private VoiceRoomSocketThread voiceRoomSocketThread;

    private HeartBeatController chatController;
    private HeartBeatController voiceRoomController;

    private boolean isScreenOff = false;
    private boolean isBackground = false;

    private TcpConnect() {
        heartbeatThread = new HeartbeatThread();
        heartbeatThread.start();

        Context context = LibBaseUtil.getApplication();
        if (context != null) {
            IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_SCREEN_OFF);
            filter.addAction(Intent.ACTION_USER_PRESENT);
            context.registerReceiver(new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    switch (intent.getAction()) {
                        case Intent.ACTION_SCREEN_OFF:
                            isScreenOff = true;
                            break;
                        case Intent.ACTION_USER_PRESENT:
                            isScreenOff = false;
                            break;
                    }
                }
            }, filter);
        }
        ActivityTaskManager.getInstance().registerActivityTaskListener(new ActivityTaskManager.ActivityTaskListener() {
            @Override
            public void onBackground(@NonNull Activity activity) {
                isBackground = true;
            }

            @Override
            public void onForeground(@NonNull Activity activity) {
                isBackground = false;
            }
        });
    }

    public static TcpConnect getInstance() {
        if (instance == null) {
            synchronized (TcpConnect.class) {
                if (null == instance) {
                    instance = new TcpConnect();
                }
            }
        }
        return instance;
    }

    public void connect() {
        HLog.d(TAG, HLog.USR, "TcpConnect connect start");
        TcpAddressInterface addressInterface = TcpMainUtil.getTcpAddressInterface();
        if (addressInterface == null) {
            HLog.d(TAG, HLog.USR, "tcpDependInterface is not init");
            return;
        }
        addressInterface.getChatServer(new DataCallback<GlobalConfig.ConfigItem>() {
            @Override
            public void onCall(GlobalConfig.ConfigItem data) {
                HLog.d(TAG, HLog.USR, "TcpConnect checkSysConfig onSuccess");
                connectChat(data);
            }

            @Override
            public void onFailed(int code, String msg) {
                HLog.d(TAG, HLog.USR, "TcpConnect checkSysConfig onFail code:{} msg:{}", code, msg);
            }
        });

        connectConnector(addressInterface);
    }

    public void connectConnector() {
        TcpAddressInterface addressInterface = TcpMainUtil.getTcpAddressInterface();
        if (addressInterface == null) {
            HLog.d(TAG, HLog.USR, "tcpDependInterface is not init");
            return;
        }
        connectConnector(addressInterface);
    }

    private void connectConnector(TcpAddressInterface addressInterface) {
        addressInterface.getVoiceRoomServer(new ServerConfigCallback() {
            @Override
            public void onSuccess(ServerConfig serverConfig) {
                HLog.d(TAG, HLog.USR, "TcpConnect getServiceConfig onSuccess");
                connectVoiceRoom(serverConfig);
            }

            @Override
            public void onFail(int code, String msg) {
                HLog.d(TAG, HLog.USR, "TcpConnect getServiceConfig onFail code={} msg={} ", code, msg);
            }
        });
    }

    private void connectChat(GlobalConfig.ConfigItem config) {
        if (config == null) return;

        if (chatSocketThread == null) {
            chatController = HeartBeatController.chat();
            heartbeatThread.addController(chatController);
            chatSocketThread = new ChatSocketThread(config);
            chatSocketThread.start();
            TcpMainUtil.print("TcpConnect start chatSocketThread");
        }

        HLog.d(TAG, HLog.USR, "connectChat!");
    }

    /**
     * 语音房 connector 长链接需要在登录后刷新相应配置后再进行连接。
     * 服务器对不同的用户分配了不同的 connector, 有一定内在逻辑。
     */
    private void connectVoiceRoom(ServerConfig serverConfig) {
        if (serverConfig == null) return;

        if (voiceRoomSocketThread == null) {
            voiceRoomController = HeartBeatController.connector();
            heartbeatThread.addController(voiceRoomController);
            voiceRoomSocketThread = new VoiceRoomSocketThread(serverConfig.connserver.address, serverConfig.connserver.useKcp);
            voiceRoomSocketThread.start();
            TcpMainUtil.print("TcpConnect start voiceRoomSocketThread");
        }
    }

    public void sendChatPacket(GeneratedMessageLite<?, ?> message, ISocketDisposer.WriteCallback cb) {
        sendPacket(chatSocketThread, message, cb);
    }

    public Cancellable sendVoiceRoomPacket(HeadPackets.ReqHead head, GeneratedMessageLite<?, ?> body,
                                           ISocketDisposer.WriteCallback cb, SeqCallback seqCallback) {
        return sendPacketLite(voiceRoomSocketThread, head, body, cb, seqCallback);
    }

    public void sendVoiceRoomPacket(byte[] data, ISocketDisposer.WriteCallback cb) {
        voiceRoomSocketThread.write(new ByteArrayEncoder(data), cb);
    }

    private void sendPacket(HWTCPSocketThread socketThread, GeneratedMessageLite<?, ?> message, ISocketDisposer.WriteCallback cb) {
        if (socketThread != null) {
//            byte[] data = HWTCPPacker.pack(message);
            String tag = message.getClass().getSimpleName();
            socketThread.write(new SimpleProtoDataEncoder(message, tag), cb);
        } else {
            if (cb != null) cb.onWriteFailed();
        }
    }

    private Cancellable sendPacketLite(ISocketDisposer socketThread, HeadPackets.ReqHead head,
                                       GeneratedMessageLite<?, ?> body, final ISocketDisposer.WriteCallback cb, final SeqCallback seqCallback) {
        LifeCancellable cancellable;
        if (socketThread != null) {
            int cmd = head.getCommand();
            int type = head.getType();
            int seq = head.getSeq();
            int protocolType = ConnectorStatUtil.PROTOCOL_TCP;
            if (socketThread instanceof VoiceRoomSocketThread && VoiceRoomSocketThread.isKcp()) {
                protocolType = ConnectorStatUtil.PROTOCOL_KCP;
            }
            if (BuildConfig.DEBUG) {
                boolean mock = ApiService.of(IDebugPluginApi.class).mockTcp(cmd, type, seqCallback);
                if (mock) {
                    return () -> {
                    };
                }
            }

            final ProtoDataEncoder protoDataEncoder = new ProtoDataEncoder(head, body, false);
            cancellable = new LifeCancellable() {
                @Override
                public void cancel() {
                    protoDataEncoder.setCancelled(true);
                    SeqCallback callback = HuiwanSeqCallbackManager.getInstance().getCallback(seq, cmd, type, RspHeadInfo.LOCAL_ERROR_CANCELLED);
                    if (callback != null) {
                        HLog.d(TAG, HLog.USR, "sendPacketLite cancelled");
                        Map<String, String> map = new HashMap<>();
                        map.put("name", "Cancel");
                        map.put("type", "tcp");
                        HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "", map);
                    }
                }
            };
            if (seqCallback instanceof LifeSeqCallback) {
                ILife life = ((LifeSeqCallback) seqCallback).getLife();
                if (life != null) {
                    cancellable.autoCancel(life);
                }
            } else {
                checkUsage(seqCallback);
            }
            if (seqCallback != null) {
                HuiwanSeqCallbackManager.getInstance().addCallback(seq, cmd, type, protocolType, cancellable, seqCallback);
            }
            socketThread.write(protoDataEncoder, new ISocketDisposer.WriteCallback() {
                @Override
                public void onWriteFailed() {
                    if (cb != null) cb.onWriteFailed();
                    SeqCallback callback = HuiwanSeqCallbackManager.getInstance().getCallback(seq, cmd, type, RspHeadInfo.LOCAL_ERROR_WRITE_FAILED);
                    if (callback != null) {
                        callback.onFail(new RspHeadInfo(RspHeadInfo.LOCAL_ERROR_WRITE_FAILED, ResUtil.getStr(R.string.net_error)));
                    }
                }

                @Override
                public void onWriteSuccess() {
                    if (cb != null) cb.onWriteSuccess();
                }
            });

            printMsg(protocolType, head, body);
        } else {
            if (cb != null) cb.onWriteFailed();
            if (seqCallback != null) {
                seqCallback.onFail(new RspHeadInfo(RspHeadInfo.LOCAL_ERROR_WRITE_FAILED, ResUtil.getStr(R.string.net_error)));
            }
            cancellable = new LifeCancellable() {
                @Override
                public void cancel() {

                }
            };
        }
        return cancellable;
    }

    private void printMsg(int protocolType, GeneratedMessageLite<?, ?> head, GeneratedMessageLite<?, ?> body) {
        boolean needPrint = LibBaseUtil.buildDebug();
        if (needPrint) {
            if (TcpMainUtil.filterLog(head)) {
                return;
            }
            String type = protocolType == ConnectorStatUtil.PROTOCOL_KCP ? "kcp" : "tcp";
            TcpMainUtil.print("send {} msg head:\n{}", type, new ProtoToString(head));
            TcpMainUtil.print("send {} msg body:\n{} ", type, new ProtoToString(body));
        }
    }

    public void disConnect() {
        if (chatSocketThread != null) {
            heartbeatThread.removeController(chatController);
            chatController = null;
            chatSocketThread.disconnect();
            chatSocketThread = null;
        }
        if (voiceRoomSocketThread != null) {
            heartbeatThread.removeController(voiceRoomController);
            voiceRoomController = null;
            voiceRoomSocketThread.disconnect();
            voiceRoomSocketThread = null;
        }
        HLog.d(TAG, HLog.USR, "TcpConnect disConnect!");
    }

    public boolean isConnect() {
        return chatSocketThread != null && voiceRoomSocketThread != null;
    }

    public int chatSocketThreadStatus() {
        if (chatSocketThread == null) return -100;
        return chatSocketThread.getSocketStatus();
    }

    public int voiceRoomSocketThreadStatus() {
        if (voiceRoomSocketThread == null) return -100;
        return voiceRoomSocketThread.getSocketStatus();
    }

    public static void registerActivityLifeCallback() {
        ActivityTaskBuilderHolder.get().register(activity -> new ActivityTask() {
            @Override
            public void onRestoreInstanceState(Activity activity, Bundle savedInstanceState) {
                TcpConnect connect = getInstance();
                // 在关闭权限等操作时，进程会重启，此时需要进行tcp的重连
                HLog.d(TAG, "activity restored isAppRestored: {}", isAppRestored);
                if (!isAppRestored && TcpMainUtil.getAppDataInterface().isLogin()) {
                    boolean isConnect = connect.isConnect();
                    HLog.d(TAG, "activity restored isConnect: {}", isConnect);
                    if (!isConnect) {
                        connect.connect();
                        HLog.d(TAG, "activity restored try connect");
                    }
                    isAppRestored = true;
                }
            }
        });
    }

    public String getTcpStatus() {
        JSONObject data = new JSONObject();
        getSocketStatus("chatSocketThread", chatSocketThread, data);
        getSocketStatus("voiceRoomSocketThread", voiceRoomSocketThread, data);
        return data.toString();
    }

    private void getSocketStatus(String name, ISocketDisposer thread, JSONObject data) {
        try {
            JSONObject status = new JSONObject();
            if (null == thread) {
                status.put("thread live", false);
            } else {
                status.put("thread live", true);
                thread.socketStatusMsg(status);
            }
            data.put(name, status);
        } catch (Exception e) {
            //ignore e
        }
    }

    public void notifyVoiceRoomSocketHeart(int index, boolean isSuccess) {
        if (voiceRoomSocketThread == null || isScreenOff || isBackground) { // 锁屏或退到后台之后KCP有概率无法发包
            return;
        }
        HLog.d(TAG, "notifyVoiceRoomSocketHeart, index={}, isSuccess={}", index, isSuccess);
        voiceRoomSocketThread.notifyVoiceRoomSocketHeart(index, isSuccess);
    }

    public int getVoiceRoomSocketIndex() {
        if (voiceRoomSocketThread == null) {
            return -1;
        }
        return voiceRoomSocketThread.getIndex();
    }

    public void setConnectorHeartBeatRate(int heartbeatFreq) {
        if (voiceRoomController != null) {
            voiceRoomController.setInterval(heartbeatFreq * 1000L);
        }
    }

    public void socketReconnect(int type, GlobalConfig.ConfigItem configItem) {
        if (type == HeartBeatHelper.CHAT) {
            if (null != chatSocketThread) {
                chatSocketThread.heartBeatSocketReconnect(true, configItem);
            }
        }
    }

    public GlobalConfig.ConfigItem getConfigItem(int type) {
        GlobalConfig.ConfigItem configItem = null;
        switch (type) {
            case HeartBeatHelper.CONNECTOR:
                if (null != voiceRoomSocketThread) {
                    configItem = voiceRoomSocketThread.getConfigItem();
                }
                break;
            case HeartBeatHelper.CHAT:
                if (null != chatSocketThread) {
                    configItem = chatSocketThread.getConfigItem();
                }
                break;
            default:
                break;
        }
        return configItem;
    }

    private void checkUsage(SeqCallback callback) {
        if (LibBaseUtil.buildDebug() && callback != null) {
            boolean hasLife = callback instanceof LifeSeqCallback;
            if (!hasLife) {
                FLog.e(new IllegalArgumentException("use LifeSeqCallback"));
            }
        }
    }
}
