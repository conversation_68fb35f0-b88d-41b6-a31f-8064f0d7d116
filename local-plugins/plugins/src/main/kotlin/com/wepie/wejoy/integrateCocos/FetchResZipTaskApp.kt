package com.wepie.wejoy.integrateCocos

import com.squareup.moshi.Types
import com.wepie.wejoy.util.httpClient
import com.wepie.wejoy.util.json
import com.wepie.wejoy.util.toHumanReadStr
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsChannel
import io.ktor.utils.io.jvm.javaio.copyTo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.gradle.api.DefaultTask
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.file.RegularFileProperty
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.InputFile
import org.gradle.api.tasks.Optional
import org.gradle.api.tasks.OutputDirectory
import org.gradle.api.tasks.OutputFile
import org.gradle.api.tasks.TaskAction
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.atomic.AtomicLong

//存储内置zip 版本信息的文件
internal val buildInAppInfoFileName = "build_in_app.json"

/**
 * 根据获取到的下载的游戏的url,下载响应的zip，并放到res目录下, 并保存到飞书
 */
abstract class FetchResZipTaskApp : DefaultTask() {
    @get:InputFile
    @get:Optional
    abstract val inputFile: RegularFileProperty

    /**
     * 是否会保存结果到飞书表格
     */
    @get:Input
    abstract val saveResultToLark: Property<Boolean>

    @get:Input
    abstract val appVersionName: Property<String>

    @get:Input
    abstract val appVersionCode: Property<Int>

    @get:Input
    abstract val usePad: Property<Boolean>

    @get:OutputDirectory
    abstract val outputDir: DirectoryProperty

    private var targetUrls: MutableMap<Int, GameResource>? = null

    @get:OutputDirectory
    abstract val assetPackOutputDir: DirectoryProperty

    @get:OutputFile
    abstract val outDownloadZipInfoFile: RegularFileProperty
    private val savePaths by lazy { DownloadZipSaveInfo() }
    private var matchResTotalSize = AtomicLong(0L)
    private var gameResTotalSize = AtomicLong(0L)

    @TaskAction
    fun taskAction() {
        if (!inputFile.isPresent) {
            log("input file not exist")
            return
        }
        log("input file:${inputFile.get().asFile.absolutePath}")
        runBlocking {
            targetUrls =
                json.adapter<MutableMap<Int, GameResource>>(
                    Types.newParameterizedType(
                        MutableMap::class.java,
                        Integer::class.java,
                        GameResource::class.java
                    )
                ).fromJson(inputFile.get().asFile.readText(Charsets.UTF_8))
            log("target urls:$targetUrls")
            outputDir.get().asFile.deleteRecursively()
            loadZips(outputDir.get().asFile.absolutePath)
            if (saveResultToLark.get()) {
                saveToLarkChart()
            }
        }
    }

    suspend fun saveToLarkChart() {
        val branchName = getGitBranchName()
        targetUrls?.forEach { (gameType, resInfo) ->
            updateOrAddEmbedRecord(
                CocosEmbedSaveInfo(
                    appVersionName = appVersionName.get(),
                    appVersionCode = appVersionCode.get(),
                    gameType = gameType,
                    cocoGameResVersion = resInfo.version,
                    cocoGameResUrl = resInfo.inGameResUrl,
                    manifest = resInfo.inGameResManifest,
                    operateType = if (resInfo.isManual) EmbedOpType.Manual.name else EmbedOpType.Auto.name,
                    matchResSize = matchResTotalSize.get().toHumanReadStr(),
                    gameResSize = gameResTotalSize.get().toHumanReadStr(),
                    branchName = branchName
                )
            )
        } ?: run {
            log("targetUrls is null no need to save to lark")
            return
        }
        log("save to lark success")
    }


    private suspend fun loadZips(basePath: String) = coroutineScope {
        launch {
            val targetVersionJson =
                basePath + File.separator + "raw" + File.separator + buildInAppInfoFileName
            writeVersionInfo(targetVersionJson)
        }

        targetUrls?.forEach { (gameType, resInfo) ->
            log("start load $gameType,resInfo:$resInfo")
            downloadInGameRes(resInfo, getBaseSavePath(basePath), gameType)
            downloadDefaultMatchPicRes(gameType, basePath, resInfo.list)
            resInfo.list?.onEach {
                downloadMatchPicRes(gameType, basePath, it)
            }
        }
        writeSavePathsToFile()
    }

    private fun writeSavePathsToFile() {
        println("IntegrateCocosResAppPlugin  FetchResZipTaskApp savePaths:$savePaths,save to ${outDownloadZipInfoFile.asFile.get().absolutePath}")
        outDownloadZipInfoFile.asFile.get().writeText(
            json.adapter(DownloadZipSaveInfo::class.java).toJson(savePaths), Charsets.UTF_8
        )
    }

    private fun getBaseSavePath(basicPath: String): String {
        return if (usePad.get()) {
            assetPackOutputDir.get().asFile.path
        } else {
            basicPath
        }
    }

    private fun CoroutineScope.downloadInGameRes(
        resInfo: GameResource,
        basePath: String,
        gameType: Int
    ) {
        val resBasePath = if (usePad.get()) {
            basePath
        } else {
            basePath + File.separator + "raw"
        }
        val inGameResPath = resBasePath + File.separator + getInGameResSaveName(gameType)
        val inGameManifestPath =
            resBasePath + File.separator + getInGameResManifestSaveName(gameType)
        savePaths.gameZips.add(inGameResPath)
        launch {
            gameResTotalSize.addAndGet(downloadFile(resInfo.inGameResUrl, inGameResPath))
        }
        launch {
            gameResTotalSize.addAndGet(downloadFile(resInfo.inGameResManifest, inGameManifestPath))
        }
    }

    private fun CoroutineScope.downloadMatchPicRes(
        gameType: Int,
        baseResPath: String,
        matchRes: MatchRes
    ) {
        val savePath = baseResPath + File.separator + genRawDirByLang(matchRes.lang) +
                File.separator + getMatchResFileSaveName(gameType)
        savePaths.matchPicZips.add(savePath)
        launch {
            matchResTotalSize.addAndGet(downloadFile(matchRes.matchResUrl, savePath))
        }
    }

    private fun CoroutineScope.downloadDefaultMatchPicRes(
        gameType: Int,
        baseResPath: String,
        matchResList: List<MatchRes>?
    ) {
        val targetRes = matchResList?.firstOrNull { it.lang == "en" }
        if (targetRes == null) {
            throw IllegalArgumentException("Lack of  default en resources")
        }
        val savePath = baseResPath + File.separator + "raw" +
                File.separator + getMatchResFileSaveName(gameType)
        savePaths.matchPicZips.add(savePath)
        launch {
            matchResTotalSize.addAndGet(downloadFile(targetRes.matchResUrl, savePath))
        }
    }

    private fun genRawDirByLang(language: String) = "raw-$language"

    private suspend fun writeVersionInfo(targetSavePath: String) {
        val outFile = File(targetSavePath).apply {
            if (!parentFile.exists()) {
                parentFile.mkdirs()
            }
            deleteOnExit()
            createNewFile()
        }
        val versionMap = HashMap<Int, VersionInfo>()
        targetUrls?.forEach { (gameType, info) ->
            val matchResSaveInfo = info.list?.associate {
                it.matchResUrl to getMatchResFileSaveName(gameType)
            } ?: emptyMap()
            versionMap[gameType] = VersionInfo(info.version, matchResSaveInfo)
        }
        val buildInAppInfo = BuildInAppVersion(versionMap)
        outFile.writeText(json.adapter(BuildInAppVersion::class.java).toJson(buildInAppInfo))
    }

    private fun getMatchResFileSaveName(gameType: Int) =
        "match_${gameType}.zip"

    private fun getInGameResSaveName(gameType: Int) = "game_$gameType.zip"
    private fun getInGameResManifestSaveName(gameType: Int) = "game_${gameType}_manifest.manifest"

    /**
     * @return file 大小
     */
    private suspend fun downloadFile(url: String, targetSavePath: String): Long {
        log("downloadFile,url:$url,targetSavePath:$targetSavePath")
        if (url.isEmpty()) {
            return 0L
        }
        val outFile = File(targetSavePath).apply {
            if (!parentFile.exists()) {
                parentFile.mkdirs()
            }
            deleteOnExit()
            createNewFile()
        }
        httpClient.run { get(url) {}.bodyAsChannel().copyTo(FileOutputStream(outFile)) }
        log(" load zip $targetSavePath success ")
        return outFile.length()
    }

    private fun log(msg: String) {
        println("IntegrateCocosResAppPlugin FetchResZipTaskApp $msg")
    }

}