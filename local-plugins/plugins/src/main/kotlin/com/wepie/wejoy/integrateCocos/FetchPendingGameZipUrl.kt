package com.wepie.wejoy.integrateCocos

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Types
import io.ktor.client.request.forms.MultiPartFormDataContent
import io.ktor.client.request.forms.formData
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import kotlinx.coroutines.runBlocking
import org.gradle.api.DefaultTask
import org.gradle.api.file.RegularFileProperty
import org.gradle.api.provider.ListProperty
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.OutputFile
import org.gradle.api.tasks.TaskAction
import com.wepie.wejoy.util.httpClient
import com.wepie.wejoy.util.json
import java.io.File
import java.io.FileOutputStream

/**
 * 负责获取需要下载的游戏的url，并将url信息写入 buildConfig，用于 在app内部获取 并进行展示
 */
abstract class FetchPendingGameZipUrl : DefaultTask() {
    @get:Input
    abstract val gameTypes: ListProperty<Int>

    @get:Input
    abstract val appVersionName: Property<String>

    @get:Input
    abstract val appVersionCode: Property<Int>

    @get:Input
    abstract val disableUpdate: Property<Boolean>

    @get:Input
    abstract val enableRespCache: Property<Boolean>

    @get:Input
    abstract val buildInLangs: ListProperty<String>

    @get:Input
    abstract val cocosZipUrl: Property<String>

    @get:OutputFile
    abstract val outputFile: RegularFileProperty
    private val targetUrls: MutableMap<Int, GameResource> = HashMap()

    @TaskAction
    fun taskAction() {
        if (gameTypes.get().isEmpty() || disableUpdate.get() == true) {
            outputFile.get().asFile.let {
                it.parentFile.mkdirs()
                it.writeText("{}")
                log(" pending json file:${it.absolutePath}")
            }
            return
        }
        runBlocking {
            parseCocosZipResp(loadResp())
        }
    }

    private suspend fun loadResp(): String {
        if (enableRespCache.get()) {
            log("cocos cache path :$CocosRespUrlCachePath")
            val cacheFile = File(CocosRespUrlCachePath)
            if (cacheFile.exists() && cacheFile.length() > 0) {
                log("url resp load form local")
                return cacheFile.readText()
            } else {
                cacheFile.deleteOnExit()
                log("url resp load form remoteServer and cache")
                return loadAndCacheResp()
            }
        }
        log("url resp load form remoteServer")
        return fetchRespFromRemote().bodyAsText()
    }

    private suspend fun loadAndCacheResp(): String {
        val respStr = fetchRespFromRemote().bodyAsText()
        FileOutputStream(File(CocosRespUrlCachePath).also {
            if (!it.exists()) {
                it.createNewFile()
            }
        }).bufferedWriter().use {
            it.write(respStr)
        }
        return respStr
    }

    private suspend fun fetchRespFromRemote(): HttpResponse {
        val resp = httpClient.post(cocosZipUrl.get()) {
            setBody(
                MultiPartFormDataContent(
                    formData {
                        // 添加文本字段
                        append("resource_lang", buildInLangs.get().joinToString(","))
                    }
                )
            )
        }
        return resp
    }


    private suspend fun parseCocosZipResp(bodyStr: String) {
        val cocoZipResp = json.adapter(CocosResZipResp::class.java).fromJson(bodyStr)
        val supportLangs = HashSet(buildInLangs.get())
        cocoZipResp ?: return
        gameTypes.get().forEach { targetGame ->
            val transformedResult = cocoZipResp.result.tryLoadFromLark(appVersionName.get())
            val manualGameTypes = transformedResult.first
            transformedResult.second.gameResourceList.firstOrNull { it.gameType == targetGame }
                ?.let {
                    val targetMatchResList = it.list?.filter { matchRes ->
                        supportLangs.contains(matchRes.lang)
                    }
                    val isManual = manualGameTypes.contains(targetGame)
                    targetUrls[targetGame] = it.copy(list = targetMatchResList, isManual = isManual)
                } ?: run {
                log(" can`t meet $targetGame")
            }
        }
        val jsonAdapter: JsonAdapter<MutableMap<Int, GameResource>> = json.adapter(
            Types.newParameterizedType(
                MutableMap::class.java,
                Integer::class.java,
                GameResource::class.java
            )
        )
        outputFile.get().asFile.let {
            it.parentFile.mkdirs()
            it.writeText(jsonAdapter.toJson(targetUrls))
            log(" pending json file:${it.absolutePath}")
        }
    }

    private fun log(msg: String) {
        println("IntegrateCocosResAppPlugin FetchPendingGameZipUrl $msg")
    }
}