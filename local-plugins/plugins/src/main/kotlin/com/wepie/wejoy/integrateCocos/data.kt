package com.wepie.wejoy.integrateCocos

import com.squareup.moshi.Json
import java.io.File


val CocosRespUrlCachePath: String =
    File(System.getProperty("java.io.tmpdir"), "cocos_embed_resp_url_cache").canonicalPath
internal val cocosZipsUrlDev =
    "https://api-dev-ar.jackarooapp.com/const_api/tools/get_game_resource_version"
internal val cocosZipUrlRelease =
    "https://api-ar.jackarooapp.com/const_api/tools/get_game_resource_version"

data class CocosResZipResp(
    val code: Int = 0,
    val msg: String = "",
    val result: Result
)

data class Result(
    @Json(name = "game_resource_list")
    val gameResourceList: List<GameResource> = emptyList()
)

data class GameResource(
    @Json(name = "game_type")
    val gameType: Int = 0,
    @<PERSON><PERSON>(name = "version")
    val version: Int = 0,
    @<PERSON>son(name = "opengl")
    val inGameResUrl: String = "",
    /**
     * inGameRes资源的manifest文件
     */
    @Json(name = "manifest")
    val inGameResManifest: String = "",
    @Json(name = "match_res_list")
    val list: List<MatchRes>? = emptyList(),
    /**
     * 该配置信息 的游戏资源信息 [inGameResUrl]，[inGameResManifest]， [version]
     *  是否被手动配置覆盖
     */
    val isManual: Boolean = false
)

data class BuildInAppVersion(
    @Json(name = "versions")
    val versionMap: Map<Int, VersionInfo> = emptyMap()
)

data class VersionInfo(
    @Json(name = "inGame_res_version")
    val inGameResVersion: Int = 0,
    /**
     * key 为 版本信息。value 为文件名
     */
    @Json(name = "match_res_versions")
    val matchResVersion: Map<String, String> = emptyMap()
)

data class MatchRes(
    //语言
    @Json(name = "lang")
    val lang: String = "",
    @Json(name = "match_res_url")
    val matchResUrl: String = "",
)


data class DownloadZipSaveInfo(
    @Json(name = "match_zips")
    val matchPicZips: MutableList<String> = ArrayList(),
    @Json(name = "game_zips")
    val gameZips: MutableList<String> = ArrayList(),
)

enum class PicCheckScope {
    //匹配资源
    MATCH,

    //Cocos游戏内资源
    GAME,

    //所有资源
    ALL
}

fun GameResource.replaceRespGameZipUrlAndManifest(
    targetZipUrl: String,
    targetManifestUrl: String,
    targetResVersion: Int
): GameResource {
    return this.copy(
        inGameResManifest = targetManifestUrl,
        inGameResUrl = targetZipUrl,
        version = targetResVersion
    )
}

//替换 游戏代码包为特定 版本 进行内置
fun Result.replaceGameZipInfo(
    gameType: Int, targetZipUrl: String,
    targetManifestUrl: String,
    targetResVersion: Int
): Result {
    return gameResourceList.map {
        if (it.gameType == gameType) {
            it.replaceRespGameZipUrlAndManifest(targetZipUrl, targetManifestUrl, targetResVersion)
        } else {
            it
        }
    }.let {
        Result(it)
    }
}

/**
 * @return Pair first 表示从Lark 获取的 gameType信息，second 表示替换后的Result
 */
suspend fun Result.tryLoadFromLark(appVersionName: String): Pair<Set<Int>, Result> {
    val configList = readCocosEmbedConfig(appVersionName)
    var result = this
    val set = HashSet<Int>()
    configList.forEach {
        set.add(it.config.gameType)
        result = result.replaceGameZipInfo(
            it.config.gameType,
            it.config.cocoGameResUrl,
            it.config.manifest,
            it.config.cocoGameResVersion.toInt()
        )
    }.also {
        println("tryLoadFromLark,curAppVersionName:$appVersionName,lark Config :$configList")
    }
    return set to result
}

