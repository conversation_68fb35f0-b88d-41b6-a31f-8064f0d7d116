package com.wepie.wejoy.integrateCocos


open class IntegrateCocosResAppExtension {
    /**
     *     需要被内置的cocos游戏的gameType
     */
    var gameTypes: MutableList<Int> = ArrayList()
    var cocosZipInfoBuildConfigKey: String = ""
    var languages: MutableList<String> = ArrayList()

    /**
     * 是否关闭内置cocos资源包功能
     */
    @Deprecated("not use any more ", replaceWith = ReplaceWith("enableCache"))
    val disableUpdate: Boolean = false
    var enablePicSizeCheck: Boolean = false

    /**
     *  enablePicSizeCheck 为true时，会尝试对匹配资源内的图片进行压缩，当压缩率超过 alertThreshold 并且 压缩大小超过 alertSizeThreshold 时并且 记录到文件中
     */

    var alertThreshold: Float = 0.9f

    /**
     *  enablePicSizeCheck 为true时，会尝试对匹配资源内的图片进行压缩，当压缩率超过 alertThreshold 并且 压缩大小超过 alertSizeThreshold 时并且 记录到文件中
     *  单位字节
     */
    var alertSizeThreshold: Long = 10 * 1024 * 1024

    /**
     * 开启后 会尝试读取缓存的请求，也就是尽量不发新请求
     */
    var enableCache: Boolean = false

    /**
     * 是否保存内置的cocos信息到飞书表格
     */
    var saveEmbedResultToLark: Boolean = true
    var picCheckScope: PicCheckScope = PicCheckScope.ALL
    override fun toString(): String {
        return "gameTypes:$gameTypes,languages=$languages, enableCache:$enableCache，saveEmbedResultToLark:$saveEmbedResultToLark,disableUpdate=$disableUpdate,enablePicSizeCheck=$enablePicSizeCheck,compressThreshold=$alertThreshold,compressSizeThreshold=$alertSizeThreshold B"
    }
}