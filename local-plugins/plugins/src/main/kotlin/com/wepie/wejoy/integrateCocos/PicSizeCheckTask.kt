package com.wepie.wejoy.integrateCocos

import com.wepie.wejoy.util.json
import com.wepie.wejoy.util.toHumanReadStr
import org.gradle.api.DefaultTask
import org.gradle.api.file.Directory
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.file.RegularFileProperty
import org.gradle.api.provider.Property
import org.gradle.api.provider.Provider
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.InputFile
import org.gradle.api.tasks.OutputDirectory
import org.gradle.api.tasks.TaskAction
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.Locale
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream

/**
 * 解压 Cocos 资源包，并调用压缩工具，压缩图片，检查压缩比，若压缩比超过一定值，说明该zip的图片还有待压缩，
 * 打包时进行提示,
 * 检查结果 默认存储在 wepie/build/tmp/${variant}officialDebugPicSizeCheckTask/report.txt
 * 如打包变体为 officialDebug，则存储在 wepie/build/tmp/officialDebugPicSizeCheckTask/report.txt
 */
abstract class PicSizeCheckTask : DefaultTask() {
    @get:InputFile
    abstract val inputFile: RegularFileProperty

    @get:Input
    abstract val alertThreshold: Property<Float>

    @get:Input
    abstract val alertSizeThreshold: Property<Long>

    @get:Input
    abstract val checkScope: Property<PicCheckScope>

    @get:OutputDirectory
    abstract val outDir: DirectoryProperty
    private lateinit var reportFile: File
    private val overThresholdRecords = ArrayList<Record>()

    class Record(
        val originSize: Long,
        val compressSize: Long,
        val compressRate: Float,
        val path: String
    ) {
        override fun toString(): String {
            return "pic src size:${
                originSize.toHumanReadStr()
            } ,compress size:${compressSize.toHumanReadStr()},compress rate is ${
                String.format(
                    Locale.getDefault(),
                    "%.2f",
                    compressRate
                )
            } ,path:$path"
        }
    }

    @TaskAction
    fun taskAction() {
        getBaseDir().get().asFile.deleteRecursively()
        reportFile =
            File(outDir.get().asFile.absolutePath + File.separator + "report.txt").also {
                it.createNewFile()
            }
        getPendingZips(loadFilesPath(inputFile.get().asFile)).forEach {
            ZipInputStream(FileInputStream(it)).use { zipIs ->
                zipIs.process { r: Record ->
                    overThresholdRecords.add(r)
                }
            }
        }
        overThresholdRecords.sortByDescending { it.originSize }
        FileOutputStream(reportFile).bufferedWriter().use { reportWriter ->
            overThresholdRecords.forEach {
                reportWriter.write(it.toString() + "\n")
            }
        }
        "PicSizeCheckTask check result saved in ${reportFile.absolutePath}".log()
    }

    private fun getBaseDir(): Provider<Directory> {
        return outDir.dir("PicSizeCheckTask")
    }

    fun clearOut() {
        "clearOut".log()
        getBaseDir().get().asFile.deleteRecursively()
    }

    private fun getPendingZips(downloadZipSaveInfo: DownloadZipSaveInfo): List<String> {
        val result = ArrayList<String>()
        when (checkScope.get()) {
            PicCheckScope.MATCH -> {
                result.addAll(downloadZipSaveInfo.matchPicZips)
            }

            PicCheckScope.GAME -> {
                result.addAll(downloadZipSaveInfo.gameZips)
            }

            PicCheckScope.ALL -> {
                result.addAll(downloadZipSaveInfo.matchPicZips)
                result.addAll(downloadZipSaveInfo.gameZips)
            }

            else -> {
                result.addAll(downloadZipSaveInfo.matchPicZips)
                result.addAll(downloadZipSaveInfo.gameZips)
            }
        }
        return result
    }

    private fun loadFilesPath(file: File): DownloadZipSaveInfo {
        return json.adapter(DownloadZipSaveInfo::class.java).fromJson(file.readText(Charsets.UTF_8))
            ?.also {
                "checkScope:${checkScope.get()} ,DownloadSaveInfo:$it".info()
            }
            ?: DownloadZipSaveInfo()
    }

    private fun ZipInputStream.process(onOverThreshold: (r: Record) -> Unit) {
        var zipEntry: ZipEntry? = nextEntry
        while (zipEntry != null) {
            processEntry(zipEntry, this, onOverThreshold)
            zipEntry = nextEntry
        }
    }

    private fun processEntry(
        zipEntry: ZipEntry,
        zipInputStream: ZipInputStream,
        onOverThreshold: (r: Record) -> Unit
    ) {
        if (zipEntry.isDirectory) {
            return
        }
        if ((!zipEntry.name.endsWith(".png") && !zipEntry.name.endsWith(".webp"))
            || zipEntry.name.startsWith("__MAC")
        ) {
            "${zipEntry.name}  is not png or not target png ,skip size check".info()
            return
        }
        val tempFile = File(getBaseDir().get().asFile.path, zipEntry.name)
        if (!tempFile.parentFile.exists()) {
            tempFile.parentFile.mkdirs()
        }
        zipInputStream.copyTo(FileOutputStream(tempFile))
        checkPicSizeValid(tempFile.absolutePath, onOverThreshold)
    }

    private fun checkPicSizeValid(srcPicPath: String, onOverThreshold: (r: Record) -> Unit) {
        val webpPath = compress(srcPicPath)
        checkCompressRate(srcPicPath, webpPath, onOverThreshold)
    }

    private fun compress(srcPicPath: String): String {
        val webpPath = if (srcPicPath.endsWith(".png")) {
            srcPicPath.replace(".png", ".webp")
        } else {
            srcPicPath.replace(".webp", "_c.webp")
        }
        val cmd = "${getToolPath()} -q 100 $srcPicPath -o $webpPath"
        val ret = Runtime.getRuntime().exec(cmd).waitFor()
        if (ret != 0) {
            logger.warn("PicSizeCheckTask exec compress error,$ret,cmd:$cmd")
            return srcPicPath
        }
        return webpPath
    }

    private fun checkCompressRate(
        srcPath: String,
        dstPath: String,
        onOverThreshold: (r: Record) -> Unit
    ) {
        val srcFile = File(srcPath)
        val dstFile = File(dstPath)
        val compressSize = srcFile.length() - File(dstPath).length()
        val compressRate = compressSize / srcFile.length().toFloat()
        if (compressRate > alertThreshold.get() && compressSize >= alertSizeThreshold.get()) {
            onOverThreshold(Record(srcFile.length(), compressSize, compressRate, srcPath))
        }
        srcFile.deleteOnExit()
        dstFile.deleteOnExit()
    }

    private fun getToolPath(): String {
        val name = System.getProperty("os.name").lowercase(Locale.ROOT)
        val arch = System.getProperty("os.arch").lowercase(Locale.ROOT)
        return when {
            name.contains("mac") -> {
                if (arch.contains("aarch64"))
                    project.rootProject.file("compressTools/mac/arm64/cwebp").canonicalPath
                else
                    project.rootProject.file("compressTools/mac/cwebp").canonicalPath
            }

            name.contains("windows") -> {
                project.rootProject.file("compressTools/windows/cwebp").canonicalPath
            }

            else -> {
                project.rootProject.file("compressTools/linux/cwebp").canonicalPath
            }
        }
    }

    private fun String.log() {
        println("PicSizeCheckTask $this")
    }

    private fun String.info() {
        logger.info("PicSizeCheckTask $this")
    }
}