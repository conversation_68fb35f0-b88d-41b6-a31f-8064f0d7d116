package com.wepie.wejoy.integrateCocos

import com.android.build.api.variant.ApplicationAndroidComponentsExtension
import com.android.build.api.variant.BuildConfigField
import com.android.build.api.variant.SourceDirectories
import com.android.build.api.variant.Variant
import com.android.build.gradle.AppPlugin
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.provider.Property
import org.gradle.api.tasks.TaskProvider
import org.gradle.internal.extensions.core.extra
import java.io.File

class IntegrateCocosResAppPlugin : Plugin<Project> {
    private val assetPackDirName = "cocos_build_in_asset"
    override fun apply(project: Project) {
        val customExtension =
            project.extensions.create("IntegrateCocos", IntegrateCocosResAppExtension::class.java)
        val usePad = project.rootProject.extra.get("USE_PAD") as Boolean
        project.plugins.withType(AppPlugin::class.java) {
            val androidComponents =
                project.extensions.getByType(ApplicationAndroidComponentsExtension::class.java)
            appendAssetPacksIfNeed(usePad, androidComponents)
            registerTaskBVariant(project, androidComponents, customExtension, usePad)
        }
    }

    private fun registerTaskBVariant(
        project: Project,
        androidComponents: ApplicationAndroidComponentsExtension,
        customExtension: IntegrateCocosResAppExtension,
        usePad: Boolean
    ) {
        androidComponents.onVariants { variant ->
            if (!isTargetVariant(variant)) {
                return@onVariants
            }
            val appVersionName = variant.outputs.single().versionName
            val appVersionCode = variant.outputs.single().versionCode
            println("IntegrateCocosResAppPlugin ${variant.name} customExtension:${customExtension},usePad=$usePad")
            variant.sources.res?.let { res ->
                val fetchUrlsTask =
                    registerFetchPendingGameZipUrlTask(
                        project,
                        variant,
                        customExtension,
                        appVersionName,
                        appVersionCode
                    )
                val readCocoZipUrlsInfoTask =
                    registerReadCocoZipUrlsInfoTask(project, variant, fetchUrlsTask)
                appendBuildConfig(variant, customExtension, readCocoZipUrlsInfoTask)
                val downloadZipTask = registerDownloadResTask(
                    project, variant, appVersionName, appVersionCode,
                    res, fetchUrlsTask, usePad, customExtension.saveEmbedResultToLark
                )
                setAssetPackPreBundleTaskDependIfNeed(project, variant, downloadZipTask, usePad)
                registerPicSizeCheckIfNeed(project, variant, downloadZipTask, customExtension)
            }
        }
    }

    private fun registerPicSizeCheckIfNeed(
        project: Project,
        variant: Variant,
        downloadZipTask: TaskProvider<FetchResZipTaskApp>,
        customExtension: IntegrateCocosResAppExtension,
    ): TaskProvider<PicSizeCheckTask>? {
        if (!customExtension.enablePicSizeCheck) {
            return null
        }
        val taskName = "${variant.name}PicSizeCheckTask"
        return project.tasks.register(
            taskName,
            PicSizeCheckTask::class.java
        ) {
            it.inputFile.set(FetchResZipTaskApp::outDownloadZipInfoFile.get(downloadZipTask.get()))
            it.alertThreshold.set(customExtension.alertThreshold)
            it.alertSizeThreshold.set(customExtension.alertSizeThreshold)
            it.outDir.set(project.layout.buildDirectory.dir("tmp/$taskName"))
            it.checkScope.set(customExtension.picCheckScope)
            it.dependsOn(downloadZipTask)
            it.doLast { _ ->
                it.clearOut()
            }
        }.also {
            downloadZipTask.get().finalizedBy(it)
        }
    }

    private fun setAssetPackPreBundleTaskDependIfNeed(
        project: Project,
        variant: Variant,
        fetchResZipTaskAppProvider: TaskProvider<FetchResZipTaskApp>,
        usePad: Boolean
    ) {
        if (!usePad) {
            return
        }
        //明确指定 assetPackPreBundleTask 任务依赖 FetchResZipTaskApp
        project.tasks.whenTaskAdded {
            if (it.name == "assetPack${variant.name.replaceFirstChar { it.uppercase() }}PreBundleTask") {
                println("IntegrateCocosResAppPlugin task  find!~ :${it.name}")
                it.dependsOn(fetchResZipTaskAppProvider)
            }
        }

    }


    private fun appendAssetPacksIfNeed(
        usePad: Boolean,
        androidComponents: ApplicationAndroidComponentsExtension
    ) {
        if (usePad) {
            androidComponents.finalizeDsl {
                it.assetPacks += ":$assetPackDirName"
            }
        }
    }

    private fun isTargetVariant(variant: Variant): Boolean {
        return !(!variant.buildType.equals(
            "release",
            true
        ) && !variant.buildType.equals(
            "debug",
            true
        ))
    }


    private fun registerFetchPendingGameZipUrlTask(
        project: Project,
        variant: Variant,
        customExtension: IntegrateCocosResAppExtension,
        appVersionName: Property<String>,
        appVersionCode: Property<Int>
    ): TaskProvider<FetchPendingGameZipUrl> {
        val fetchCocosZipUrlName = "${variant.name}FetchCocosZipUrls"
        return project.tasks.register(
            fetchCocosZipUrlName,
            FetchPendingGameZipUrl::class.java
        ) {
            val cocosZipUrl = if (variant.buildType.equals(
                    "release",
                    true
                )
            ) cocosZipUrlRelease else cocosZipsUrlDev
            it.appVersionName.set(appVersionName)
            it.appVersionCode.set(appVersionCode)
            it.disableUpdate.set(customExtension.disableUpdate)
            it.cocosZipUrl.set(cocosZipUrl)
            it.gameTypes.set(customExtension.gameTypes)
            it.buildInLangs.set(customExtension.languages)
            it.enableRespCache.set(customExtension.enableCache)
            it.outputs.upToDateWhen { false }
            it.outputFile.set(
                File(
                    project.layout.buildDirectory.asFile.get(),
                    "intermediates/$fetchCocosZipUrlName/output"
                )
            )
        }
    }

    private fun registerReadCocoZipUrlsInfoTask(
        project: Project,
        variant: Variant,
        fetchUrlsTask: TaskProvider<FetchPendingGameZipUrl>
    ): TaskProvider<ReadCocoZipUrlsInfo> {
        val readCocoZipUrlsInfoTaskName = "${variant.name}ReadCocosZipUrlsInfo"
        return project.tasks.register(
            readCocoZipUrlsInfoTaskName,
            ReadCocoZipUrlsInfo::class.java
        ) {
            it.inputFile.set(FetchPendingGameZipUrl::outputFile.get(fetchUrlsTask.get()))
            it.outputFile.set(
                File(
                    project.layout.buildDirectory.asFile.get(),
                    "intermediates/$readCocoZipUrlsInfoTaskName/output"
                )
            )
        }
    }


    private fun appendBuildConfig(
        variant: Variant,
        customExtension: IntegrateCocosResAppExtension,
        readCocoZipUrlsInfoTask: TaskProvider<ReadCocoZipUrlsInfo>,
    ) {
        variant.buildConfigFields.put(
            customExtension.cocosZipInfoBuildConfigKey,
            readCocoZipUrlsInfoTask.map { readInfoTask ->
                BuildConfigField(
                    type = "String",
                    value = "\"${
                        readInfoTask.outputFile.asFile.get()
                            .readText(Charsets.UTF_8)
                    }\"",
                    comment = "Cocos resource inApp version info"
                )
            })
    }

    private fun registerDownloadResTask(
        project: Project,
        variant: Variant,
        appVersionName: Property<String>,
        appVersionCode: Property<Int>,
        res: SourceDirectories.Layered,
        fetchUrlsTask: TaskProvider<FetchPendingGameZipUrl>,
        usePad: Boolean,
        saveEmbedResultToLark: Boolean
    ): TaskProvider<FetchResZipTaskApp> {
        val downloadResToAssetTask =
            project.tasks.register(
                "${variant.name}DownloadResCocosAppZip",
                FetchResZipTaskApp::class.java
            ) {
                it.doFirst {
                    println("DownloadResCocosAppZip task at first")
                }
                val assetsPath = "${File.separator}src${File.separator}main${File.separator}assets"
                it.assetPackOutputDir.set(
                    File(
                        project.rootProject.project(":$assetPackDirName").projectDir,
                        assetsPath
                    )
                )
                it.outDownloadZipInfoFile.set(project.layout.buildDirectory.file("tmp/${variant.name}FetchResZipTask/AppSavePathInfo.txt"))
                it.usePad.set(usePad)
                it.appVersionName.set(appVersionName)
                it.appVersionCode.set(appVersionCode)
                it.inputFile.set(FetchPendingGameZipUrl::outputFile.get(fetchUrlsTask.get()))
                it.saveResultToLark.set(saveEmbedResultToLark)
                it.description = " download cocos res release zip to Assets "
                it.doLast { _ ->
                    println("DownloadResCocosAppZip task exec finish")
                }
            }
        res.addGeneratedSourceDirectory(
            downloadResToAssetTask,
            FetchResZipTaskApp::outputDir
        )

        return downloadResToAssetTask
    }

}