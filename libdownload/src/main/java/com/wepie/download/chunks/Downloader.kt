package com.wepie.download.chunks

import com.huiwan.base.util.FileUtil
import com.huiwan.base.util.JsonUtil
import com.huiwan.platform.ThreadUtil
import com.wepie.download.DownloadUtil
import com.wepie.download.FileEntity
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import okhttp3.Headers
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody
import java.io.RandomAccessFile
import java.util.concurrent.ConcurrentHashMap

// https://wepie.feishu.cn/wiki/FW05wHuXriaA0zk1TeOcjpI1nOf?from=from_copylink
object Downloader {
    private val dispatcher = ThreadUtil.newSingleThreadPool("chunk_downloader").asCoroutineDispatcher()
    private val jobs: ConcurrentHashMap<String, Job> = ConcurrentHashMap()

    @JvmStatic
    fun downloadJava(fileEntity: FileEntity, callBack: UpdateCallBack?): DownloadResult? {
        val res = runBlocking {
            download(fileEntity, callBack)
        }
        return res
    }

    suspend fun download(fileEntity: FileEntity, callBack: UpdateCallBack?): DownloadResult? {
        var res: DownloadResult? = null
        val job = CoroutineScope(Dispatchers.IO).launch {
            res = startDownload(fileEntity, callBack)
        }
        jobs[fileEntity.url] = job
        job.join()
        val flag = res?.taskOver ?: false
        if (flag) {
            FileUtil.safeDeleteFile(fileEntity.chunkConfig.configFile)
        }
        DownloadHelper.log("download over! over=${flag}, msg=${res?.msg}, url=${fileEntity.url}")
        return res
    }

    fun stop(url: String?) {
        if (!url.isNullOrEmpty()) {
            val job = jobs[url]
            job?.cancel(CancellationException("stop!"))
            val key = jobs.remove(url)
            DownloadHelper.log("stop! key=$key")
        }
    }

    private suspend fun startDownload(fileEntity: FileEntity, callBack: UpdateCallBack?): DownloadResult? {
        return try {
            val response = startDownload(fileEntity.url, null, null)
            initChunkInfo(fileEntity, response)
            DownloadHelper.log("download！size=${fileEntity.fileSize}, url=${fileEntity.url}")
            if (fileEntity.chunkConfig.supportChunk) {
                chunkDownload(fileEntity, callBack)
            } else {
                DownloadResult(false, fileEntity, "not support!")
            }
        } catch (e: Exception) {
            DownloadResult(false, fileEntity, "startDownload  Exception, msg=$e")
        }
    }

    private fun initChunkInfo(fileEntity: FileEntity, response: Response) {
        fileEntity.fileSize = (response.headers["Content-Length"] ?: "-1").toLong()
        val support = "bytes".equals(response.headers["Accept-Ranges"] ?: "", true)
        val eTag = response.headers["ETag"] ?: ""
        ChunkInfo.build(fileEntity, support, eTag)
    }

    private suspend fun chunkDownload(fileEntity: FileEntity, callBack: UpdateCallBack?): DownloadResult? {
        return if (fileEntity.fileSize > 0) {
            if (fileEntity.chunkConfig.chunks.isEmpty()) {
                DownloadHelper.splitChunks(fileEntity)
            }
            DownloadHelper.printChunks(fileEntity.chunkConfig.chunks)
            if (fileEntity.chunkConfig.chunks.isNotEmpty()) {
                doChunkDownload(fileEntity, callBack)
            } else {
                DownloadResult(false, fileEntity, "fileEntity.chunks is empty! ")
            }
        } else {
            DownloadResult(false, fileEntity, "chunkDownload size error! ")
        }
    }

    private suspend fun doChunkDownload(fileEntity: FileEntity, callBack: UpdateCallBack?): DownloadResult {
        // CoroutineExceptionHandler no use ，install it in root coroutine plz
        val handler = CoroutineExceptionHandler { _, exception ->
            val msg = "downloadSync, onFailure, msg=$exception"
            DownloadHelper.log(msg)
        }
        notifyProgress(fileEntity, callBack)
        withContext(Dispatchers.IO + handler) {
            val writeScope = CoroutineScope(dispatcher + handler)
            val deferreds: List<Deferred<ChunkData>> = fileEntity.chunkConfig.chunks.map { chunkData ->
                async {
                    if (chunkData.isDownloaded()) {
                        DownloadHelper.log("【${chunkData.id}】is downloaded!")
                    } else {
                        initChunkRegion(chunkData)
                        val response = startDownload(fileEntity.url, chunkData, fileEntity.chunkConfig.eTag)
                        response.body?.let {
                            handleChunkResponse(fileEntity, chunkData, it,writeScope, callBack)
                        }
                    }
                    chunkData
                }
            }
            deferreds.awaitAll()
            writeScope.cancel()
            updateProgress(fileEntity, null)
            DownloadHelper.log("download finished! url=${fileEntity.url}")
            fileEntity.chunkConfig.chunkConfigFile.close()
        }
        return DownloadResult(true, fileEntity, "download finished!")
    }

    private fun initChunkRegion(chunkData: ChunkData) {
        chunkData.currentStart = chunkData.start + chunkData.bytesWritten
        if (chunkData.currentStart > chunkData.end) {
            chunkData.currentStart = chunkData.start
        }
    }

    private suspend fun handleChunkResponse(fileEntity: FileEntity, chunkData: ChunkData,
                                            body: ResponseBody,writeScope:CoroutineScope, callBack: UpdateCallBack?) {
        withContext(Dispatchers.IO) {
            val byteStream = body.byteStream()
            val randomAccessFile = RandomAccessFile(fileEntity.chunkConfig.tempFile, "rw")
            randomAccessFile.seek(chunkData.currentStart)
            var bytesRead: Int
            val buffer = ByteArray(ChunkInfo.config.chunkReadSize)
            try {
                var bytesWritten = chunkData.bytesWritten
                while (byteStream.read(buffer).also { bytesRead = it } != -1) {
                    val bytesToWrite = Math.min(bytesRead, (chunkData.end - chunkData.start + 1 - bytesWritten).toInt())
                    randomAccessFile.write(buffer, 0, bytesToWrite)
                    bytesWritten += bytesToWrite
                    chunkData.bytesWritten = bytesWritten
                    writeScope.launch {
                        updateProgress(fileEntity, callBack)
                    }
                }
            } finally {
                randomAccessFile.close()
                byteStream.close()
            }
        }
    }

    private fun startDownload(url: String, chunkData: ChunkData?, eTag: String?): Response {
        val headers = Headers.Builder()
        val method: String
        if (null != chunkData) {
            headers.add("Range", "bytes=${chunkData.currentStart}-${chunkData.end}")
            method = "GET"
        } else {
            method = "HEAD"
        }
        if (!eTag.isNullOrEmpty()) {
            headers.add("If-None-Match", eTag)
        }
        val request: Request = Request.Builder()
            .url(url)
            .method(method, null)
            .headers(headers.build())
            .build()
        val call = DownloadUtil.getDownloadClient()
            .newCall(request)
        val response = call.execute()
        if (!DownloadHelper.isNormalHttpCode(response.code)) {
            val msg = "response over! code=${response.code}, ${response.protocol},【${chunkData?.start}-${chunkData?.end}】, url=${url}"
            DownloadHelper.log(msg)
        }
        return response
    }
    // save progress to local
    private fun updateProgress(fileEntity: FileEntity, callBack: UpdateCallBack?) {
        val configFile = fileEntity.chunkConfig.chunkConfigFile
        synchronized(configFile) {
            notifyProgress(fileEntity, callBack)
            val config = JsonUtil.toJson(fileEntity.chunkConfig)
            configFile.setLength(0)
            configFile.seek(0)
            configFile.write(config.toByteArray())
        }
    }

    private fun notifyProgress(fileEntity: FileEntity, callBack: UpdateCallBack?) {
        var cur = 0L
        fileEntity.chunkConfig.chunks.forEach {
            cur += it.bytesWritten
        }
        val progress = (cur.toDouble() / fileEntity.fileSize * 100).toInt()
        if ((progress - fileEntity.chunkConfig.progress) > 1) {
            callBack?.updatePercent(fileEntity, progress, cur, fileEntity.fileSize)
            fileEntity.chunkConfig.progress = progress
        }
    }
}

data class DownloadResult(val taskOver: Boolean, val fileEntity: FileEntity, val msg: String?)

interface UpdateCallBack {
    fun updatePercent(fileEntity: FileEntity, percent: Int, cur: Long, total: Long)
}