package com.wepie.download.chunks

import android.text.TextUtils
import com.google.gson.annotations.SerializedName
import com.huiwan.base.common.ChunkConfig
import com.huiwan.base.util.FileUtil
import com.huiwan.base.util.JsonUtil
import com.huiwan.store.file.FileCacheName
import com.huiwan.store.file.FileConfig
import com.huiwan.store.file.IOUtilities
import com.wepie.download.FileEntity
import java.io.File
import java.io.RandomAccessFile

class ChunkInfo {
    @SerializedName("originalUrl")
    var url: String = ""

    @SerializedName("downloadUrl")
    var downloadUrl: String = ""

    @SerializedName("md5")
    var md5 = ""

    @SerializedName("totalLength")
    var fileSize = -1L

    @Transient
    var chunkNumber = 0

    @SerializedName("segments")
    val chunks: MutableList<ChunkData> = ArrayList()

    @Transient
    lateinit var chunkConfigFile: RandomAccessFile

    @Transient
    var supportChunk = true

    @Transient
    var progress = 0

    @Transient
    lateinit var tempFile: String

    @Transient
    lateinit var configFile: String

    @SerializedName("http_eTag")
    var eTag: String = ""

    companion object {
        const val HTTP_CODE_NORMAL = 200
        const val HTTP_CODE_NORMAL_CHUNK = 206
        var config = ChunkConfig()

        fun build(fileEntity: FileEntity, support: Boolean, eTag: String) {
            val url = fileEntity.url
            val tempFile = fileEntity.tempFile()
            val dir =
                tempFile.parentFile ?: File(FileConfig.getResourceFolderPath(), FileCacheName.FILE)
            IOUtilities.ensureDir(dir)
            val config = getConfigFile(dir.path, url)
            val configFile = RandomAccessFile(config, "rw")
            var chunkInfo = initLocal(configFile)
            if (null == chunkInfo
                || TextUtils.isEmpty(chunkInfo.url)
                || !eTag.equals(chunkInfo.eTag, true)
                || !tempFile.exists()
            ) {
                FileUtil.safeDeleteFile(tempFile)
                chunkInfo = ChunkInfo()
                chunkInfo.url = url
                chunkInfo.downloadUrl = url
                chunkInfo.fileSize = fileEntity.fileSize
                chunkInfo.eTag = eTag
                chunkInfo.md5 = DownloadHelper.md5(url)
            } else {
                DownloadHelper.log("use local download file!")
            }
            chunkInfo.tempFile = tempFile.path
            chunkInfo.configFile = config.path
            chunkInfo.supportChunk = support
            chunkInfo.chunkConfigFile = configFile
            fileEntity.chunkConfig = chunkInfo
        }

        // load local temp file
         internal fun initLocal(configFile: RandomAccessFile): ChunkInfo? {
            var chunkConfig: ChunkInfo? = null
            try {
                val length = configFile.length().toInt()
                if (length > 0) {
                    val buffer = ByteArray(length)
                    configFile.readFully(buffer)
                    val content = String(buffer)
                    chunkConfig = JsonUtil.fromJson(content, ChunkInfo::class.java)
                }
            } catch (e: Exception) {
                DownloadHelper.log("initLocal error! msg=$e")
            }
            return chunkConfig
        }

        private fun getConfigFile(dir: String, url: String): File {
            return File(dir, "${DownloadHelper.md5(url)}.config")
        }

        fun getTempFileName(url: String): String {
            return "${DownloadHelper.md5(url)}.temp"
        }
    }
}