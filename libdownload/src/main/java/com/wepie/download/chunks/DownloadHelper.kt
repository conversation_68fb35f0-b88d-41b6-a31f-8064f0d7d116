package com.wepie.download.chunks

import com.huiwan.base.LibBaseUtil
import com.huiwan.base.common.ChunkConfig
import com.wepie.download.FileEntity
import com.wepie.liblog.main.HLog
import java.io.File
import java.io.RandomAccessFile
import java.lang.StringBuilder
import java.math.BigInteger
import java.security.MessageDigest
import java.util.Locale
import kotlin.math.ceil

object DownloadHelper {
    private const val TAG = "【chunk_download】"

    fun log(msg: String) {
        HLog.d(TAG, HLog.USR, "【${Thread.currentThread().name}】 $msg")
    }

    fun splitChunks(fileEntity: FileEntity): Int {
        fileEntity.chunkConfig.chunkNumber = getChunkNumber(fileEntity.fileSize)
        createChunkEntity(fileEntity, fileEntity.chunkConfig.chunkNumber)
        return fileEntity.chunkConfig.chunkNumber
    }

    fun printChunks(chunks: List<ChunkData>) {
        val sb = StringBuilder()
        sb.append("******chunks_info********").append("\n")
        chunks.forEach {
            sb.append("${it.id}, ${it.start} - ${it.end}, chunk_size=${it.bytesWritten}").append("\n")
        }
        sb.append("******chunks_info_over********")
        log(sb.toString())
    }

    private fun getChunkNumber(fileSize: Long): Int {
        return if (fileSize <= ChunkInfo.config.minChunkSize) {
            1
        } else {
            var chunkNumber = ChunkInfo.config.maxChunkNumber
            while (chunkNumber > 0) {
                val x = (fileSize / chunkNumber).toInt()
                if (x >= ChunkInfo.config.minChunkSize) {
                    break
                } else {
                    chunkNumber--
                }
            }
            return chunkNumber
        }
    }

    private fun createChunkEntity(fileEntity: FileEntity, chunkNumber: Int) {
        val avgChunkSize = (ceil(fileEntity.fileSize.toDouble() / chunkNumber)).toLong()
        for (i in 1..chunkNumber) {
            var end = i * avgChunkSize - 1
            if (end > fileEntity.fileSize) {
                end = fileEntity.fileSize
            }
            addChunkEntity(i, (i - 1) * avgChunkSize, end, fileEntity.fileSize - (i - 1) * avgChunkSize, fileEntity)
        }
    }

    private fun addChunkEntity(number: Int, start: Long, end: Long, size: Long, fileEntity: FileEntity) {
        val chunkData = ChunkData(number, start, end)
        fileEntity.chunkConfig.chunks.add(chunkData)
    }

    fun md5(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        return BigInteger(1, md.digest(input.toByteArray())).toString(16).padStart(32, '0')
    }

    @JvmStatic
    fun isSuccess(fileEntity: FileEntity, result: DownloadResult?): Boolean {
        var flag = false
        if (null != fileEntity.chunkConfig && fileEntity.fileSize > 0) {
            var cur = 0L
            fileEntity.chunkConfig.chunks.forEach {
                cur += it.bytesWritten
            }
            flag = cur >= fileEntity.fileSize
        }
        if (!flag && null != result) {
            flag = result.taskOver
        }
        return flag
    }

    @JvmStatic
    fun init(dir: String?, config: ChunkConfig) {
        ChunkStrategy.update(config)
        ChunkInfo.config = config
        delete(dir)
    }

    @JvmStatic
    private fun delete(dir: String?) {
        log("delete! dir=$dir")
        if (!dir.isNullOrEmpty()) {
            deleteOldFiles(File(dir))
        }
    }

    fun isNormalHttpCode(code: Int): Boolean {
        return code == ChunkInfo.HTTP_CODE_NORMAL || code == ChunkInfo.HTTP_CODE_NORMAL_CHUNK
    }

    fun deleteOldFiles(folder: File, force: Boolean = false) {
        val currentTime = System.currentTimeMillis()
        folder.listFiles()?.forEach { file ->
            if (file.isDirectory) {
                deleteOldFiles(file)
            } else {
                val lastModified = file.lastModified()
                val name = file.name
                val interval = currentTime - lastModified
                if ((interval > (ChunkInfo.config.chunksSaveTime * 60 * 60 * 1000L) || force)
                    && !name.isNullOrEmpty()
                ) {
                    file.delete()
                }
            }
        }
    }


    // for test
    fun testReadLocalTemp(path: String?) {
        if (!LibBaseUtil.buildDebug() || path == null) {
            log("read local temp return, buildDebug=${LibBaseUtil.buildDebug()}, path=$path")
            return
        }
        try {
            val dir = File(path)
            log("read local temp, name=$path")
            if (dir.exists() && dir.isDirectory) {
                dir.listFiles()?.forEach { f ->
                    val name = f.name
                    log("local file, name=$name")
                    if (name.endsWith("config")) {
                        val file = RandomAccessFile(f, "rw")
                        val chunkInfo = ChunkInfo.initLocal(file)
                        var total = 0L
                        if (null != chunkInfo) {
                            chunkInfo.chunks.forEach {
                                total += it.bytesWritten
                            }
                            log("${chunkInfo.md5}, downloaded =${byteToMB(total)}MB,  total =${byteToMB(chunkInfo.fileSize)}MB")
                            log("chunks details:\n ${chunkInfo.chunks.joinToString("\n")}")
                        }
                    }
                }
            }
        } catch (e: Exception) {

        }
    }

    private fun byteToMB(bytes: Long): String {
        val mb = bytes.toDouble() / (1024 * 1024)
        return String.format(Locale.US, "%.2f", mb)
    }
}