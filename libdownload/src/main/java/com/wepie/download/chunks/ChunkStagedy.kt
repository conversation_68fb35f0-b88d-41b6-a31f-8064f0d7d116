package com.wepie.download.chunks

import com.huiwan.base.common.ChunkConfig
import com.huiwan.base.util.NetWorkUtil
import com.huiwan.base.util.SysUtils

// 根据设备性能和网络情况做一些调整，https://wepie.feishu.cn/wiki/IAh1wB93biLzLskXLAwce3QTnxh?from=from_copylink
object ChunkStrategy {

    fun update(config: ChunkConfig) {
        try {
            val isWifi = NetWorkUtil.isWifi()
            val isLowDevice = SysUtils.isLowEndDevice(false)
            if (!isWifi) {
                config.maxChunkNumber = 2
            }
            if (isLowDevice) {
                config.chunkReadSize = 2048
            }
            DownloadHelper.log("ChunkStrategy update! isWifi=$isWifi, isLowDevice=$isLowDevice")
        } catch (e: Exception) {

        }
    }
}