#!/bin/bash

# 别名设置脚本
# 运行此脚本后，可以在任何地方使用 sync-proto 命令

echo "🔧 设置 ProtoReflectionMapper 同步别名..."

# 获取当前目录的绝对路径
CURRENT_DIR=$(pwd)
SYNC_SCRIPT="$CURRENT_DIR/quick_sync.sh"

# 检查同步脚本是否存在
if [ ! -f "$SYNC_SCRIPT" ]; then
    echo "❌ 同步脚本不存在: $SYNC_SCRIPT"
    exit 1
fi

# 创建别名
alias sync-proto="$SYNC_SCRIPT"

# 添加到 shell 配置文件
SHELL_CONFIG=""
if [ -f "$HOME/.zshrc" ]; then
    SHELL_CONFIG="$HOME/.zshrc"
elif [ -f "$HOME/.bashrc" ]; then
    SHELL_CONFIG="$HOME/.bashrc"
elif [ -f "$HOME/.bash_profile" ]; then
    SHELL_CONFIG="$HOME/.bash_profile"
else
    echo "⚠️  未找到 shell 配置文件，请手动添加别名"
    echo "请将以下行添加到您的 shell 配置文件中:"
    echo "alias sync-proto='$SYNC_SCRIPT'"
    exit 1
fi

# 检查别名是否已存在
if grep -q "alias sync-proto" "$SHELL_CONFIG"; then
    echo "✅ 别名已存在于 $SHELL_CONFIG"
else
    # 添加别名到配置文件
    echo "" >> "$SHELL_CONFIG"
    echo "# ProtoReflectionMapper 同步别名" >> "$SHELL_CONFIG"
    echo "alias sync-proto='$SYNC_SCRIPT'" >> "$SHELL_CONFIG"
    echo "✅ 别名已添加到 $SHELL_CONFIG"
fi

echo ""
echo "🎉 别名设置完成！"
echo "=================================="
echo "使用方法:"
echo "  sync-proto                    # 快速同步"
echo "  ./sync_proto_mapper.sh        # 完整同步（带验证）"
echo ""
echo "💡 提示: 重新打开终端或运行 'source $SHELL_CONFIG' 使别名生效"
echo ""
echo "📁 当前同步脚本位置: $SYNC_SCRIPT"
echo "📁 目标文件位置: /Users/<USER>/WorkSapce/wejoy1/module/debug-tool/src/debug/java/com/wepie/debug/ProtoReflectionMapper.java"
