# 循环调用问题解决方案

## 问题描述

在调用`getRspBodyClass`方法时，可能会出现回调循环的问题。这是因为：

1. `buildTypeFieldMappings()` 调用 `buildTypeMappingForClass()`
2. `buildTypeMappingForClass()` 调用 `isRspBodyRelatedField()`
3. 如果用户调用 `getRspBodyClass()`，它会依赖 `TYPE_FIELD_MAP`
4. 但 `TYPE_FIELD_MAP` 是在 `buildTypeFieldMappings()` 中构建的
5. 如果 `TYPE_FIELD_MAP` 还没有构建完成，就会形成循环

## 解决方案

### 1. 状态标志机制

添加了两个状态标志来避免循环调用：

```java
// 状态标志，避免循环调用
private static volatile boolean isBuildingMappings = false;
private static volatile boolean mappingsBuilt = false;
```

### 2. 安全检查

在`getRspBodyClass`方法中添加了安全检查：

```java
public static Class<?> getRspBodyClass(int command, int type) {
    try {
        // 安全检查：如果正在构建映射，则等待完成
        if (isBuildingMappings) {
            System.out.println("正在构建映射中，请稍后再试");
            return null;
        }
        
        // 如果映射未构建，则提示用户先初始化
        if (!mappingsBuilt && TYPE_FIELD_MAP.isEmpty()) {
            System.err.println("映射未初始化，请先调用 ProtoReflectionMapper.initializeMappings()");
            return null;
        }
        
        // ... 其他逻辑
    }
}
```

### 3. 公共初始化方法

提供了公共的初始化方法，让用户可以手动控制初始化时机：

```java
/**
 * 公共初始化方法，手动初始化所有映射
 * 在调用getRspBodyClass等方法之前，建议先调用此方法
 */
public static void initializeMappings() {
    if (isBuildingMappings) {
        System.out.println("正在构建映射中，请稍后再试");
        return;
    }
    
    System.out.println("开始初始化ProtoReflectionMapper映射...");
    
    // 初始化CommandType枚举映射
    initializeCommandTypeMappings();
    
    // 动态发现和加载Java类
    discoverAndLoadClasses();
    
    // 动态构建type字段映射
    buildTypeFieldMappings();
    
    System.out.println("ProtoReflectionMapper映射初始化完成");
}
```

## 使用方法

### 1. 正确的使用顺序

```java
// 第一步：初始化映射
ProtoReflectionMapper.initializeMappings();

// 第二步：检查是否初始化成功
if (ProtoReflectionMapper.isMappingsInitialized()) {
    // 第三步：使用getRspBodyClass方法
    Class<?> rspBodyClass = ProtoReflectionMapper.getRspBodyClass(3, 5);
    if (rspBodyClass != null) {
        System.out.println("获取到RspBody类: " + rspBodyClass.getName());
    }
} else {
    System.err.println("映射初始化失败");
}
```

### 2. 错误处理

```java
try {
    // 获取RspBody类
    Class<?> rspBodyClass = ProtoReflectionMapper.getRspBodyClass(command, type);
    
    if (rspBodyClass != null) {
        // 成功获取到类
        System.out.println("成功: " + rspBodyClass.getName());
    } else {
        // 检查错误原因
        if (!ProtoReflectionMapper.isMappingsInitialized()) {
            System.err.println("映射未初始化，请先调用 initializeMappings()");
        } else {
            System.err.println("未找到对应的RspBody类");
        }
    }
} catch (Exception e) {
    System.err.println("获取RspBody类时出错: " + e.getMessage());
}
```

### 3. 批量处理

```java
// 批量获取多个RspBody类
public void processMultipleCommands(int[][] commandTypePairs) {
    // 确保映射已初始化
    if (!ProtoReflectionMapper.isMappingsInitialized()) {
        ProtoReflectionMapper.initializeMappings();
    }
    
    for (int[] pair : commandTypePairs) {
        int command = pair[0];
        int type = pair[1];
        
        Class<?> rspClass = ProtoReflectionMapper.getRspBodyClass(command, type);
        if (rspClass != null) {
            System.out.println("command=" + command + ", type=" + type + " -> " + rspClass.getSimpleName());
        }
    }
}
```

## 最佳实践

### 1. 初始化时机

- **应用启动时**: 在应用启动时调用`initializeMappings()`
- **首次使用前**: 在首次使用`getRspBodyClass`前调用
- **配置变更后**: 在proto文件变更后重新调用

### 2. 错误处理

```java
// 总是检查初始化状态
if (!ProtoReflectionMapper.isMappingsInitialized()) {
    try {
        ProtoReflectionMapper.initializeMappings();
    } catch (Exception e) {
        System.err.println("初始化失败: " + e.getMessage());
        return;
    }
}
```

### 3. 性能优化

```java
// 避免重复初始化
private static volatile boolean initialized = false;

public static void ensureInitialized() {
    if (!initialized) {
        synchronized (ProtoReflectionMapper.class) {
            if (!initialized) {
                ProtoReflectionMapper.initializeMappings();
                initialized = true;
            }
        }
    }
}
```

## 故障排除

### 问题1: "正在构建映射中，请稍后再试"

**原因**: 映射正在构建过程中
**解决方案**: 等待构建完成，或检查是否有重复的初始化调用

### 问题2: "映射未初始化，请先调用 ProtoReflectionMapper.initializeMappings()"

**原因**: 映射尚未初始化
**解决方案**: 调用`initializeMappings()`方法

### 问题3: 初始化失败

**原因**: 可能是proto文件问题或类路径问题
**解决方案**: 
1. 检查proto文件是否正确编译
2. 检查类路径是否正确
3. 查看错误日志获取详细信息

## 总结

通过以下机制解决了循环调用问题：

1. **状态标志**: 使用`isBuildingMappings`和`mappingsBuilt`标志
2. **安全检查**: 在调用前检查映射状态
3. **公共接口**: 提供明确的初始化方法
4. **错误提示**: 清晰的错误信息和解决建议

现在您可以安全地使用`getRspBodyClass`方法，而不用担心循环调用问题！
