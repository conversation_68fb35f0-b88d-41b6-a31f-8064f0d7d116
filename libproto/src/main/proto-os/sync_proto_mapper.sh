#!/bin/bash

# ProtoReflectionMapper.java 同步脚本
# 自动将源文件的修改同步到目标目录

# 配置
SOURCE_FILE="ProtoReflectionMapper.java"
SOURCE_DIR="/Users/<USER>/WorkSapce/wejoy1/lib/libproto/src/main/proto-os"
TARGET_DIR="/Users/<USER>/WorkSapce/wejoy1/module/debug-tool/src/debug/java/com/wepie/debug"
TARGET_FILE="$TARGET_DIR/ProtoReflectionMapper.java"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 ProtoReflectionMapper.java 同步脚本${NC}"
echo "=================================="

# 检查源文件是否存在
if [ ! -f "$SOURCE_DIR/$SOURCE_FILE" ]; then
    echo -e "${RED}❌ 源文件不存在: $SOURCE_DIR/$SOURCE_FILE${NC}"
    exit 1
fi

# 检查目标目录是否存在
if [ ! -d "$TARGET_DIR" ]; then
    echo -e "${YELLOW}⚠️  目标目录不存在，正在创建...${NC}"
    mkdir -p "$TARGET_DIR"
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 创建目标目录失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 目标目录创建成功${NC}"
fi

# 检查是否需要同步（忽略包名差异）
if [ -f "$TARGET_FILE" ]; then
    # 比较文件内容（忽略包名行）
    SOURCE_CONTENT=$(grep -v "package com\." "$SOURCE_DIR/$SOURCE_FILE")
    TARGET_CONTENT=$(grep -v "package com\." "$TARGET_FILE")
    
    if [ "$SOURCE_CONTENT" = "$TARGET_CONTENT" ]; then
        echo -e "${GREEN}✅ 文件内容一致（忽略包名），无需同步${NC}"
        exit 0
    else
        echo -e "${YELLOW}🔄 检测到文件差异，开始同步...${NC}"
    fi
else
    echo -e "${YELLOW}🔄 目标文件不存在，开始创建...${NC}"
fi

# 备份原文件（如果存在）
if [ -f "$TARGET_FILE" ]; then
    BACKUP_FILE="$TARGET_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$TARGET_FILE" "$BACKUP_FILE"
    echo -e "${BLUE}📦 已备份原文件: $BACKUP_FILE${NC}"
fi

# 复制文件
echo -e "${BLUE}📋 正在复制文件...${NC}"
cp "$SOURCE_DIR/$SOURCE_FILE" "$TARGET_FILE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 文件复制成功${NC}"
else
    echo -e "${RED}❌ 文件复制失败${NC}"
    exit 1
fi

# 更新包名
echo -e "${BLUE}🔧 正在更新包名...${NC}"
sed -i '' 's/package com\.wepie\.wespy\.net\.tcp\.reflection;/package com.wepie.debug;/' "$TARGET_FILE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 包名更新成功${NC}"
else
    echo -e "${RED}❌ 包名更新失败${NC}"
    exit 1
fi

# 验证同步结果
echo -e "${BLUE}🔍 验证同步结果...${NC}"

# 检查包名是否正确
if grep -q "package com.wepie.debug;" "$TARGET_FILE"; then
    echo -e "${GREEN}✅ 包名更新正确${NC}"
else
    echo -e "${RED}❌ 包名更新失败${NC}"
    exit 1
fi

# 显示同步统计
echo ""
echo -e "${GREEN}🎉 同步完成！${NC}"
echo "=================================="
echo "源文件: $SOURCE_DIR/$SOURCE_FILE"
echo "目标文件: $TARGET_FILE"
echo "同步时间: $(date)"
echo ""

# 显示文件差异（如果有的话）
if [ -f "$BACKUP_FILE" ]; then
    echo -e "${BLUE}📊 显示主要变更:${NC}"
    diff -u "$BACKUP_FILE" "$TARGET_FILE" | head -20
    echo ""
    echo -e "${YELLOW}💡 提示: 完整差异请查看备份文件: $BACKUP_FILE${NC}"
fi
