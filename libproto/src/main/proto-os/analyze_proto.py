#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析proto文件，提取包名、类名和RspBody信息
基于head.proto中的CommandType枚举值建立映射关系
"""

import os
import re
from typing import Dict, List, Tu<PERSON>

def analyze_proto_files(proto_dir: str) -> Dict[str, Dict]:
    """
    分析proto文件，提取关键信息
    
    Args:
        proto_dir: proto文件目录
        
    Returns:
        包含分析结果的字典
    """
    results = {}
    
    # 遍历所有proto文件
    for filename in os.listdir(proto_dir):
        if not filename.endswith('.proto'):
            continue
            
        filepath = os.path.join(proto_dir, filename)
        print(f"分析文件: {filename}")
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取包名
            package_match = re.search(r'package\s+(\w+);', content)
            package_name = package_match.group(1) if package_match else "unknown"
            
            # 提取Java包名
            java_package_match = re.search(r'option\s+java_package\s*=\s*"([^"]+)"', content)
            java_package = java_package_match.group(1) if java_package_match else "unknown"
            
            # 提取Java外部类名
            java_outer_classname_match = re.search(r'option\s+java_outer_classname\s*=\s*"([^"]+)"', content)
            java_outer_classname = java_outer_classname_match.group(1) if java_outer_classname_match else "unknown"
            
            # 查找RspBody定义
            rspbody_matches = re.findall(r'message\s+(\w+RspBody)\s*\{', content)
            
            # 查找enum定义（可能包含type值）
            enum_matches = re.findall(r'enum\s+(\w+)\s*\{', content)
            
            # 查找message定义
            message_matches = re.findall(r'message\s+(\w+)\s*\{', content)
            
            results[filename] = {
                'proto_package': package_name,
                'java_package': java_package,
                'java_outer_classname': java_outer_classname,
                'rspbody_messages': rspbody_matches,
                'enums': enum_matches,
                'messages': message_matches
            }
            
        except Exception as e:
            print(f"分析文件 {filename} 时出错: {e}")
            results[filename] = {'error': str(e)}
    
    return results

def get_command_type_mapping() -> Dict[int, str]:
    """
    基于head.proto中的CommandType枚举建立command映射
    
    Returns:
        command值到服务名的映射
    """
    return {
        0: "COMMOND_UNDEFINED",
        1: "CONNECTOR",
        2: "TMPROOM", 
        3: "GAMEMATCH",
        4: "ICEBALL",
        5: "GROUP",
        6: "VOICEMATCH",
        7: "FIXROOM",
        8: "LANDLORDS",
        9: "SPY",
        10: "MARRY",
        11: "DRAW",
        12: "CP",
        13: "MUSIC",
        14: "AUCTION",
        15: "MUSIC_HUM",
        16: "HWROOM",
        17: "HWMATCH",
        18: "SAYGUESS",
        19: "WEREWOLF",
        20: "TMPWIDGET",
        21: "MAJIANGTW",
        22: "PARTYROOM",
        1001: "PUSH",
        1002: "HWTCP_PUSH",
        10000: "HWChat",
        10001: "HWGift",
        2001: "BigWinner",
        2004: "Video",
        2005: "BALOOT"
    }

def generate_reflection_mapping(results: Dict[str, Dict]) -> str:
    """
    生成反射映射代码，基于CommandType枚举值
    
    Args:
        results: 分析结果
        
    Returns:
        生成的Java代码
    """
    command_mapping = get_command_type_mapping()
    
    java_code = """// 自动生成的反射映射代码
package com.wepie.wespy.net.tcp.reflection;

import java.util.HashMap;
import java.util.Map;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 通过command和type找到对应RspBody的反射工具类
 * 基于head.proto中的CommandType枚举值自动生成
 */
public class RspBodyReflectionMapper {
    
    // command值 -> Java类的映射
    private static final Map<Integer, Class<?>> COMMAND_CLASS_MAP = new HashMap<>();
    
    // command值 -> type字段映射的映射
    private static final Map<Integer, Map<Integer, String>> TYPE_FIELD_MAP = new HashMap<>();
    
    // command值 -> 服务名的映射
    private static final Map<Integer, String> COMMAND_SERVICE_MAP = new HashMap<>();
    
    static {
        initializeMappings();
    }
    
    private static void initializeMappings() {
        // 初始化command到服务名的映射
        initializeCommandServiceMapping();
        
        // 初始化Java类映射
        initializeJavaClassMappings();
        
        // 初始化type字段映射
        initializeTypeFieldMappings();
    }
    
    /**
     * 初始化command到服务名的映射
     */
    private static void initializeCommandServiceMapping() {
"""
    
    # 添加command到服务名的映射
    for command_value, service_name in command_mapping.items():
        java_code += f"""
        COMMAND_SERVICE_MAP.put({command_value}, "{service_name}");"""
    
    java_code += """
    }
    
    /**
     * 初始化Java类映射
     */
    private static void initializeJavaClassMappings() {
        try {
"""
    
    # 为每个proto文件生成映射
    for filename, info in results.items():
        if 'error' in info:
            continue
            
        if info['java_outer_classname'] != 'unknown':
            # 根据文件名找到对应的command值
            command_value = find_command_value_for_file(filename, command_mapping)
            if command_value is not None:
                java_code += f"""
            // {filename} -> {info['java_outer_classname']} (Command: {command_value})
            Class<?> {info['java_outer_classname']}Class = Class.forName("{info['java_package']}.{info['java_outer_classname']}");
            COMMAND_CLASS_MAP.put({command_value}, {info['java_outer_classname']}Class);
            TYPE_FIELD_MAP.put({command_value}, new HashMap<>());
"""
    
    java_code += """
        } catch (ClassNotFoundException e) {
            System.err.println("初始化Java类映射时出错: " + e.getMessage());
        }
    }
    
    /**
     * 初始化type字段映射
     */
    private static void initializeTypeFieldMappings() {
        // 这里需要根据具体的proto定义来完善type映射
        // 示例：为tmproom服务建立type映射
        Map<Integer, String> tmproomTypeMap = TYPE_FIELD_MAP.get(2); // TMPROOM = 2
        if (tmproomTypeMap != null) {
            tmproomTypeMap.put(1, "sync");
            tmproomTypeMap.put(2, "quick_match");
            tmproomTypeMap.put(3, "create");
            tmproomTypeMap.put(4, "enter");
            tmproomTypeMap.put(5, "exit");
            // ... 更多字段映射
        }
        
        // 可以继续为其他服务建立映射...
    }
    
    /**
     * 根据command值获取服务名
     */
    public static String getServiceName(int command) {
        return COMMAND_SERVICE_MAP.get(command);
    }
    
    /**
     * 根据command和type找到对应的RspBody字段
     * 
     * @param command 命令类型（对应CommandType枚举值）
     * @param type 具体类型（对应RspBody中的字段）
     * @param instance 包含RspBody的对象实例
     * @return RspBody字段值，如果未找到返回null
     */
    public static Object getRspBodyField(int command, int type, Object instance) {
        try {
            // 检查command是否支持
            if (!COMMAND_CLASS_MAP.containsKey(command)) {
                return null;
            }
            
            // 获取type对应的字段名
            Map<Integer, String> typeMap = TYPE_FIELD_MAP.get(command);
            if (typeMap == null) {
                return null;
            }
            
            String fieldName = typeMap.get(type);
            if (fieldName == null) {
                return null;
            }
            
            // 通过反射获取字段值
            Class<?> instanceClass = instance.getClass();
            Field field = instanceClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            
            return field.get(instance);
            
        } catch (Exception e) {
            System.err.println("获取RspBody字段时出错: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取所有支持的command值
     */
    public static Set<Integer> getSupportedCommands() {
        return COMMAND_CLASS_MAP.keySet();
    }
    
    /**
     * 获取指定command支持的所有type值
     */
    public static Set<Integer> getSupportedTypes(int command) {
        Map<Integer, String> typeMap = TYPE_FIELD_MAP.get(command);
        return typeMap != null ? typeMap.keySet() : Collections.emptySet();
    }
    
    /**
     * 获取指定command的Java类
     */
    public static Class<?> getCommandClass(int command) {
        return COMMAND_CLASS_MAP.get(command);
    }
    
    /**
     * 打印所有映射信息（用于调试）
     */
    public static void printMappings() {
        System.out.println("=== CommandType映射信息 ===");
        System.out.println("支持的command数量: " + COMMAND_CLASS_MAP.size());
        
        System.out.println("\\nCommand值到服务名的映射:");
        for (Map.Entry<Integer, String> entry : COMMAND_SERVICE_MAP.entrySet()) {
            System.out.println("  " + entry.getKey() + " -> " + entry.getValue());
        }
        
        System.out.println("\\nCommand值到Java类的映射:");
        for (Map.Entry<Integer, Class<?>> entry : COMMAND_CLASS_MAP.entrySet()) {
            System.out.println("  " + entry.getKey() + " -> " + entry.getValue().getName());
        }
    }
}
"""
    
    return java_code

def find_command_value_for_file(filename: str, command_mapping: Dict[int, str]) -> int:
    """
    根据proto文件名找到对应的command值
    
    Args:
        filename: proto文件名
        command_mapping: command映射
        
    Returns:
        command值，如果未找到返回None
    """
    # 文件名到command值的映射
    file_to_command = {
        'connector.proto': 1,      # CONNECTOR = 1
        'tmproom.proto': 2,       # TMPROOM = 2
        'gamematch.proto': 3,     # GAMEMATCH = 3
        'iceball.proto': 4,       # ICEBALL = 4
        'group.proto': 5,         # GROUP = 5
        'voicematch.proto': 6,    # VOICEMATCH = 6
        'fixroom.proto': 7,       # FIXROOM = 7
        'landlords.proto': 8,     # LANDLORDS = 8
        'spy.proto': 9,           # SPY = 9
        'marry_push.proto': 10,   # MARRY = 10
        'proto_draw.proto': 11,   # DRAW = 11
        'proto_cp.proto': 12,     # CP = 12
        'music.proto': 13,        # MUSIC = 13
        'auction.proto': 14,      # AUCTION = 14
        'musichum.proto': 15,     # MUSIC_HUM = 15
        'hwroom.proto': 16,       # HWROOM = 16
        'hwmatch.proto': 17,      # HWMATCH = 17
        'sayguess.proto': 18,     # SAYGUESS = 18
        'tmpwidget.proto': 20,    # TMPWIDGET = 20
        'mj_reqrsp.proto': 21,    # MAJIANGTW = 21
        'partyroom.proto': 22,    # PARTYROOM = 22
        'baloot_reqrsp.proto': 2005, # BALOOT = 2005
    }
    
    return file_to_command.get(filename)

def main():
    """主函数"""
    proto_dir = "."  # 当前目录
    
    print("开始分析proto文件...")
    results = analyze_proto_files(proto_dir)
    
    print("\n=== 分析结果 ===")
    for filename, info in results.items():
        if 'error' in info:
            print(f"{filename}: 错误 - {info['error']}")
            continue
            
        print(f"\n{filename}:")
        print(f"  Proto包名: {info['proto_package']}")
        print(f"  Java包名: {info['java_package']}")
        print(f"  Java外部类名: {info['java_outer_classname']}")
        print(f"  RspBody消息: {', '.join(info['rspbody_messages']) if info['rspbody_messages'] else '无'}")
        print(f"  枚举数量: {len(info['enums'])}")
        print(f"  消息数量: {len(info['messages'])}")
    
    # 生成反射映射代码
    print("\n=== 生成反射映射代码 ===")
    java_code = generate_reflection_mapping(results)
    
    # 保存到文件
    with open('RspBodyReflectionMapper.java', 'w', encoding='utf-8') as f:
        f.write(java_code)
    
    print("反射映射代码已保存到 RspBodyReflectionMapper.java")
    
    # 统计信息
    total_files = len(results)
    files_with_rspbody = sum(1 for info in results.values() if 'rspbody_messages' in info and info['rspbody_messages'])
    total_rspbody_count = sum(len(info.get('rspbody_messages', [])) for info in results.values())
    
    print(f"\n=== 统计信息 ===")
    print(f"总proto文件数: {total_files}")
    print(f"包含RspBody的文件数: {files_with_rspbody}")
    print(f"总RspBody消息数: {total_rspbody_count}")
    
    # 显示CommandType映射
    print(f"\n=== CommandType映射 ===")
    command_mapping = get_command_type_mapping()
    for command_value, service_name in command_mapping.items():
        print(f"  {command_value}: {service_name}")

if __name__ == "__main__":
    main()
