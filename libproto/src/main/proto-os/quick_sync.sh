#!/bin/bash

# 快速同步脚本 - 简化版本
# 使用方法: ./quick_sync.sh

SOURCE="ProtoReflectionMapper.java"
TARGET="/Users/<USER>/WorkSapce/wejoy1/module/debug-tool/src/debug/java/com/wepie/debug/ProtoReflectionMapper.java"

echo "🔄 快速同步 ProtoReflectionMapper.java..."

# 复制文件
cp "$SOURCE" "$TARGET"

# 更新包名
sed -i '' 's/package com\.wepie\.wespy\.net\.tcp\.reflection;/package com.wepie.debug;/' "$TARGET"

echo "✅ 同步完成！"
echo "目标文件: $TARGET"
