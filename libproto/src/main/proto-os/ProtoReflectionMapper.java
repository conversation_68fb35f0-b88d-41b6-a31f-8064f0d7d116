package com.wepie.wespy.net.tcp.reflection;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.Collections;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.io.File;
import java.util.HashSet;
import android.util.Log;

/**
 * Proto文件反射映射工具类
 * 通过反射动态读取class的方式来处理command和type映射
 * 
 * 基于head.proto中的CommandType枚举值：
 * - command是int值，对应CommandType枚举
 * - type是int值，对应RspBody中的字段序号
 */
public class ProtoReflectionMapper {
    
    // 基础包名
    private static final String BASE_PACKAGE = "com.wepie.wespy.net.tcp.packet";
    
    // command值 -> Java类的映射（动态加载）
    private static final Map<Integer, Class<?>> COMMAND_CLASS_MAP = new HashMap<>();
    
    // command值 -> type字段映射的映射（动态构建）
    private static final Map<Integer, Map<Integer, String>> TYPE_FIELD_MAP = new HashMap<>();
    
    // command值 -> 服务名的映射（基于CommandType枚举）
    private static final Map<Integer, String> COMMAND_SERVICE_MAP = new HashMap<>();
    
    // 特殊类名缓存，避免重复扫描
    private static final Map<String, String> SPECIAL_CLASS_NAME_CACHE = new HashMap<>();
    
    // 状态标志，避免循环调用
    private static volatile boolean isBuildingMappings = false;
    private static volatile boolean mappingsBuilt = false;
    private static volatile boolean isInitializing = false; // 新增：延迟初始化标志
    
    static {
        // 静态初始化时调用公共初始化方法
        initializeMappings();
    }
    
    /**
     * 初始化CommandType枚举映射
     * 通过反射遍历HeadPackets.CommandType枚举类动态获取
     */
    private static void initializeCommandTypeMappings() {
        try {
            // 通过反射获取HeadPackets.CommandType枚举类
            Class<?> headPacketsClass = Class.forName(BASE_PACKAGE + ".HeadPackets");
            Class<?> commandTypeEnum = null;
            
            // 查找CommandType内部枚举类
            Class<?>[] declaredClasses = headPacketsClass.getDeclaredClasses();
            for (Class<?> declaredClass : declaredClasses) {
                if (declaredClass.getSimpleName().equals("CommandType")) {
                    commandTypeEnum = declaredClass;
                    break;
                }
            }
            
            if (commandTypeEnum == null) {
                Log.e("ProtoReflectionMapper", "未找到CommandType枚举类，CommandType映射初始化失败");
                return;
            }
            
            // 检查是否是枚举类
            if (!commandTypeEnum.isEnum()) {
                Log.e("ProtoReflectionMapper", "CommandType不是枚举类，CommandType映射初始化失败");
                return;
            }
            
            // 获取枚举常量
            Object[] enumConstants = commandTypeEnum.getEnumConstants();
            if (enumConstants == null) {
                Log.e("ProtoReflectionMapper", "无法获取CommandType枚举常量，CommandType映射初始化失败");
                return;
            }
            

            
            // 遍历枚举常量，获取名称和值
            for (Object enumConstant : enumConstants) {
                try {
                    // 获取枚举常量的名称
                    String enumName = enumConstant.toString();
                    
                    // 获取枚举常量的值（通过反射调用ordinal()方法或获取字段值）
                    int enumValue = getEnumValue(enumConstant);
                    
                    // 将枚举值和服务名添加到映射中
                    COMMAND_SERVICE_MAP.put(enumValue, enumName);
                    

                    
                } catch (Exception e) {
                    Log.e("ProtoReflectionMapper", "处理枚举常量时出错: " + e.getMessage());
                }
            }
            

            
        } catch (ClassNotFoundException e) {
            Log.e("ProtoReflectionMapper", "无法找到HeadPackets类，CommandType映射初始化失败: " + e.getMessage());
        } catch (Exception e) {
            Log.e("ProtoReflectionMapper", "通过反射初始化CommandType映射时出错: " + e.getMessage());
        }
    }
    
    /**
     * 获取枚举常量的值
     */
    private static int getEnumValue(Object enumConstant) {
        try {
            // 方法1: 尝试调用ordinal()方法
            Method ordinalMethod = enumConstant.getClass().getMethod("ordinal");
            if (ordinalMethod != null) {
                return (Integer) ordinalMethod.invoke(enumConstant);
            }
        } catch (Exception e) {
            // 继续尝试其他方法
        }
        
        try {
            // 方法2: 尝试获取value字段（如果枚举有自定义值）
            Field[] fields = enumConstant.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.getType() == int.class && !field.getName().equals("$VALUES")) {
                    field.setAccessible(true);
                    return field.getInt(enumConstant);
                }
            }
        } catch (Exception e) {
            // 继续尝试其他方法
        }
        
        try {
            // 方法3: 尝试获取name()方法返回的字符串，然后解析
            Method nameMethod = enumConstant.getClass().getMethod("name");
            if (nameMethod != null) {
                String name = (String) nameMethod.invoke(enumConstant);
                // 暂时跳过名称解析，使用默认值
            }
        } catch (Exception e) {
            // 继续尝试其他方法
        }
        
        // 如果所有方法都失败，返回默认值
        Log.e("ProtoReflectionMapper", "无法获取枚举常量值，使用默认值0");
        return 0;
    }
    

    

    
    /**
     * 动态发现和加载Java类
     */
    private static void discoverAndLoadClasses() {
        // 尝试加载所有可能的类
        for (Map.Entry<Integer, String> entry : COMMAND_SERVICE_MAP.entrySet()) {
            int command = entry.getKey();
            String serviceName = entry.getValue();
            
            // 跳过一些不需要的command
            if (command == 0 || command == 1001 || command == 1002 || 
                command == 10000 || command == 10001 || command == 2001 || 
                command == 2004) {
                continue;
            }
            
            // 尝试加载对应的Java类
            Class<?> loadedClass = tryLoadClass(command, serviceName);
            if (loadedClass != null) {
                COMMAND_CLASS_MAP.put(command, loadedClass);
                
                // 检查是否包含RspBody
                // if (hasRspBodyInClass(loadedClass)) {
                //     RSPBODY_COMMAND_MAP.put(command, getProtoFileNameByCommand(command));
                // }
            }
        }
    }
    
    /**
     * 尝试加载指定command对应的Java类
     */
    private static Class<?> tryLoadClass(int command, String serviceName) {
        String[] possibleClassNames = generatePossibleClassNames(command, serviceName);
        
        for (String className : possibleClassNames) {
            try {
                String fullClassName = BASE_PACKAGE + "." + className;
                Class<?> clazz = Class.forName(fullClassName);

                return clazz;
            } catch (ClassNotFoundException e) {
                // 类未找到，继续尝试下一个
            } catch (Exception e) {
                Log.e("ProtoReflectionMapper", "加载类时出错: " + className + " - " + e.getMessage());
            }
        }
        

        return null;
    }
    
    /**
     * 根据command值和服务名生成可能的类名
     * 基于服务名称和类名的对应规则
     */
    private static String[] generatePossibleClassNames(int command, String serviceName) {
        // 跳过一些不需要的command
        if (command == 0 || command == 1001 || command == 1002 || 
            command == 10000 || command == 10001 || command == 2001 || 
            command == 2004) {
            return new String[]{};
        }
        
        // 基于服务名称和类名的对应规则生成可能的类名
        // 规则1: 服务名 + "Packets" (最常见)
        // 规则2: "Proto" + 服务名 + "Packets" (对于某些特殊服务)
        // 规则3: 服务名 + "Packet" (单数形式)
        // 规则4: 特殊命名规则 (如TMPROOM -> TmpRoom, GAMEMATCH -> GameMatch)
        
        String[] possibleNames = new String[4];
        int index = 0;
        
        // 规则1: 服务名 + "Packets"
        possibleNames[index++] = serviceName + "Packets";
        
        // 规则2: "Proto" + 服务名 + "Packets" (对于某些特殊服务)
        if (shouldUseProtoPrefix(serviceName)) {
            possibleNames[index++] = "Proto" + serviceName + "Packets";
        }
        
        // 规则3: 服务名 + "Packet" (单数形式)
        possibleNames[index++] = serviceName + "Packet";
        
        // 规则4: 特殊命名规则
        String specialName = getSpecialClassName(serviceName);
        if (specialName != null) {
            possibleNames[index++] = specialName;
        }
        
        // 返回非空的类名数组
        String[] result = new String[index];
        System.arraycopy(possibleNames, 0, result, 0, index);
        return result;
    }
    
    /**
     * 判断是否应该使用"Proto"前缀
     */
    private static boolean shouldUseProtoPrefix(String serviceName) {
        // 某些服务使用"Proto"前缀，基于命名规律
        return serviceName.equals("MARRY") || 
               serviceName.equals("DRAW") || 
               serviceName.equals("CP") || 
               serviceName.equals("AUCTION");
    }
    
    /**
     * 通过扫描包名下的所有类来动态生成特殊类名映射
     * 替代硬编码的switch语句
     */
    private static String getSpecialClassName(String serviceName) {
        // 如果缓存中没有，则动态扫描包
        if (SPECIAL_CLASS_NAME_CACHE.isEmpty()) {
            scanPackageForSpecialClassNames();
        }
        
        return SPECIAL_CLASS_NAME_CACHE.get(serviceName);
    }
    

    
    /**
     * 扫描包名下的所有类，自动发现特殊命名规则
     */
    private static void scanPackageForSpecialClassNames() {
        try {

            
            // 获取包下的所有类
            Set<Class<?>> classes = getClassesInPackage(BASE_PACKAGE);
            
            for (Class<?> clazz : classes) {
                String className = clazz.getSimpleName();
                
                // 分析类名，尝试匹配服务名
                for (Map.Entry<Integer, String> entry : COMMAND_SERVICE_MAP.entrySet()) {
                    String serviceName = entry.getValue();
                    
                    // 如果类名包含服务名（忽略大小写），则建立映射
                    if (isClassNameRelatedToService(className, serviceName)) {
                        SPECIAL_CLASS_NAME_CACHE.put(serviceName, className);

                        break;
                    }
                }
            }
            

            
        } catch (Exception e) {
            Log.e("ProtoReflectionMapper", "扫描包时出错: " + e.getMessage());
            // 扫描失败时，缓存保持为空，后续调用将返回null
        }
    }
    
    /**
     * 判断类名是否与服务名相关
     */
    private static boolean isClassNameRelatedToService(String className, String serviceName) {
        // 跳过一些通用的类名
        if (className.equals("HeadPackets") || 
            className.equals("CommonPackets") ||
            className.equals("PushPackets")) {
            return false;
        }
        
        // 检查类名是否包含服务名（忽略大小写）
        String lowerClassName = className.toLowerCase();
        String lowerServiceName = serviceName.toLowerCase();
        
        // 直接包含
        if (lowerClassName.contains(lowerServiceName)) {
            return true;
        }
        
        // 处理缩写展开的情况
        if (isAbbreviationExpansion(className, serviceName)) {
            return true;
        }
        
        // 处理特殊命名规则
        if (isSpecialNamingPattern(className, serviceName)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否是缩写展开的情况
     * 例如：TMPROOM -> TmpRoomPackets
     */
    private static boolean isAbbreviationExpansion(String className, String serviceName) {
        // 常见的缩写展开规则
        Map<String, String> abbreviationRules = new HashMap<>();
        abbreviationRules.put("TMPROOM", "TmpRoom");
        abbreviationRules.put("GAMEMATCH", "GameMatch");
        abbreviationRules.put("HWROOM", "HWRoom");
        abbreviationRules.put("HWMATCH", "HWMatch");
        abbreviationRules.put("TMPWIDGET", "TmpWidget");
        abbreviationRules.put("MAJIANGTW", "MJ");
        abbreviationRules.put("MUSICHUM", "MusicHum");
        
        String expectedPrefix = abbreviationRules.get(serviceName);
        if (expectedPrefix != null && className.startsWith(expectedPrefix)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查特殊命名模式
     * 例如：BALOOT -> BalootPacketsReqRsp
     */
    private static boolean isSpecialNamingPattern(String className, String serviceName) {
        // 特殊命名模式
        if (serviceName.equals("BALOOT") && className.contains("Baloot") && className.contains("ReqRsp")) {
            return true;
        }
        
        if (serviceName.equals("MAJIANGTW") && className.contains("MJ") && className.contains("ReqRsp")) {
            return true;
        }
        
        if (serviceName.equals("LANDLORDS") && className.contains("Landlords")) {
            return true;
        }
        
        if (serviceName.equals("VOICEMATCH") && className.contains("VoiceMatch")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取指定包名下的所有类
     */
    private static Set<Class<?>> getClassesInPackage(String packageName) {
        Set<Class<?>> classes = new HashSet<>();
        
        try {
            // 方法1: 使用ClassLoader获取包下的类
            ClassLoader classLoader = ProtoReflectionMapper.class.getClassLoader();
            String path = packageName.replace('.', '/');
            
            // 尝试从classpath中获取资源
            java.net.URL url = classLoader.getResource(path);
            if (url != null && "file".equals(url.getProtocol())) {
                File directory = new File(url.getFile());
                if (directory.exists()) {
                    scanDirectoryForClasses(directory, packageName, classes);
                }
            }
            
            // 方法2: 如果方法1失败，尝试使用反射获取已知的类
            if (classes.isEmpty()) {
                scanKnownClassesInPackage(packageName, classes);
            }
            
        } catch (Exception e) {
            Log.e("ProtoReflectionMapper", "获取包下类时出错: " + e.getMessage());
        }
        
        return classes;
    }
    
    /**
     * 扫描目录中的类文件
     */
    private static void scanDirectoryForClasses(File directory, String packageName, Set<Class<?>> classes) {
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 递归扫描子目录
                    String subPackage = packageName + "." + file.getName();
                    scanDirectoryForClasses(file, subPackage, classes);
                } else if (file.getName().endsWith(".class")) {
                    // 尝试加载类
                    String className = file.getName().substring(0, file.getName().length() - 6);
                    String fullClassName = packageName + "." + className;
                    
                    try {
                        Class<?> clazz = Class.forName(fullClassName);
                        classes.add(clazz);
                    } catch (ClassNotFoundException e) {
                        // 忽略无法加载的类
                    }
                }
            }
        }
    }
    
    /**
     * 扫描包中已知的类（备用方案）
     * 注意：此方法在初始化过程中调用，不能调用可能触发循环的方法
     */
    private static void scanKnownClassesInPackage(String packageName, Set<Class<?>> classes) {
        // 基于现有的COMMAND_SERVICE_MAP，尝试加载已知的类
        // 注意：这里不能调用generatePossibleClassNames，避免循环调用
        for (Map.Entry<Integer, String> entry : COMMAND_SERVICE_MAP.entrySet()) {
            String serviceName = entry.getValue();
            
            // 使用简单的命名规则，避免调用generatePossibleClassNames
            String[] possibleClassNames = generateSimpleClassNames(serviceName);
            
            for (String className : possibleClassNames) {
                try {
                    String fullClassName = packageName + "." + className;
                    Class<?> clazz = Class.forName(fullClassName);
                    classes.add(clazz);
                } catch (ClassNotFoundException e) {
                    // 忽略无法加载的类
                }
            }
        }
    }
    
    /**
     * 生成简单的类名（不调用getSpecialClassName，避免循环）
     */
    private static String[] generateSimpleClassNames(String serviceName) {
        String[] possibleNames = new String[4];
        int index = 0;
        
        // 规则1: 服务名 + "Packets"
        possibleNames[index++] = serviceName + "Packets";
        
        // 规则2: 服务名 + "Packet" (单数形式)
        possibleNames[index++] = serviceName + "Packet";
        
        // 规则3: 处理缩写展开（硬编码，避免循环）
        String expandedName = getExpandedServiceName(serviceName);
        if (expandedName != null) {
            possibleNames[index++] = expandedName + "Packets";
        }
        
        // 规则4: 某些服务使用"Proto"前缀
        if (shouldUseProtoPrefix(serviceName)) {
            possibleNames[index++] = "Proto" + serviceName + "Packets";
        }
        
        // 返回非空的类名数组
        String[] result = new String[index];
        System.arraycopy(possibleNames, 0, result, 0, index);
        return result;
    }
    
    /**
     * 获取展开的服务名（硬编码，避免循环调用）
     */
    private static String getExpandedServiceName(String serviceName) {
        // 硬编码的缩写展开规则，避免循环调用
        switch (serviceName) {
            case "TMPROOM": return "TmpRoom";
            case "GAMEMATCH": return "GameMatch";
            case "HWROOM": return "HWRoom";
            case "HWMATCH": return "HWMatch";
            case "TMPWIDGET": return "TmpWidget";
            case "MAJIANGTW": return "MJ";
            case "MUSICHUM": return "MusicHum";
            default: return null;
        }
    }
    

    
    /**
     * 动态构建type字段映射
     */

    private static void buildTypeFieldMappings() {
        // 设置构建标志，避免循环调用
        if (isBuildingMappings) {
            Log.e("ProtoReflectionMapper", "正在构建映射中，跳过重复调用");
            return;
        }
        
        isBuildingMappings = true;
        
        try {
            for (Map.Entry<Integer, Class<?>> entry : COMMAND_CLASS_MAP.entrySet()) {
                int command = entry.getKey();
                Class<?> clazz = entry.getValue();
                
                Map<Integer, String> typeMap = new HashMap<>();
                TYPE_FIELD_MAP.put(command, typeMap);
                
                // 通过反射分析类结构，构建type映射
                buildTypeMappingForClass(clazz, typeMap);
            }
            
            mappingsBuilt = true;
            Log.e("ProtoReflectionMapper", "Type字段映射构建完成");
            
        } finally {
            isBuildingMappings = false;
        }
    }
    
    /**
     * 为指定类构建type字段映射
     */
    private static void buildTypeMappingForClass(Class<?> clazz, Map<Integer, String> typeMap) {
        try {
            // 查找RspBody相关的字段
            Field[] fields = clazz.getDeclaredFields();
            int typeIndex = 1;
            
            for (Field field : fields) {
                // 跳过静态字段和私有字段
                if (Modifier.isStatic(field.getModifiers()) || Modifier.isPrivate(field.getModifiers())) {
                    continue;
                }
                
                String fieldName = field.getName();
                Class<?> fieldType = field.getType();
                
                // 检查是否是RspBody相关字段
                if (isRspBodyRelatedField(fieldType, fieldName)) {
                    typeMap.put(typeIndex++, fieldName);
    
                }
            }
            
        } catch (Exception e) {
            Log.e("ProtoReflectionMapper", "构建type映射时出错: " + e.getMessage());
        }
    }
    
    /**
     * 检查字段是否与RspBody相关
     */
    private static boolean isRspBodyRelatedField(Class<?> fieldType, String fieldName) {
        String typeName = fieldType.getSimpleName();
        
        // 检查字段类型名是否包含RspBody
        if (typeName.endsWith("RspBody") || typeName.endsWith("Rsp")) {
            return true;
        }
        
        // 检查字段名是否包含RspBody相关关键词
        if (fieldName.toLowerCase().contains("rsp") || 
            fieldName.toLowerCase().contains("response") ||
            fieldName.toLowerCase().contains("result")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 延迟初始化检查，确保映射已初始化
     */
    private static void ensureInitialized() {
        if (!mappingsBuilt && !isInitializing && !isBuildingMappings) {
            Log.e("ProtoReflectionMapper", "检测到映射未初始化，自动进行初始化...");
            initializeMappings();
        }
    }
    
    /**
     * 通过command和type获取对应的RspBody类
     * 例如：command=3 (GAMEMATCH), type=5 获取 SyncTeamRsp
     * 
     * @param command 命令值
     * @param type 类型值（字段序号）
     * @return RspBody类，如果未找到返回null
     */
    public static Class<?> getRspBodyClass(int command, int type) {
        try {
            // 延迟初始化检查
            ensureInitialized();
            
            // 安全检查：如果正在构建映射，则等待完成
            if (isBuildingMappings || isInitializing) {
                Log.e("ProtoReflectionMapper", "正在初始化中，请稍后再试");
                return null;
            }
            
            // 如果映射未构建，则提示用户先初始化
            if (!mappingsBuilt && TYPE_FIELD_MAP.isEmpty()) {
                Log.e("ProtoReflectionMapper", "映射初始化失败，请检查错误日志");
                return null;
            }
            
            // 获取command对应的Java类
            Class<?> commandClass = COMMAND_CLASS_MAP.get(command);
            if (commandClass == null) {
                Log.e("ProtoReflectionMapper", "未找到command " + command + " 对应的Java类");
                return null;
            }
            
            // 获取type对应的字段名
            Map<Integer, String> typeMap = TYPE_FIELD_MAP.get(command);
            if (typeMap == null) {
                Log.e("ProtoReflectionMapper", "未找到command " + command + " 对应的type映射");
                return null;
            }
            
            String fieldName = typeMap.get(type);
            if (fieldName == null) {
                Log.e("ProtoReflectionMapper", "未找到command " + command + ", type " + type + " 对应的字段");
                return null;
            }
            
            // 通过反射获取字段类型
            Field field = commandClass.getDeclaredField(fieldName);
            if (field == null) {
                Log.e("ProtoReflectionMapper", "未找到字段: " + fieldName);
                return null;
            }
            
            Class<?> fieldType = field.getType();
            
            // 检查是否是RspBody类型
            if (isRspBodyClass(fieldType)) {
                return fieldType;
            } else {
                Log.e("ProtoReflectionMapper", "字段 " + fieldName + " 不是RspBody类型: " + fieldType.getName());
                return null;
            }
            
        } catch (Exception e) {
            Log.e("ProtoReflectionMapper", "获取RspBody类时出错: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查类是否是RspBody类型
     */
    private static boolean isRspBodyClass(Class<?> clazz) {
        if (clazz == null) {
            return false;
        }
        
        String className = clazz.getSimpleName();
        
        // 检查类名是否以"Rsp"结尾
        if (className.endsWith("Rsp")) {
            return true;
        }
        
        // 检查类名是否包含"RspBody"
        if (className.contains("RspBody")) {
            return true;
        }
        
        // 检查类名是否以"Response"结尾
        if (className.endsWith("Response")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取RspBody类的完整类名
     * 例如：com.wepie.wespy.net.tcp.packet.GameMatchPackets.SyncTeamRsp
     */
    public static String getRspBodyClassName(int command, int type) {
        Class<?> rspBodyClass = getRspBodyClass(command, type);
        if (rspBodyClass != null) {
            return rspBodyClass.getName();
        }
        return null;
    }
    
    /**
     * 获取RspBody类的简单类名
     * 例如：SyncTeamRsp
     */
    public static String getRspBodySimpleClassName(int command, int type) {
        Class<?> rspBodyClass = getRspBodyClass(command, type);
        if (rspBodyClass != null) {
            return rspBodyClass.getSimpleName();
        }
        return null;
    }
    
    /**
     * 获取RspBody类的包名
     * 例如：com.wepie.wespy.net.tcp.packet.GameMatchPackets
     */
    public static String getRspBodyPackageName(int command, int type) {
        Class<?> rspBodyClass = getRspBodyClass(command, type);
        if (rspBodyClass != null) {
            Package pkg = rspBodyClass.getPackage();
            return pkg != null ? pkg.getName() : "";
        }
        return null;
    }
    
    /**
     * 根据command和type找到对应的RspBody字段
     * 
     * @param command 命令类型（对应CommandType枚举值）
     * @param type 具体类型（对应RspBody中的字段）
     * @param instance 包含RspBody的对象实例
     * @return RspBody字段值，如果未找到返回null
     */
    public static Object getRspBodyField(int command, int type, Object instance) {
        try {
            // 检查command是否支持
            if (!COMMAND_CLASS_MAP.containsKey(command)) {
                return null;
            }
            
            // 获取type对应的字段名
            Map<Integer, String> typeMap = TYPE_FIELD_MAP.get(command);
            if (typeMap == null) {
                return null;
            }
            
            String fieldName = typeMap.get(type);
            if (fieldName == null) {
                return null;
            }
            
            // 通过反射获取字段值
            Class<?> instanceClass = instance.getClass();
            Field field = instanceClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            
            return field.get(instance);
            
        } catch (Exception e) {
            Log.e("ProtoReflectionMapper", "获取RspBody字段时出错: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 根据command值获取服务名
     */
    public static String getServiceName(int command) {
        return COMMAND_SERVICE_MAP.get(command);
    }
    
    /**
     * 检查指定的command是否包含RspBody
     * 注意：此方法已废弃，始终返回false
     */
    @Deprecated
    public static boolean hasRspBody(int command) {
        return false;
    }
    
    /**
     * 获取指定command对应的proto文件名
     * 注意：此方法已废弃，始终返回"unknown.proto"
     */
    @Deprecated
    public static String getProtoFileName(int command) {
        return "unknown.proto";
    }
    
    /**
     * 获取所有支持的command值
     */
    public static Set<Integer> getSupportedCommands() {
        return Collections.unmodifiableSet(COMMAND_CLASS_MAP.keySet());
    }
    
    /**
     * 获取包含RspBody的command值
     * 注意：此方法已废弃，始终返回空集合
     */
    @Deprecated
    public static Set<Integer> getRspBodyCommands() {
        return Collections.emptySet();
    }
    
    /**
     * 获取指定command支持的所有type值
     */
    public static Set<Integer> getSupportedTypes(int command) {
        Map<Integer, String> typeMap = TYPE_FIELD_MAP.get(command);
        return typeMap != null ? Collections.unmodifiableSet(typeMap.keySet()) : Collections.emptySet();
    }
    
    /**
     * 获取指定command的Java类
     */
    public static Class<?> getCommandClass(int command) {
        return COMMAND_CLASS_MAP.get(command);
    }
    
    /**
     * 动态重新加载所有映射（用于运行时更新）
     */
    public static void reloadMappings() {
        COMMAND_CLASS_MAP.clear();
        TYPE_FIELD_MAP.clear();
        // RSPBODY_COMMAND_MAP.clear(); // 已移除RSPBODY_COMMAND_MAP
        
        discoverAndLoadClasses();
        buildTypeFieldMappings();
    }
    
    /**
     * 打印所有映射信息（用于调试）
     */
    public static void printMappings() {
        Log.e("ProtoReflectionMapper", "=== 动态反射映射信息 ===");
        Log.e("ProtoReflectionMapper", "支持的command数量: " + COMMAND_CLASS_MAP.size());
        Log.e("ProtoReflectionMapper", "包含RspBody的command数量: 0 (已移除RSPBODY_COMMAND_MAP)");
        
        Log.e("ProtoReflectionMapper", "\nCommand值到服务名的映射:");
        for (Map.Entry<Integer, String> entry : COMMAND_SERVICE_MAP.entrySet()) {
            Log.e("ProtoReflectionMapper", "  " + entry.getKey() + " -> " + entry.getValue());
        }
        
        Log.e("ProtoReflectionMapper", "\n包含RspBody的command:");
        Log.e("ProtoReflectionMapper", "  无 (已移除RSPBODY_COMMAND_MAP)");
        
        Log.e("ProtoReflectionMapper", "\nCommand值到Java类的映射:");
        for (Map.Entry<Integer, Class<?>> entry : COMMAND_CLASS_MAP.entrySet()) {
            Log.e("ProtoReflectionMapper", "  " + entry.getKey() + " -> " + entry.getValue().getName());
        }
        
        Log.e("ProtoReflectionMapper", "\nType字段映射:");
        for (Map.Entry<Integer, Map<Integer, String>> entry : TYPE_FIELD_MAP.entrySet()) {
            int command = entry.getKey();
            Map<Integer, String> typeMap = entry.getValue();
            Log.e("ProtoReflectionMapper", "  Command " + command + " (" + getServiceName(command) + "):");
            for (Map.Entry<Integer, String> typeEntry : typeMap.entrySet()) {
                Log.e("ProtoReflectionMapper", "    Type " + typeEntry.getKey() + " -> " + typeEntry.getValue());
            }
        }
    }
    
    /**
     * 公共初始化方法，手动初始化所有映射
     * 在调用getRspBodyClass等方法之前，建议先调用此方法
     */
    public static void initializeMappings() {
        if (isBuildingMappings) {
            Log.e("ProtoReflectionMapper", "正在构建映射中，请稍后再试");
            return;
        }
        
        Log.e("ProtoReflectionMapper", "开始初始化ProtoReflectionMapper映射...");
        
        // 设置初始化标志，避免重复调用
        isInitializing = true;
        
        try {
            // 初始化CommandType枚举映射
            initializeCommandTypeMappings();
            
            // 动态发现和加载Java类
            discoverAndLoadClasses();
            
            // 动态构建type字段映射
            buildTypeFieldMappings();
            
            mappingsBuilt = true; // 确保映射已构建
        } finally {
            isInitializing = false; // 重置初始化标志
        }
        
        Log.e("ProtoReflectionMapper", "ProtoReflectionMapper映射初始化完成");
    }
    
    /**
     * 检查映射是否已初始化
     */
    public static boolean isMappingsInitialized() {
        return mappingsBuilt && !TYPE_FIELD_MAP.isEmpty();
    }
}
