<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="AppTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="action_bar_drawable">@color/white</item>
        <item name="colorPrimary">@color/color_accent</item>
        <item name="colorPrimaryDark">@color/color_accent_grey</item>
        <item name="colorPrimaryVariant">@color/white</item>
        <item name="colorAccent">@color/color_accent</item>
        <item name="colorOnSurface">@color/color_accent</item>
        <item name="android:statusBarColor" tools:targetApi="l">@color/white</item>
        <item name="android:windowLightStatusBar" tools:targetApi="M">true</item>
        <item name="buttonStyle">@style/Widget.BtnStyle</item>
        <item name="materialButtonStyle">@style/Widget.BtnStyle</item>

        <!-- md 组件 不指定背景时的默认背景色 -->
        <item name="colorSurface">@color/white</item>
        <!--        标题颜色受影响-->
        <item name="android:textColorPrimary">@color/text_color_dark</item>

        <item name="android:textViewStyle">@style/TextViewStyle</item>
        <item name="android:editTextStyle">@style/EditTextStyle</item>

    </style>
    <style name="WodiStartStyle" parent="@style/AppTheme">
        <item name="android:windowBackground">@drawable/start_window_bg</item>
        <item name="android:navigationBarColor">#FFF36A</item>
    </style>
</resources>
