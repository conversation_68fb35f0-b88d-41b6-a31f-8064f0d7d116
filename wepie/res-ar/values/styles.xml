<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="action_bar_drawable">@color/white</item>
        <item name="colorPrimary">@color/color_accent</item>
        <item name="colorPrimaryDark">@color/color_accent_grey</item>
        <item name="colorPrimaryVariant">@color/white</item>
        <item name="colorAccent">@color/color_accent</item>
        <item name="colorOnSurface">@color/color_accent</item>
        <item name="android:statusBarColor">@color/white</item>
        <item name="android:windowLightStatusBar" tools:targetApi="M">true</item>
        <item name="android:navigationBarColor">@color/white</item>
        <item name="buttonStyle">@style/Widget.BtnStyle</item>
        <item name="materialButtonStyle">@style/Widget.BtnStyle</item>

        <!-- md 组件 不指定背景时的默认背景色 -->
        <item name="colorSurface">@color/white</item>
        <!--        标题颜色受影响-->
        <item name="android:textColorPrimary">@color/text_color_dark</item>
        <item name="snackbarStyle">@style/WeplaySnackBar</item>

        <item name="android:textViewStyle">@style/TextViewStyle</item>
        <item name="android:editTextStyle">@style/EditTextStyle</item>
        <item name="android:fontFamily">@font/tajawal</item>
        <item name="fontFamily">@font/tajawal</item>
        <item name="boldFontFamily">@font/tajawal_bold</item>
        <item name="baseui_font_regular">@font/tajawal</item>
        <item name="baseui_font_regular_italic">@font/tajawal</item>
        <item name="baseui_font_bold">@font/tajawal_bold</item>
        <item name="baseui_font_bold_italic">@font/tajawal_bold</item>
    </style>

    <style name="ImagePickerTheme" parent="@style/AppTheme">
        <item name="android:fitsSystemWindows">true</item>
    </style>

    <style name="ImagePickerThemeFullScreen" parent="ImagePickerTheme">
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <style name="LittleGameStyle" parent="@style/AppTheme">
        <item name="colorPrimary">@color/color_primary</item>
        <item name="colorPrimaryDark">@color/color_primary_dark</item>
        <item name="colorAccent">@color/color_accent</item>
    </style>

    <style name="WodiStartStyle" parent="@style/AppTheme">
        <item name="android:windowBackground">@drawable/start_window_bg</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">true</item>
        <item name="android:navigationBarColor">#FFFFFF</item>
    </style>

    <style name="AppTheme.Translucent" parent="@style/AppTheme">
        <item name="android:background">@color/translucent</item>
        <item name="android:windowBackground">@color/translucent</item>
    </style>

    <style name="AppTheme.Cocos" parent="@style/AppTheme">
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:navigationBarColor">@color/translucent</item>
    </style>

    <!-- 解决小游戏匹配界面进入到Unity游戏加载页面，匹配Activity finish()的时候加载页面会闪一下的问题 -->
    <style name="AppTheme.Cocos.NoAnim" parent="AppTheme.Cocos">
        <item name="android:windowAnimationStyle">@style/Animation</item>
    </style>

    <style name="Animation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>
    <!-- 解决小游戏匹配界面进入到Unity游戏加载页面，匹配Activity finish()的时候加载页面会闪一下的问题 -->

    <style name="CustomToast">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">16dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">@font/tajawal</item>
    </style>

    <style name="me_item_arrow">
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_marginEnd">16dp</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:scaleX">@integer/image_scale_x</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="srcCompat">@drawable/forward_icon_new</item>
    </style>
    <style name="create_voice_room_arrow">
        <item name="android:layout_width">14dp</item>
        <item name="android:layout_height">14dp</item>
        <item name="android:scaleX">@integer/image_scale_x</item>
        <item name="srcCompat">@drawable/forward_icon_new</item>
        <item name="tint">#1B1D38</item>
    </style>
    <style name="voice_room_popular_title_arrow" parent="right_arrow_grey" />
</resources>
