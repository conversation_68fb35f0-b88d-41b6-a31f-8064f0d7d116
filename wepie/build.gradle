import com.wepie.wejoy.integrateCocos.PicCheckScope

plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'androidx.baselineprofile'
    id 'com.wejoy.wepie.IntegrateCocosResAppPlugin'
}

def taskReqName = getGradle().getStartParameter().getTaskRequests().toString()
def isHuawei = taskReqName.contains("Huawei") || taskReqName.contains("huawei")

def ndk_abi_filters = new String[]{'armeabi-v7a', 'arm64-v8a'}
if (BUILD_MIN_PACKAGE.toBoolean() || isHuawei) {
    ndk_abi_filters = new String[]{'arm64-v8a'}
}

android {
    namespace "com.wepie.wespy"
    buildFeatures {
        viewBinding true
        buildConfig true
        renderScript true
    }
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        applicationId rootProject.ext.packageName
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.appConfig.VERSION_CODE
        versionName rootProject.ext.appConfig.VERSION_NAME

        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true

        ndk {
            abiFilters ndk_abi_filters
        }

        manifestPlaceholders = rootProject.ext.appConfig

//        javaCompileOptions {
//            annotationProcessorOptions {
//                arguments = [eventBusIndex: 'com.wepie.wespy.MyEventBusIndex']
//            }
//        }

        resConfigs appConfig.RES_CONFIG

        if (IS_DEBUG_COMPILE.toBoolean() && !COMPILE_DEBUG_SETTINGS.toBoolean()) {
            buildConfigField "String", "BUILD_TIME", "\"${getDate()}\""
        } else {
            buildConfigField "String", "BUILD_TIME", "\"${getBuildTime()}\""
        }
        buildConfigField "String", "DEFAULT_TRACK_REGION", "\"${rootProject.ext.appConfig.DEFAULT_TRACK_REGION}\""
        buildConfigField "String", "FLASH_LOGIN_ID", "\"${rootProject.ext.appConfig.FLASH_LOGIN_ID}\""
        buildConfigField "String", "FLASH_LOGIN_KEY", "\"${rootProject.ext.appConfig.FLASH_LOGIN_KEY}\""
        buildConfigField "String", "BASE_FILE_DIR", "\"${rootProject.ext.appConfig.BASE_FILE_DIR}\""
        buildConfigField "String", "PHOTO_FILE_DIR", "\"${rootProject.ext.appConfig.PHOTO_FILE_DIR}\""
        buildConfigField "String", "PKG_NAME", "\"${rootProject.ext.appConfig.PKG_NAME}\""
        buildConfigField "String", "COMMON_VERSION_NAME", "\"${rootProject.ext.appConfig.COMMON_VERSION_NAME}\""
        buildConfigField "int", "COMMON_VERSION_CODE", "${rootProject.ext.appConfig.COMMON_VERSION_CODE}"
        buildConfigField "String", "PUSH_CHANNEL", "\"${rootProject.ext.appConfig.PUSH_CHANNEL}\""
        buildConfigField "String", "PROD_DOMAIN", "\"${rootProject.ext.appConfig.PROD_DOMAIN}\""
        buildConfigField "String", "DEBUG_DOMAIN", "\"${rootProject.ext.appConfig.DEBUG_DOMAIN}\""
        buildConfigField "String[]", "LANGUAGE_ORDER", "${rootProject.ext.appConfig.LANGUAGE_ORDER}"
    }

    signingConfigs {
        releaseKeyStore {
            storeFile file("../keystores/${rootProject.ext.appConfig.KEY_STORE}")
            storePassword "87542701"
            keyAlias "android"
            keyPassword "87542701"
        }
    }
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.releaseKeyStore
            // Add this extension
            firebaseCrashlytics {
                // Enable processing and uploading of native symbols to Crashlytics servers.
                // By default, this is disabled to improve build speeds.
                // This flag must be enabled to see properly-symbolicated native
                // stack traces in the Crashlytics dashboard.
                nativeSymbolUploadEnabled true
                mappingFileUploadEnabled true
                unstrippedNativeLibsDir 'wejoy'
            }
            ndk {
                debugSymbolLevel = 'FULL'
            }
        }
        debug {
            minifyEnabled rootProject.ext.android.enableMinifyForDebug
            shrinkResources rootProject.ext.android.enableMinifyForDebug
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules-debug.pro'
            signingConfig signingConfigs.releaseKeyStore
            crunchPngs false
            // Add this extension
            firebaseCrashlytics {
                // Enable processing and uploading of native symbols to Crashlytics servers.
                // By default, this is disabled to improve build speeds.
                // This flag must be enabled to see properly-symbolicated native
                // stack traces in the Crashlytics dashboard.
                nativeSymbolUploadEnabled rootProject.ext.android.enableMinifyForDebug
                mappingFileUploadEnabled rootProject.ext.android.enableMinifyForDebug
                unstrippedNativeLibsDir 'wejoy'
            }
//            resValue "string", "app_name", rootProject.ext.appConfig.APP_NAME
        }
    }

    flavorDimensions "channel"

    productFlavors {
        official {
            dimension "channel"
            setDefault(true)
        }
        huawei {
            dimension "channel"
        }
    }

    android.applicationVariants.configureEach { variant ->
        if (IS_DEBUG_COMPILE.toBoolean() && !COMPILE_DEBUG_SETTINGS.toBoolean()) {
            return
        }
        variant.outputs.configureEach {
            outputFileName = outputFileName.replace(".apk", "_" + android.defaultConfig.versionName + "_" + android.defaultConfig.versionCode + "_" + getLocalBuildTime() + ".apk")
        }

        outProjectInfo()
    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            java.srcDirs = appConfig.SRC_DIR
            resources.srcDirs = appConfig.SRC_DIR
            aidl.srcDirs = ['src']
            renderscript.srcDirs = ['src']
            res.srcDirs = appConfig.RES_DIR
            assets.srcDirs = appConfig.ASSETS_DIR
            jniLibs.srcDirs = ['libs']
        }

        debug.setRoot('build-types/debug')
        release.setRoot('build-types/release')
    }

    compileOptions {
        sourceCompatibility rootProject.ext.java_version
        targetCompatibility rootProject.ext.java_version
    }

    kotlinOptions {
        jvmTarget = rootProject.ext.java_version.toString()
    }

    configurations {
        implementation.exclude module: 'protolite-well-known-types'
    }

    bundle {
        language {
            // This is disabled so that the App Bundle does NOT split the APK for each language.
            // We're gonna use the same APK for all languages.
            enableSplit true
        }
    }

//    if (USE_PAD.toBoolean()) {
//        assetPacks = [":wepie_asset_pack"]
//    }
    packaging {
        dex {
            useLegacyPackaging = true
        }
        jniLibs {
            useLegacyPackaging true
            pickFirsts += ['lib/arm64-v8a/libc++_shared.so', 'lib/armeabi-v7a/libc++_shared.so',
                           'lib/x86_64/libc++_shared.so', 'lib/x86/libc++_shared.so',
                           "lib/armeabi-v7a/libZegoExpressEngine.so", 'lib/arm64-v8a/libZegoExpressEngine.so',
                           "lib/armeabi-v7a/libwebp.so", 'lib/arm64-v8a/libwebp.so',
                           "lib/armeabi-v7a/libwebpdecoder.so", 'lib/arm64-v8a/libwebpdecoder.so',
                           "lib/armeabi-v7a/libwebpencoder.so", 'lib/arm64-v8a/libwebpencoder.so',
                           "lib/armeabi-v7a/libwebpdemux.so", 'lib/arm64-v8a/libwebpdemux.so',
                           "lib/armeabi-v7a/libwebpmux.so", 'lib/arm64-v8a/libwebpmux.so',
            ]
        }
        resources {
            excludes += ['META-INF/shark.kotlin_module', '**/*.proto']
        }
    }
    androidResources {
        noCompress '.unity3d', '.ress', '.resource', '.obb'
    }
    lint {
        abortOnError false
        checkReleaseBuilds false
        checkDependencies true
        lintConfig rootProject.file("lint/lint.xml")
    }
    if (BUILD_FROM_IDE || !isHuawei) {
        dynamicFeatures = [':super_user', ':dy_test']
    } else {
        dynamicFeatures = [':dy_test']
    }
    ndkVersion rootProject.ext.android.ndkVersion
}

configurations.configureEach {
    resolutionStrategy {
        force(libs.recyclerview, libs.androidx.appcompat,
                libs.installreferrer,
                libs.okhttp,
                libs.okio,
                libs.androidx.lifecycle.runtimektx,
                libs.kotlin.core,
                libs.kotlinx.coroutines.android,
                libs.androidx.collection,
                libs.androidx.core.runtime)
    }
}

baselineProfile {
//    automaticGenerationDuringBuild = true
    dexLayoutOptimization = true
}

IntegrateCocos {
    //jackaroo
    gameTypes = [1041]
    cocosZipInfoBuildConfigKey = "CocoResInAppZipInfo"
    //匹配图片资源需要支持的语言
    languages = ["ar", "en", "hi"]
    //是否启用图片压缩大小检查功能
    enablePicSizeCheck = !rootProject.ext.IS_DEBUG_COMPILE
    saveEmbedResultToLark = !rootProject.ext.IS_DEBUG_COMPILE
    //启用检查时 触发报警的阈值
    alertThreshold = 0.5f
    enableCache = rootProject.ext.IS_DEBUG_COMPILE
    alertSizeThreshold = 1 * 1024
    //图片检查的资源范围
    picCheckScope = PicCheckScope.ALL
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(':lib:api')
    implementation project(':lib:api-plugin:invite-api')
    implementation project(":lib:api-plugin:add-friend")
    implementation project(":lib:api-plugin:location")
    implementation project(":lib:api-plugin:uploader")
//    implementation project(":lib:api-plugin:hwroom-api")
    implementation project(":lib:api-plugin:voice-api")
    implementation project(":lib:api-plugin:avatar")

    implementation project(':lib:floating')
    implementation project(":lib:luban")
//    implementation project(':lib:libflutter')
    implementation project(':lib:store')
    implementation project(':lib:libtcp')
    implementation project(':lib:liblog')
    implementation project(':lib:helper')
    implementation project(':lib:libdialog')
//    implementation project(':lib:base-ui')
    // 3.2.1拍照可能卡住，先用3.2.0
    //添加 Sensors Analytics SDK 依赖
    implementation project(':lib:libthirdparty-fbline')
    implementation libs.skynet.apm
    implementation project(":lib:anim")
    implementation project(':lib:libhttp')
//    implementation project(":lib:liblocation-wemap")
    implementation project(":lib:libuploader")
    implementation project(":lib:libwidget")
    implementation project(':lib:lame')
    implementation project(":lib:os-libshare")
    implementation project(":lib:libtrack-shushu")
    implementation project(":lib:startup")
    implementation project(":lib:libdeeplinkbase")
    implementation project(":lib:libshumei")
    implementation project(":lib:libproto")
    implementation project(":lib:deviceid")
    implementation project(":lib:libimageloader")
    implementation project(":lib:libappsflyer")
    implementation project(":lib:libdownload")
    implementation project(":lib:os-libphoto")
    implementation project(":lib:libpermission")
    implementation project(":lib:Emojilib")
    implementation project(":lib:libscan")
    implementation project(":lib:libwplink")
    implementation project(":lib:alpha-video")
    implementation project(":lib:libwebview")
    implementation project(":lib:libprovider")
    implementation project(':lib:video')
    implementation project(":lib:libcaptcha")

    implementation project(":component:google-pay")
    implementation project(":component:gift")
    implementation project(":component:activity")
    implementation project(":component:authcheck-no-op")
    implementation project(":component:prop")
    implementation project(":component:magic-emoji")
    implementation project(":component:suspend")
    implementation project(':component:adjustvolume')
    implementation project(':component:music')
    implementation project(':component:game-chip')
    implementation project(':component:decorate')
    implementation project(':component:barrage')

    implementation project(":module:webview")
    implementation project(":module:game-match")
    implementation project(":module:base-littlegame")
    implementation project(":module:littlegame")
    implementation project(":module:login-wejoy")
    implementation project(":module:rank-ar")
    implementation project(':module:basechat')
    implementation project(':module:hwroom')
//    implementation project(':module:baloot')
//    implementation project(':module:mahjong')
    implementation project(':module:medal')
    implementation project(':module:voiceroom:base')
    implementation project(':module:voiceroom:gift-game')
    implementation project(':module:voiceroom:nation-flag')
//    implementation project(':module:voiceroom:dragonsolo')
    implementation project(':module:care')
//    implementation project(':module:partyspy')
    implementation project(':module:teen-mode')
    implementation project(':module:bingo')
    implementation project(':module:competition')

    implementation project(':service:CustomerService')
    implementation project(':service:ConfigService')
    implementation project(':service:UserService')
    implementation project(':service:VoiceService')
    implementation project(':service:VoiceServiceTrt')
    implementation project(':service:VoiceServiceZego')
    implementation platform(libs.firebase.bom)
    implementation libs.bundles.firebases

    implementation(rootProject.ext.USE_DEV_SEC_LIB ? libs.security.dev : libs.security.release)

    implementation libs.androidx.appcompat
    implementation libs.constraintlayout
    implementation libs.recyclerview
    implementation libs.refresh.layout
    implementation libs.androidx.lifecycle.viewmodelKtx
    implementation libs.androidx.viewmodel.compose
    implementation libs.androidx.lifecycle.runtimektx
    implementation libs.androidx.lifecycle.livedata
    implementation libs.installreferrer

    implementation libs.kotlin.core
    implementation libs.kotlinx.coroutines.android
    implementation libs.eventBus
    implementation libs.exifinterface

    implementation libs.leakcanary.plumber
    implementation libs.glide.compose
    implementation project(":lib:libcomposewidget")

//    if (!USE_PAD.toBoolean()) {
//        implementation project(':wepie_asset_pack')
//    }

//    implementation libs.thirdlib.acrcloud

    implementation(libs.work.ktx) {
        exclude group: "androidx.lifecycle"
    }

    implementation libs.androidx.profileinstaller

    lintChecks rootProject.files("lint/lib-rules.jar")
    implementation libs.vrlottery.flexbox
    implementation libs.google.review
}

if (isHuawei) {
    apply from: rootProject.file("gradle/huawei.gradle")
}
apply from: rootProject.file("gradle/debug_tools.gradle")
apply from: rootProject.file("gradle/manifest.gradle")
//apply from: rootProject.file("gradle/asset_pack.gradle")
apply from: rootProject.file("gradle/base-composed.gradle")
apply from: rootProject.file("gradle/proguard.gradle")
apply from: rootProject.file("lint/lint.gradle")

tasks.withType(JavaCompile).configureEach {
    options.encoding = "UTF-8"
}

tasks.configureEach { task ->
    if (task.name == "lint") {
        task.enabled = false
    }
}

static def getLocalBuildTime() {
    def date = new Date()
    def formattedDate = date.format('yyyyMMddHHmmss')
    return formattedDate
}

static def getDate() {
    def date = new Date()
    def formattedDate = date.format('yyyy.MM.dd')
    return formattedDate
}

static def getBuildTime() {
    def date = new Date()
    def formattedDate = date.format('yyyy.MM.dd.HH.mm.ss')
    return formattedDate
}

def outProjectInfo() {
    def appName = getRootDir().getPath() + "/project_info.txt"
    def targetFile = new File(appName)
    def osm = targetFile.newOutputStream()
    def application = "applicationId \"" + rootProject.ext.packageName + "\"\n"
    osm.write(application.getBytes())
    def name = "versionName \"" + rootProject.ext.appConfig.VERSION_NAME + "\"\n"
    osm.write(name.getBytes())
    def code = "versionCode " + rootProject.ext.appConfig.VERSION_CODE + "\n"
    osm.write(code.getBytes())
    def unity_version = "unityVersion \"" + rootProject.ext.UNITY_VERSION + "\"\n"
    osm.write(unity_version.getBytes())
    osm.close()
}