<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.wepie.wespy">

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 百度定位SDK -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 获取运营商信息，用于支持提供运营商信息相关的接口 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 这个权限用于获取wifi的获取权限，wifi信息会用来进行网络定位 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 用于读取手机当前的状态 -->
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <!-- 访问网络，网络定位需要上网 -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- SD卡读取权限，用户写入离线定位数据 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" /> <!-- 华为推送权限 -->
    <!--  图片权限  -->
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"
        android:minSdkVersion="33" />
    <!--  声音权限  -->
    <uses-permission
        android:name="android.permission.READ_MEDIA_AUDIO"
        android:minSdkVersion="33" />
    <!--  视频权限  -->
    <uses-permission
        android:name="android.permission.READ_MEDIA_VIDEO"
        android:minSdkVersion="33" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- 通知权限 -->
    <!-- 广告 ID 需要权限 -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <!-- 必需的权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- &lt;!&ndash; 保存富媒体消息需要,无富媒体消息则不需要 &ndash;&gt; -->

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <queries>
        <package android:name="com.kakao.talk" />

        <!-- 风控检测 -->
        <package android:name="com.ss.android.ugc.aweme" /><!--  抖音 -->
        <package android:name="com.smile.gifmaker" /><!--  快手 -->
        <package android:name="com.kuaishou.nebula" /><!--  快手极速版 -->
        <package android:name="com.xunmeng.pinduoduo" /> <!--  拼多多 -->
        <package android:name="com.wepie.weplay" /><!--  会玩 -->
        <package android:name="com.wepie.ivy" /><!--  青藤之恋 -->
        <package android:name="com.tmri.app.main" /><!--  交管 12123 -->
        <package android:name="cn.gov.tax.its" /> <!-- 个人所得税 -->
        <!-- 风控检测 -->
    </queries>

    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_BASIC_PHONE_STAT"
        tools:node="remove" />
<!--    <uses-permission-->
<!--        android:name="android.permission.REQUEST_INSTALL_PACKAGES"-->
<!--        tools:node="remove" />-->
<!--    <uses-permission-->
<!--        android:name="android.permission.READ_LOGS"-->
<!--        tools:node="remove" />-->

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <!--  图片权限  -->
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />

    <application
        android:name=".base.WPApplication"
        android:allowBackup="true"
        android:allowNativeHeapPointerTagging="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:extractNativeLibs="true"
        android:fullBackupContent="@xml/backup_rules"
        android:gwpAsanMode="always"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:resizeableActivity="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup,android:fullBackupContent,android:extractNativeLibs">
        <profileable
            android:shell="true"
            tools:targetApi="29" />
        <activity
            android:name=".module.game.game.base.SimpleDialogActivity"
            android:launchMode="singleTop"
            android:theme="@style/PublicDialogActivityStyle" />
        <!-- <activity android:name=".helper.push.OppoPushClickActivity" -->
        <!-- android:theme="@style/PublicDialogActivityStyle" -->
        <!-- android:exported="true" -->
        <!-- &gt; -->
        <!-- &lt;!&ndash;<intent-filter>&ndash;&gt; -->
        <!-- &lt;!&ndash;<action android:name=".helper.push.OppoPushClickActivity" />&ndash;&gt; -->
        <!-- &lt;!&ndash;<category android:name="android.intent.category.DEFAULT" />&ndash;&gt; -->
        <!-- &lt;!&ndash;<category android:name="com.wepie.wespy" />&ndash;&gt; -->
        <!-- &lt;!&ndash;</intent-filter>&ndash;&gt; -->
        <!-- </activity> -->
        <activity android:name=".module.marry.lover_home.LoveHomeVoiceSettingActivity" />

        <meta-data
            android:name="android.max_aspect"
            android:value="2.2" />



        <activity
            android:name=".module.main.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />

        <activity android:name=".module.main.activity.events.EventsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NavTrans"/>

        <activity
            android:name=".module.login.start.StartActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/WodiStartStyle">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name=".module.login.start.StartActivity" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="jackaroolink"/>
            </intent-filter>
        </activity> <!-- 登录注册界面 -->
        <!-- 设置界面 -->
        <activity
            android:name=".module.settings.edituser.EditUserInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustUnspecified|stateAlwaysHidden" />
        <activity
            android:name=".module.settings.edituser.EditNickNameActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".module.settings.edituser.EditWodiIdActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".module.settings.edituser.EditSignatureActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".module.settings.main.MsgRemindActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.contact.searchuser.AddFriendActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.contact.friendlist.FriendsListActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".module.contact.newfriend.NewFriendActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.contact.detail.UserInfoDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.contact.title.UserTitleActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.contact.detail.UserVoiceRoomListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.chat.ui.single.ChatActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:windowSoftInputMode="stateHidden|adjustResize" /> <!-- 房间列表界面 -->
        <activity
            android:name=".module.game.room.roomcreate.CreateVoiceRoomActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustResize" /> <!-- 邀请小伙伴界面 -->
        <activity
            android:name=".module.game.room.roomcreate.InviteFriendActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustResize|stateHidden" /> <!-- 房间详情界面 -->
        <activity
            android:name=".module.scribble.ScribbleActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Fullscreen" />
        <activity
            android:name=".module.pay.commonapi.GoodsListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.album.BrowseImageActivity"
            android:theme="@style/ImageBrowserTheme" />
        <activity
            android:name=".helper.dialog.coin.PublicCoinPayDialogActivity"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".helper.dialog.coin.PublicCoinPayBottomDialogActivity"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".helper.dialog.coin.PublicCoinPayHorizontalActivity"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".helper.dialog.PublicTipsDialogActivity"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".helper.dialog.PublicForceUpdateDialogActivity"
            android:launchMode="singleTask"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".helper.dialog.PublicTipIdVerifyActivity"
            android:launchMode="singleTask"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".helper.dialog.PublicNotifyDialogActivity"
            android:launchMode="singleTask"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".module.fdiscover.main.CircleActivityNew"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.shop.activity.ShopActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.shop.activity.MyPropActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.edit.word.CardWordActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.redpacket.send2.SendPacketActivityNew"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.redpacket.choosebg.ChooseSendPacketActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.redpacket.bag.BackpackActivity114"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.marry.wedding.MarryShareActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.redpacket.detail.RedPacketDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.HalfStatusBar" />
        <activity
            android:name=".module.lookme.activity.LookMeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.task.view.TaskActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.fdiscover.broad.send.ChooseTimeDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.fdiscover.broad.send.SendActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.fdiscover.broad.broadlist.BroadListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.HalfStatusBar" />
        <activity
            android:name=".module.fdiscover.broad.show.BroadAppointedActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity android:name=".module.marry.regret.RegretDivorceActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"/>
        <activity android:name=".module.marry.regret.RegretDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"/>
        <activity
            android:name=".module.fdiscover.send.MultiSelectSendActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/AppTheme" />

        <service
            android:name=".module.voiceroom.util.VoiceRoomNotifyService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone"
            android:stopWithTask="true" />

        <activity
            android:name=".module.task.share_task.ShareEarnCoinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.marry.lover_home.LoverHomeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.lover_home.LoverHomeSettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.marry.lover_home.updatering.LoverHomeUpdateSingleRingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.marry.lover_home.LoverHomeChooseBGActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.settings.main.ShareGameStateActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.settings.main.JoinedRoomStateActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.contact.detail.UserCircleActivityNew"
            android:screenOrientation="portrait" />
        <activity
            android:name=".helper.update.UpdateDialogActivity"
            android:launchMode="singleTask"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".module.shop.insurance.InsuranceHelpActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.shop.insurance.InsuranceActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.shop.insurance.RoseRequestActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".helper.scheme.SchemeActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:noHistory="true"
            android:theme="@style/PublicDialogActivityStyle">
            <intent-filter>

                <!-- 协议部分，随便设置 -->
                <data android:scheme="com_wepie_wespy" />
                <!-- 下面这几行也必须得设置 -->
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name=".module.contact.detail.CharmHelpActivity"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".module.game.room.roomcreate.RoomSecretActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.voiceroom.setting.VoiceRoomSettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.voiceroom.setting.VoiceRoomNameEditActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.voiceroom.setting.VoiceRoomNoticeEditActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.voiceroom.setting.VoiceRoomMemberActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.voiceroom.setting.VoiceRoomThemeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.voiceroom.setting.VoiceRoomFansActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.voiceroom.setting.VoiceRoomLabelActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.voiceroom.setting.VoiceRoomIntimacyActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".cocosnew.match.main.CocosMainActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Cocos" />
        <activity
            android:name=".cocosnew.match.matching.CocosMatchActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Cocos" />
        <activity
            android:name=".cocosnew.match.create.CocosCreateRoomActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Cocos" />
        <activity
            android:name=".cocosnew.match.rank.GameRankActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Cocos" />
        <activity
            android:name=".cocosnew.match.prepare.CocosMatchPrepareActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Cocos" />
        <activity
            android:name=".module.chat.ui.group.GroupChatActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />
        <activity
            android:name=".module.chat.ui.group.interest.InterestGroupChatMainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".module.chat.ui.group.interest.InterestGroupCreateActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.chat.ui.group.interest.GroupInfoDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.chat.ui.group.interest.GroupAdminManagerActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.chat.ui.group.interest.GroupAdminManagerActivityNew"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".module.chat.ui.group.interest.AddGroupActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".module.chat.ui.group.interest.GroupManagerActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.chat.ui.group.interest.GroupInfoEditActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.chat.ui.group.interest.GroupAddMethodManagerActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.chat.ui.group.GroupChatSettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.chat.ui.group.GroupNoteActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.chat.ui.group.GroupNameActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.chat.ui.group.GroupAllIconDisplayActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".helper.dialog.TextDialogActivity"
            android:theme="@style/TranslucentDialogActivityStyle" />
        <activity
            android:name=".helper.seletefriend.FriendChooseActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.discover.square.topic.DiscoverSquareTopicActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.discover.msg.DiscoverMsgActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.discover.square.SquareTopicSelectActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.discover.post.DiscoverPostDetailActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.discover.likes.PostLikeListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.fdiscover.broad.send.selecttime.SelectDayActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.voiceroom.LittleGameRecoverActivity"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".module.main.view.game.friendplay.FriendPlayActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />
        <activity
            android:name=".module.settings.main.ShareRecordStateActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.settings.main.BlackListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.wejoy.jackaroo.record.JackarooGameRecordActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.discover.DiscoverCircleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.lover_home.LoverSendGiftSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".helper.dialog.DoubleBtnDialogActivity"
            android:theme="@style/PublicDialogActivityStyle" />
        <!--        <activity-->
        <!--            android:name=".module.shop.doudou.DoudouLotteryActivity"-->
        <!--            android:screenOrientation="portrait" />-->
        <!--        <activity-->
        <!--            android:name=".module.pay.doudou.ExchangeDoudouActivity"-->
        <!--            android:theme="@style/PublicDialogActivityStyle"-->
        <!--            android:windowSoftInputMode="adjustPan" />-->
        <activity
            android:name=".module.contact.detail.UserSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".helper.dialog.PublicOpenNotificationDialogActivity"
            android:theme="@style/PublicDialogActivityStyle" />
        <activity
            android:name=".module.shop.activity.AllGoodsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.shop.activity.SecKillListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.church.ChurchActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.propose.main.ProposeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />
        <activity
            android:name=".module.marry.divorces.DivorcesActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />
        <activity
            android:name=".module.marry.wedding.main.WeddingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />
        <activity
            android:name=".module.marry.propose.msg.ProposeMsgDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.propose.msg.ProposeMsgListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />
        <activity
            android:name=".module.marry.propose.process.ProposeEditTextActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.propose.template.ProposeTemplateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.propose.process.ProposeChooseUserActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.invitation.EditTemplateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.invitation.CreateInvitationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.DressWeddingRoomActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.invitation.InvitationTemplateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.edit.bgm.EditBgmActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.edit.auction.AuctionActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />
        <activity
            android:name=".module.marry.lover_home.updatering.LoverUpdateCoupleRingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.WeddingRoomSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.SelectHostListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.wedding.SetHostActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.marry.church.emcee.EmceeListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.draw.core.share.DrawPlayActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.DarkNavigation" />
        <activity
            android:name=".module.voiceroom.main.BaseVoiceRoomActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoExitAnimation"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.voiceroom.setting.VoiceRoomModeEditActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.voiceroom.music.ChooseSongActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustUnspecified|stateHidden" />
        <activity
            android:name=".module.vip.main.VipMainActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.vip.donate.DonateVipToFriendActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.vip.donate.GainVipDonateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.vip.record.BuyVipRecordActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.pwd.PwdActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.pwd.ForgetPwdActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.main.FamilyMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.family.detail.FamilyDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.HalfStatusBar" />
        <activity
            android:name=".module.family.member.FamilyMemberActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.family.member.FamilyMemberManageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.main.FamilyCreateActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".module.family.main.FamilySettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.steal.FamilyStealActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.lottery.FamilyLotteryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.HalfStatusBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.family.main.mine.family.benefits.FamilyBenefitsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.HalfStatusBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.family.main.mine.manage.FamilyEditHeadActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.main.mine.manage.FamilyEditNameActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.main.mine.manage.FamilyEditDescActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.main.mine.manage.title.FamilyTitleDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.main.mine.manage.title.FamilyTitleSetUpActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.box.send.FamilySendBoxActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.family.box.send.choose.FamilyChooseBoxActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.settings.edituser.phone.ReplacePhoneSuccessActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.common.CommonSelectListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.report.ReportUserActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.report.ReportMainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.report.module.message.ReportChatChooseActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.report.module.privatechat.ReportPrivateChatActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.report.module.privatechat.ReportBlockUserSuccessActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.report.module.common.ReportPublicScreenActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".module.activity.tab.ActivityTabActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.chat.ui.chatmore.ChatMoreActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.match.AudioMatchActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.HalfStatusBar" />
        <activity
            android:name=".module.msgroaming.MsgRoamingMainActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.msgroaming.sync.MsgRoamingSyncActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.chat.send.face.EmoticonFavoriteActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.chat.send.face.EmoticonShowActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.advertisement.AdvertisementActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.activity.gamecenter.GameCenterActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.HalfStatusBar" />
        <activity
            android:name="com.wepie.wespy.module.voiceroom.advance.AdvancedRoomDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wepie.wespy.module.voiceroom.advance.CreateAdvancedRoomActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.locationslecet.LocationSelectActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.settings.edituser.phone.WejoyVerifyPhoneActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.settings.edituser.phone.WejoyVerifyPhoneCodeActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.wejoy.weplay.module.settings.main.WeJoySettingActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent"/>
        <activity
            android:name="com.wejoy.weplay.module.settings.main.WeJoyAccountManagerSettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />

        <activity
            android:name="com.wejoy.weplay.module.settings.main.WejoyMsgRemindActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />

        <activity
            android:name="com.wejoy.weplay.module.settings.main.WejoySettingCommonActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />

        <activity
            android:name="com.wejoy.weplay.module.settings.main.WejoyAboutActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />

        <activity
            android:name="com.wejoy.weplay.module.settings.main.WejoyChangeLangActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />

        <activity
            android:name="com.wejoy.weplay.module.settings.main.RestartActivity"
            android:process=":restart"
            android:screenOrientation="portrait"
            android:theme="@style/WodiStartStyle" />

        <service
            android:name="com.wejoy.weplay.module.settings.main.RestartService"
            android:process=":restart" />

        <activity
            android:name="com.wejoy.weplay.module.settings.main.WejoyPrivacyActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent" />

        <activity
            android:name="com.wepie.wespy.module.main.manage.GameRecoverActivity"
            android:launchMode="singleTop"
            android:theme="@style/PublicDialogActivityStyle" />

        <activity
            android:name="com.wejoy.weplay.module.login.register.WejoyCompleteUserInfoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.wejoy.weplay.module.login.register.WejoyCompleteBirthdayActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible|stateHidden" />

        <activity
            android:name="com.wejoy.weplay.module.settings.main.WejoyPreferenceActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Translucent"
            android:windowSoftInputMode="stateVisible|stateHidden" />

        <activity
            android:name="com.wejoy.weplay.module.makefriend.hot.HotCountryVoiceRoomActivity"
            android:screenOrientation="portrait" />

<!--        <activity-->
<!--            android:name=".helper.firebase.FirebaseMiddleActivity"-->
<!--            android:exported="true"-->
<!--            android:screenOrientation="portrait"-->
<!--            android:theme="@style/WodiStartStyle">-->

<!--            <intent-filter android:autoVerify="true">-->
<!--                <action android:name="android.intent.action.VIEW"/>-->
<!--                <category android:name="android.intent.category.DEFAULT"/>-->
<!--                <category android:name="android.intent.category.BROWSABLE"/>-->
<!--                <data-->
<!--                    android:host="weplayar.page.link"-->
<!--                    android:scheme="https"/>-->
<!--            </intent-filter>-->
<!--        </activity>-->

        <activity
            android:name=".module.voiceroom.roomgroup.RoomOwnerGroupActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".module.chat.ui.group.interest.InterestGroupApplyActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />

        <activity
            android:name="com.wejoy.jackaroo.qualifying.JackarooQualifyingActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.TranslucentSystemBar" />

        <activity
            android:name="com.wejoy.jackaroo.vip.JackarooVipRoomListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NavTrans" />
        <activity
            android:name="com.wejoy.jackaroo.create.JackarooRoomCreateActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NavTrans" />

        <activity android:name="com.wejoy.weplay.module.settings.main.WeJoyClearCacheActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"/>

        <activity android:name="com.wejoy.weplay.module.settings.main.WeJoyGameCacheActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"/>

        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages.
        See README(https://goo.gl/l4GJaQ) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/default_notification_icon" />
        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming
             notification message. See README(https://goo.gl/6BKBk7) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/default_notification_color" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="wespy_chat" />

        <service
            android:name=".base.WPFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!--        <provider-->
        <!--            android:name="com.google.firebase.provider.FirebaseInitProvider"-->
        <!--            android:authorities="${applicationId}.firebaseinitprovider"-->
        <!--            tools:node="remove" />-->

        <provider
            android:name="com.facebook.internal.FacebookInitProvider"
            android:authorities="${applicationId}.FacebookInitProvider"
            android:exported="false"
            tools:node="remove" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <!-- This entry makes ExampleLoggerInitializer discoverable. -->
            <meta-data
                android:name="com.wepie.wespy.base.startup.FirebaseProviderInitializer"
                android:value="androidx.startup" />

            <meta-data
                android:name="com.wepie.wespy.base.startup.FacebookInitializer"
                android:value="androidx.startup" />
        </provider>

<!--        <meta-data-->
<!--            android:name="com.wepie.wespy.voiceroom.dragonsolo.DragonSoloInitializer"-->
<!--            android:value="wp-initializer" />-->

    </application>

</manifest>