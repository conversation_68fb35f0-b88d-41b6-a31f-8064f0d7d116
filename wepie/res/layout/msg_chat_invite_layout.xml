<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:maxWidth="238dp"
    android:minWidth="238dp"
    android:paddingLeft="10dp"
    android:paddingTop="5dp"
    android:paddingRight="4dp"
    android:paddingBottom="5dp">

    <TextView
        android:id="@+id/invite_msg_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="9dp"
        android:includeFontPadding="false"
        android:maxWidth="225dp"
        android:textAlignment="viewStart"
        android:textColor="@color/color_text_primary"
        android:textSize="14dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="你关注的活动「语音房活动标题」开始咯~快来看看吧！你关注的活动「语音房活动标题」开始咯~快来看看吧！" />

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/invite_msg_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="8dp"
        android:src="@drawable/default_head_icon"
        app:civ_corner_radius="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/invite_msg_content"
        tools:src="@drawable/default_head_icon" />

    <TextView
        android:id="@+id/invite_msg_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxWidth="160dp"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@color/color_text_primary"
        android:textSize="14dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/invite_msg_sub_title"
        app:layout_constraintStart_toEndOf="@id/invite_msg_icon"
        app:layout_constraintTop_toTopOf="@id/invite_msg_icon"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="远离风暴浩劫远离风暴浩劫远离风暴浩劫远离风暴浩劫远离风暴浩劫远离风暴浩劫远离风暴浩劫远离风暴浩劫远离风暴浩劫" />

    <TextView
        android:id="@+id/invite_msg_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="5dp"
        android:alpha="0.6"
        android:includeFontPadding="false"
        android:maxWidth="156dp"
        android:maxLines="3"
        android:paddingEnd="5dp"
        android:textAlignment="viewStart"
        android:textColor="@color/color_text_tertiary"
        android:textSize="12dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/invite_msg_title"
        app:layout_constraintTop_toBottomOf="@id/invite_msg_title"
        tools:text="答！文多多多多答！文多多多多答！文多多多多答！文多多多多答！文多多多多答！文多多多多" />

</androidx.constraintlayout.widget.ConstraintLayout>