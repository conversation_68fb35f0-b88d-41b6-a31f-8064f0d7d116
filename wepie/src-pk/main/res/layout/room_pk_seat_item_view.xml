<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false">

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/pk_seat_title_view"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/color_black"
        tools:visibility="visible" />

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/pk_seat_title_svga"
        android:layout_width="48dp"
        android:layout_height="48dp"
        app:layout_constraintBottom_toBottomOf="@id/pk_seat_title_view"
        app:layout_constraintEnd_toEndOf="@id/pk_seat_title_view"
        app:layout_constraintStart_toStartOf="@id/pk_seat_title_view"
        app:layout_constraintTop_toTopOf="@+id/pk_seat_title_view" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/pk_seat_identity_view"
        android:layout_width="wrap_content"
        android:layout_height="14dp"
        android:background="@drawable/shape_ff7596_ec2e82_corner10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_marginTop="48dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/pk_seat_identity_icon"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/pk_seat_identity_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/pk_seat_icon" />

        <TextView
            android:id="@+id/pk_seat_identity_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="6dp"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="10dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/pk_seat_identity_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="PK" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/pk_seat_vote_tv"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/sel_00ccf9_corner16"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/pk_vote"
        android:textColor="@color/white"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pk_seat_identity_view"
        tools:layout_marginTop="54dp"
        tools:layout_width="60dp"
        tools:visibility="gone" />

    <FrameLayout
        android:id="@+id/pk_seat_send_gift_view"
        android:layout_width="80dp"
        android:layout_height="24dp"
        android:layout_marginTop="6dp"
        android:clipChildren="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pk_seat_identity_view"
        tools:visibility="visible">

        <FrameLayout
            android:layout_width="48dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:background="@drawable/shape_fe6484_corner16" />

        <ImageView
            android:id="@+id/pk_seat_send_gift_icon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="center"
            android:src="@drawable/pk_seat_send_gift_icon" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>