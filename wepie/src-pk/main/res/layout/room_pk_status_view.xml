<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/pk_status_root_view"
    android:layout_width="wrap_content"
    android:layout_height="43dp"
    android:clipChildren="false"
    android:minWidth="101dp"
    android:paddingVertical="10dp">

    <FrameLayout
        android:id="@+id/pk_status_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/pk_status_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/pk_status_unstart"
        android:layout_width="wrap_content"
        android:layout_height="43dp"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:lines="1"
        android:paddingHorizontal="12dp"
        android:paddingTop="3dp"
        android:textColor="@color/white"
        android:textSize="12dp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Başlamayı Bekliyor"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/pk_status_icon"
        android:layout_width="17dp"
        android:layout_height="14dp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="2dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/pk_icon"
        tools:visibility="gone" />

    <TextView
        android:id="@+id/pk_status_started"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="3dp"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="12dp"
        android:textStyle="bold"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/pk_status_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="15:40"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>