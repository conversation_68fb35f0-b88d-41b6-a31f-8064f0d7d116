<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false">

    <FrameLayout
        android:id="@+id/pk_multi_red_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="-2dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/pk_multi_guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/pk_multi_blue_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-2dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/pk_multi_guideline"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <com.wepie.wespy.module.voiceroom.pk445.PkStatusView
        android:id="@+id/pk_status_view"
        android:layout_width="wrap_content"
        android:layout_height="43dp"
        android:layout_marginTop="-10dp"
        android:minWidth="101dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pk_progress_bar" />

    <com.wepie.wespy.module.voiceroom.pk445.PkProgressBar
        android:id="@+id/pk_progress_bar"
        android:layout_width="match_parent"
        android:layout_height="14dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="3dp"
        android:clipChildren="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/pk_multi_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/pk_manage_icon"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginStart="5dp"
        android:padding="3dp"
        app:layout_constraintStart_toEndOf="@id/pk_status_view"
        app:layout_constraintTop_toBottomOf="@id/pk_progress_bar"
        tools:src="@color/red" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/pk_red_supported"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/shape_33000000_stroke_fe6484"
        android:drawablePadding="4dp"
        android:includeFontPadding="false"
        android:paddingHorizontal="8dp"
        android:text="@string/pk_voted"
        android:textColor="#FE6484"
        android:textSize="12dp"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/pk_red_support"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pk_progress_bar" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/left_guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="20dp" />

    <com.huiwan.widget.MarqueeTextView
        android:id="@+id/pk_blue_supported"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/shape_33000000_stroke_00ccf9"
        android:drawablePadding="4dp"
        android:drawableTint="@color/color_accent"
        android:ellipsize="marquee"
        android:includeFontPadding="false"
        android:lines="1"
        android:paddingHorizontal="8dp"
        android:singleLine="true"
        android:text="@string/pk_voted"
        android:textColor="@color/color_accent"
        android:textSize="12dp"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/pk_red_support"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toEndOf="@id/pk_manage_icon"
        app:layout_constraintTop_toBottomOf="@id/pk_progress_bar" />

</androidx.constraintlayout.widget.ConstraintLayout>