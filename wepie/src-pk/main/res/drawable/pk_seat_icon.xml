<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="12dp"
    android:height="12dp"
    android:viewportWidth="12"
    android:viewportHeight="12">
  <path
      android:pathData="M2.048,3.285C2.181,2.292 3.029,1.551 4.03,1.551H8.216C9.253,1.551 10.119,2.345 10.208,3.379L10.255,3.922C10.209,3.919 10.163,3.918 10.118,3.918H9.87C9.861,3.918 9.851,3.918 9.842,3.918L9.752,3.921C9.742,3.921 9.733,3.922 9.723,3.922C8.61,3.986 7.631,4.778 7.417,5.911L7.16,7.268H4.84L4.583,5.911C4.359,4.729 3.305,3.918 2.13,3.918H1.963L2.048,3.285ZM2.51,10.178L2.51,10.183H9.749C10.299,10.044 10.733,9.563 10.839,9.004L11.345,6.331C11.468,5.679 11.019,5.056 10.341,4.937C10.267,4.924 10.192,4.918 10.118,4.918H9.87L9.781,4.92C9.097,4.96 8.523,5.443 8.4,6.097L7.988,8.268H4.012L3.601,6.097C3.471,5.414 2.852,4.918 2.13,4.918H1.882C1.808,4.918 1.733,4.924 1.659,4.937C0.981,5.056 0.532,5.679 0.655,6.331L1.161,9.004C1.283,9.648 1.841,10.126 2.51,10.178Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="6"
          android:startY="1.551"
          android:endX="6"
          android:endY="10.183"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#B2FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
