package com.wejoy.jackaroo.qualifying.dialog

import android.content.Context
import android.content.DialogInterface
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.collection.ArrayMap
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.*
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.ConstV3Info
import com.huiwan.constants.GameType
import com.huiwan.lib.api.ApiService
import com.huiwan.widget.HWEffectTextView
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wejoy.jackaroo.home.JackarooTextGradient
import com.wejoy.weplay.ex.view.launch
import com.wepie.lib.api.plugins.share.*
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoadListener
import com.wepie.libimageloader.WpImageLoader
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.databinding.JackarooQfGradeShareViewBinding
import com.wepie.wespy.databinding.JackarooQfGradeUpDialogFragmentBinding
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import com.wepie.wespy.helper.shence.ShenceEvent
import com.wepie.wespy.model.entity.gamestate.CheckoutInfo
import com.wepie.wespy.net.http.api.markQFGradeDialogShow
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.lang.Exception

class JKQFGradeUpDialog : BaseDialogFragment() {
    private var continuation: CancellableContinuation<Int>? = null
    private lateinit var binding: JackarooQfGradeUpDialogFragmentBinding
    private var checkoutInfo: CheckoutInfo? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        binding = JackarooQfGradeUpDialogFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }
    private val shareViewBinding: JackarooQfGradeShareViewBinding by lazy { JackarooQfGradeShareViewBinding.inflate(
        LayoutInflater.from(context)) }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        StatusBarUtil.hideBangStatus(dialog?.window)
        initClickEvent()
        bindCheckInfo()
        markCheckoutRead()
        reportViewScreen()
    }

    private fun markCheckoutRead() {
        binding.root.launch {
            val result = markQFGradeDialogShow()
            if (result is KtResultSuccess) {
                HLog.d(TAG, HLog.USR, "markQFGradeDialogShow response succeed")
            } else if (result is KtResultFailed) {
                HLog.d(TAG, HLog.USR, "markQFGradeDialogShow response code: ${result.code} msg: ${result.msg}")
            }
        }
    }

    private fun initClickEvent() {
        initButton(binding.jackarooLevelUpOkTv) {
            dismissAllowingStateLoss()
            reportAppClick(TrackButtonName.CONFIRM)
        }
        initButton(binding.jackarooLevelUpShareTv) {
            doShare()
            reportAppClick(TrackButtonName.SHARE)
        }
    }

    private fun reportAppClick(btnName: String){
        val map: MutableMap<String, Any> = ArrayMap()
        map["game_type"] = GameType.GAME_TYPE_JACKAROO
        ShenceEvent.appClick(TrackScreenName.QUALIFYING_SETTLE_PAGE, btnName, map)
    }

    private fun reportViewScreen() {
        val map: MutableMap<String, Any> = ArrayMap()
        map["game_type"] = GameType.GAME_TYPE_JACKAROO
        TrackUtil.appViewScreen(TrackScreenName.QUALIFYING_SETTLE_PAGE, map)
    }

    private fun bindCheckInfo() {
        checkoutInfo?.let {
            bindGradeInfoBg(it)
            bindGradeTitle(it)
            bindGradeIconAndName(it)
            bindRankAndEarning(it)
            bindReward(it)
        }
    }

    private fun bindGradeInfoBg(checkoutInfo: CheckoutInfo) {
        val bgSource = ResUtil.getStr(R.string.jackaroo_qualifying_grade_dialog_info_bg_url)
        WpImageLoader.load(
            bgSource,
            null, ImageLoadInfo().owner(binding.root).placeholder(R.drawable.jackaroo_qf_grade_info_bg)
                .error(R.drawable.jackaroo_qf_grade_info_bg),
            object : WpImageLoadListener<String> {
                override fun onComplete(model: String?, t: Drawable): Boolean {
                    binding.jackarooGradeInfoLay.background = t
                    return false
                }

                override fun onFailed(model: String?, e: Exception?): Boolean {
                    HLog.d(TAG, "load img failed $bgSource  exception: ${e?.stackTrace}")
                    return false
                }
            }
        )
        if (checkoutInfo.change > 0) {
            showSvgaAnimOnGradeUp()
        }
    }

    private fun bindGradeTitle(checkoutInfo: CheckoutInfo) {
        JackarooTextGradient(
            binding.root,
            R.id.jackaroo_grade_up_title_tv,
            R.color.color_fcedcd,
            R.color.color_yellow_disabled
        )
        (binding.jackarooGradeUpTitleTv.layoutParams as? MarginLayoutParams)?.let {
            it.topMargin = (ScreenUtil.getScreenHeight() * 0.08f).toInt()
            binding.jackarooGradeUpTitleTv.layoutParams = it
        }
        binding.jackarooGradeUpTitleTv.text = if (checkoutInfo.change > 0) ResUtil.getStr(R.string.jackaroo_grade_up)
        else if (checkoutInfo.change == 0) ResUtil.getStr(R.string.jackaroo_grade_keep)
        else ResUtil.getStr(R.string.jackaroo_grade_down)
    }

    private fun bindGradeIconAndName(checkoutInfo: CheckoutInfo) {
        JackarooTextGradient(
            binding.root,
            R.id.jackaroo_grade_name_tv,
            R.color.color_fffcdb,
            R.color.jackaroo_basic_end
        )
        val qfGrade = ConfigHelper.getInstance().constV3Info.findQFGrade(checkoutInfo.grade)
        qfGrade?.let {
            preloadShareGradeIcon(it)
            binding.jackarooGradeNameTv.text = it.name
            WpImageLoader.load(it.icon, binding.jackarooGradeIconIv)
        }
        if (checkoutInfo.star > 0) {
            binding.gradeKingLay.visibility = View.VISIBLE
            binding.gradeKingTv.text = ResUtil.getStr(R.string.x_number, checkoutInfo.star.toString())
        } else {
            binding.gradeKingLay.visibility = View.GONE
        }
    }

    private fun preloadShareGradeIcon(qfInfo: ConstV3Info.QualifyingGrade) {
        WpImageLoader.load(qfInfo.icon, shareViewBinding.jackarooGradeIconIv)
    }

    private fun bindRankAndEarning(checkoutInfo: CheckoutInfo) {
        binding.rankNumTv.text = ResUtil.getStr(R.string.common_no_x, checkoutInfo.rank.toString())
        binding.earningNumTv.text = StringUtil.formatGuardNumberForEN(checkoutInfo.earning.toLong())
    }

    private fun bindReward(checkoutInfo: CheckoutInfo) {
        HLog.d(TAG, HLog.USR, "bindReward ${checkoutInfo.reward}")
        if (checkoutInfo.reward.isNullOrEmpty()) {
            applyRewardInfoVisibility(View.GONE)
            return
        }
        applyRewardInfoVisibility(View.VISIBLE)
        JackarooTextGradient(binding.root, R.id.reward_title_tv, R.color.color_fffcdb, R.color.jackaroo_basic_end)
        val itemPadding = ScreenUtil.dip2px(16F)
        val iconBgRes = R.drawable.jackaroo_level_reward_item_bg
        val textColor = ResUtil.getColor(R.color.white_color)
        val data = checkoutInfo.reward

        val dp4 = ScreenUtil.dip2px(4F)
        binding.jackarooGradeUpRewardRv.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.HORIZONTAL, false)
        binding.jackarooGradeUpRewardRv.addItemDecoration(
            SpaceItemDecoration(
                Rect(itemPadding, 0, 0, 0), Rect(dp4, 0, dp4, 0)
            )
        )
        binding.jackarooGradeUpRewardRv.adapter = JackarooGradeRewardAdapter(data, iconBgRes, textColor)

    }

    private fun applyRewardInfoVisibility(visibility: Int) {
        binding.rewardTitleTv.visibility = visibility
        binding.rewardTitleLeftDivider.visibility = visibility
        binding.rewardTitleRightDivider.visibility = visibility
        binding.jackarooGradeUpRewardRv.visibility = visibility
    }

    private fun showSvgaAnimOnGradeUp() {
        WpImageLoader.load(
            ResUtil.getStr(R.string.jackaroo_qualifying_grade_up_anim_url),
            binding.jackarooGradeUpBgAnimIv,
            ImageLoadInfo().setLoopCount(1)
        )
        WpImageLoader.load(
            ResUtil.getStr(R.string.jackaroo_qualifying_star_anim_url),
            binding.jackarooGradeUpStarAnimIv
        )
    }

    private fun initButton(view: HWEffectTextView, click: suspend () -> Unit) {
        PressUtil.addPressEffect(view)
        view.setOnClickListener { lifecycleScope.launch { click.invoke() } }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setDimAmount(0.7F)
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        continuation?.resumeWith(Result.success(1))
    }


    private suspend fun doShare() {
        val ctx = context ?: return
        val shareInfo = ShareInfo()
        shareInfo.setTitle(ConfigHelper.getInstance().myShareTitle)
        shareInfo.content = ConfigHelper.getInstance().myShareDesc

        shareInfo.setShareContentType(ShareInfo.SHARE_CONTENT_TYPE_IMG)
        shareInfo.screenName = TrackScreenName.SHARE_PAGE
        shareInfo.scene = TrackString.SCENE_GAME
        shareInfo.gameType = GameType.GAME_TYPE_JACKAROO
        val bgColor = ContextCompat.getColor(binding.root.context, R.color.color_transparent70)
        shareInfo.picBgDrawable = ColorDrawable(bgColor)


        var rewardBp: Bitmap? = null
        if (binding.jackarooGradeUpRewardRv.visibility == View.VISIBLE
            && binding.jackarooGradeUpRewardRv.width > 0 && binding.jackarooGradeUpRewardRv.height > 0
        ) {
            rewardBp = Bitmap.createBitmap(
                binding.jackarooGradeUpRewardRv.width,
                binding.jackarooGradeUpRewardRv.height,
                Bitmap.Config.ARGB_8888
            )
            binding.jackarooGradeUpRewardRv.draw(Canvas(rewardBp))
        }
        HLog.d(TAG, HLog.USR,
            "doShare with rewardRV w: ${binding.jackarooGradeUpRewardRv.width} h: ${binding.jackarooGradeUpRewardRv.height}")
        val holder = JKQFGradeShareHolder(ctx, checkoutInfo, rewardBp, shareViewBinding)
        delay(150)
        val bitmap = holder.getBitmap()
        shareInfo.setBitmap(bitmap)
        val shareApi = ApiService.of(IShareApi::class.java)
        shareApi.showPictureShareDialog(context, shareInfo, object : ShareCallback {
            override fun onShare(data: ShareResult): Boolean {
                if (data.shareType == ShareType.saveBmp) {
                    ToastUtil.show(data.msg)
                }
                return false
            }
        })
    }

    private suspend fun showDialog(manager: FragmentManager) = suspendCancellableCoroutine {
        show(manager, "")
        continuation = it
    }

    companion object {
        const val TAG = "JKQFGradeUpDialog"

        init {
            WpImageLoader.downloadOnly(
                ResUtil.getStr(R.string.jackaroo_qualifying_grade_up_anim_url),
                ImageLoadInfo.newInfo(),
                null
            )
            WpImageLoader.downloadOnly(
                ResUtil.getStr(R.string.jackaroo_qualifying_grade_dialog_info_bg_url),
                ImageLoadInfo.newInfo(),
                null
            )
        }

        @JvmStatic
        fun show(
            context: Context,
            checkoutInfo: CheckoutInfo,
            listener: DialogInterface.OnDismissListener? = null
        ) {
            val activity = ContextUtil.getFragmentActivityFromContext(context) ?: return
            activity.lifecycleScope.launch {
                val fragment = JKQFGradeUpDialog()
                fragment.initFullScreen()
                fragment.initNormalStyle()
                fragment.checkoutInfo = checkoutInfo
                fragment.showDialog(activity.supportFragmentManager)
                listener?.onDismiss(null)
            }
        }
    }
}