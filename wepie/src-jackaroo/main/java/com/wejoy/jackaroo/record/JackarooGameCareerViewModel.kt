package com.wejoy.jackaroo.record

import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.configservice.model.PropItem
import com.huiwan.lib.api.ApiService
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserServiceKt
import com.huiwan.user.entity.User
import com.wejoy.weplay.ex.view.postAutoCancel
import com.wepie.lib.api.plugins.share.IShareApi
import com.wepie.lib.api.plugins.share.ShareCallback
import com.wepie.lib.api.plugins.share.ShareInfo
import com.wepie.lib.api.plugins.share.ShareResult
import com.wepie.lib.api.plugins.share.ShareType
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.wespy.databinding.JackarooGameRecordActivityBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class JackarooGameCareerViewModel : ViewModel() {
    private val _collectionAlbumFlow: MutableStateFlow<CollectionAlbum?> =
        MutableStateFlow(null)
    val collectionAlbumFlow = _collectionAlbumFlow.asStateFlow()

    internal val collectionPageStateFlow: StateFlow<CollectionPageState> =
        _collectionAlbumFlow.mapLatest {
            it.CollectionPageState()
        }.stateIn(
            scope = viewModelScope,
            initialValue = CollectionPageState.Idle,
            started = SharingStarted.Eagerly,
        )

    val shouldHideGameRecord: Boolean
        get() = LoginHelper.getLoginUid() != uid && _userInfoStateFlow.value?.achivement_not_show != 0

    private val _userInfoStateFlow: MutableStateFlow<User?> = MutableStateFlow(null)
    val userInfoStateFlow = _userInfoStateFlow.asStateFlow()

    private val _recordInfoLiveData = MutableLiveData<UserRecordInfo>()
    val recordInfoLiveData: LiveData<UserRecordInfo> = _recordInfoLiveData

    private val _collectionTypeFlow = MutableStateFlow(JackarooCollectionInfo())
    val collectionTypeFlow: Flow<JackarooCollectionInfo> = _collectionTypeFlow.asStateFlow()

    private val _outAppbarStateFlow = MutableStateFlow(AppbarState(false, true))
    val outAppbarState: Flow<AppbarState> = _outAppbarStateFlow.asStateFlow()

    private val _eventFlow = MutableSharedFlow<JackarooGameCareerEvent>()

    val flow: Flow<JackarooGameCareerEvent> = _eventFlow
    var uid = 0
        private set

    val GAME_CAREER_TAB_COLLECTIONINDEX = 0
    val GAME_CAREER_TAB_GAME_RECORD_INDEX = 1

    fun init(uid: Int) {
        if (this.uid == uid) {
            return
        }
        this.uid = uid
        viewModelScope.launch {
            val info = UserServiceKt.get().getCacheUser(uid) ?: return@launch
            _userInfoStateFlow.value = info
        }
        GameCareerCache.collectionAlbumFlow(uid, viewModelScope).let { albumFlow ->
            viewModelScope.launch {
                albumFlow.collectLatest {
                    _collectionAlbumFlow.value = it
                }
            }
        }
        GameCareerCache.userRecordInfoFlow(uid, viewModelScope).let { userRecordFlow ->
            viewModelScope.launch {
                userRecordFlow.collect {
                    it?.let {
                        _recordInfoLiveData.value = it
                    }
                }
            }
        }

        GameCareerCache.userFlow(uid, viewModelScope).let { userFlow ->
            viewModelScope.launch {
                userFlow.collectLatest {
                    _userInfoStateFlow.value = it
                }
            }

        }
    }

    override fun onCleared() {
        super.onCleared()
        GameCareerCache.clear()
    }

    fun isGameRecordTab(gameCareerTabIndex: Int): Boolean =
        gameCareerTabIndex == GAME_CAREER_TAB_GAME_RECORD_INDEX

    fun notifyEvent(event: JackarooGameCareerEvent) {
        viewModelScope.launch {
            _eventFlow.emit(event)
        }
    }

    fun onCollectionIntent(intent: JackarooCollectionIntent) {
        when (intent) {
            is JackarooCollectionIntent.ShowDialog -> onShowCollectionDialog(intent.level)
            is JackarooCollectionIntent.ShowMore -> onShowCollectionMore(intent.type)
        }
    }

    /**
     * 显示收藏对话框，构建指定等级的收藏信息
     *
     * @param grade 道具等级
     */
    private fun onShowCollectionDialog(grade: Int) {
        // 获取指定等级的收藏数据，如果不存在则使用默认值
        val gradeItem = _collectionAlbumFlow.value?.items?.find { it.grade == grade }
            ?: CollectionGradeItem()

        viewModelScope.launch(Dispatchers.IO) {
            val collectionInfo = buildCollectionInfo(gradeItem, grade)
            _collectionTypeFlow.emit(collectionInfo)
        }
    }

    fun notifyAppBarCollapseStateChange(isCollapsed: Boolean) {
        _outAppbarStateFlow.value = _outAppbarStateFlow.value.copy(isCollapsed = isCollapsed)
    }

    fun notifyAppBarTotalExpandStateChange(isTotalExpanded: Boolean) {
        _outAppbarStateFlow.value =
            _outAppbarStateFlow.value.copy(isTotalExpanded = isTotalExpanded)
    }

    /**
     * 构建收藏信息
     *
     * @param gradeItem 等级道具数据
     * @param grade 道具等级
     * @return 构建完成的收藏信息
     */
    private fun buildCollectionInfo(
        gradeItem: CollectionGradeItem,
        grade: Int
    ): JackarooCollectionInfo {
        val displayCount = 6
        val config = PropItemConfig::class.java.instance()
        val typeList = mutableListOf<JackarooCollectionType>()
        var totalCount = 0

        for (propGradeItem in gradeItem.props) {
            val collectionType = buildCollectionType(propGradeItem, grade, config, displayCount)

            if (collectionType != null) {
                typeList.add(collectionType)
                totalCount += collectionType.obtainedList.size + collectionType.lockedList.size
            }
        }

        return JackarooCollectionInfo(
            grade = gradeItem.grade,
            obtainedNum = gradeItem.obtainedNum,
            totalNum = totalCount,
            typeList = typeList
        )
    }

    /**
     * 构建单个收藏类型的数据
     *
     * @param propGradeItem 道具等级项目
     * @param grade 道具等级
     * @param config 道具配置
     * @param displayCount 显示数量限制
     * @return 收藏类型数据，如果没有道具则返回null
     */
    private fun buildCollectionType(
        propGradeItem: CollectionGradePropItem,
        grade: Int,
        config: PropItemConfig,
        displayCount: Int
    ): JackarooCollectionType? {
        val allPropItems = GameCareerCache.getPropItemList(propGradeItem.type, grade)

        val obtainedPropIdSet = propGradeItem.obtainedPropIds.toSet()

        val obtainedList = propGradeItem.obtainedPropIds.mapNotNull { propId ->
            config.getPropItem(propId)
        }

        val lockedList = allPropItems
            .asSequence()
            .filter { propItem ->
                !obtainedPropIdSet.contains(propItem.itemId) && propItem.vipLevel < LIMIT_VIP_LEVEL
            }.toList()

        if (obtainedList.isEmpty() && lockedList.isEmpty()) {
            return null
        }

        // 构建显示列表
        val showList = buildDisplayList(
            typeName = propGradeItem.typeName,
            type = propGradeItem.type,
            obtainedList = obtainedList,
            lockedList = lockedList,
            displayCount = displayCount
        )

        return JackarooCollectionType(
            type = propGradeItem.type,
            name = propGradeItem.typeName,
            showList = showList,
            obtainedList = obtainedList,
            lockedList = lockedList
        )
    }

    /**
     * 构建显示列表
     *
     * @param typeName 类型名称
     * @param type 类型ID
     * @param obtainedList 已获得道具列表
     * @param lockedList 未获得道具列表
     * @param displayCount 显示数量限制
     * @return 显示项目列表
     */
    private fun buildDisplayList(
        typeName: String,
        type: Int,
        obtainedList: List<PropItem>,
        lockedList: List<PropItem>,
        displayCount: Int
    ): List<IJackarooCollection> {
        val totalSize = obtainedList.size + lockedList.size
        val obtainedDisplayCount = minOf(obtainedList.size, displayCount)
        val lockedDisplayCount = minOf(lockedList.size, displayCount - obtainedDisplayCount)

        return buildList {
            // 添加标题
            add(JackarooCollectionTitle(typeName))

            // 添加已获得的道具（最多6个）
            obtainedList.take(obtainedDisplayCount).mapTo(this) { propItem ->
                JackarooCollectionContent(propItem, locked = false)
            }

            // 添加未获得的道具（最多剩余位置个）
            lockedList.take(lockedDisplayCount).mapTo(this) { propItem ->
                JackarooCollectionContent(propItem, locked = true)
            }

            // 如果总数超过显示限制，添加"更多"项目
            if (totalSize > displayCount) {
                add(JackarooCollectionMore(type))
            }
        }
    }

    private fun onShowCollectionMore(type: Int) {
        viewModelScope.launch {
            val value = _collectionTypeFlow.value
            val newTypeList = value.typeList.map { t ->
                if (t.type == type) {
                    val list = buildList {
                        add(JackarooCollectionTitle(t.name))
                        addAll(t.obtainedList.map { JackarooCollectionContent(it, false) })
                        addAll(t.lockedList.map { JackarooCollectionContent(it, true) })
                    }
                    t.copy(showList = list)
                } else {
                    t
                }
            }
            _collectionTypeFlow.emit(value.copy(typeList = newTypeList))
        }
    }

    internal fun doShare(
        context: FragmentActivity,
        binding: JackarooGameRecordActivityBinding,
        gameCareerTabIndex: Int,
        gameRecordTabIndex: Int,
        gameRecordTabSubIndex: Int,
    ) {
        val currentTabInfo: GameCareerTabInfo =
            buildTabInfo(gameCareerTabIndex, gameRecordTabIndex, gameRecordTabSubIndex)
        val info = recordInfoLiveData.value ?: return
        val holder =
            JackarooGameCareerShareViewHolder(context, binding.jackarooGameRecordShareLay)
        holder.init(
            _userInfoStateFlow.value,
            GameCareerInfo(
                jackarooGameInfo = info.rankStat,
                collectionAlbum = collectionAlbumFlow.value
            ),
            currentTabInfo
        )
        holder.binding.root.postAutoCancel(300) {
            val bitmap = holder.getBitmap() ?: return@postAutoCancel
            binding.jackarooGameRecordShareLay.removeAllViews()
            holder.clear()
            val shareInfo = ShareInfo()
            shareInfo.setTitle(ConfigHelper.getInstance().myShareTitle)
            shareInfo.content = ConfigHelper.getInstance().myShareDesc

            shareInfo.setShareContentType(ShareInfo.SHARE_CONTENT_TYPE_IMG)
            shareInfo.screenName = TrackScreenName.SHARE_PAGE
            shareInfo.scene =
                if (isGameRecordTab(gameCareerTabIndex)) TrackString.SCENE_USER_RECORD else TrackString.GAME_CAREER_COLLECTION
            shareInfo.gameType = -1
            shareInfo.setBitmap(bitmap)
            val shareApi = ApiService.of(IShareApi::class.java)
            shareApi.showPictureShareDialog(context, shareInfo, object : ShareCallback {
                override fun onShare(data: ShareResult): Boolean {
                    if (data.shareType == ShareType.saveBmp) {
                        ToastUtil.show(data.msg)
                    }
                    return false
                }
            })
        }
    }

    private fun buildTabInfo(
        gameCareerTabIndex: Int,
        gameRecordTabIndex: Int,
        gameRecordTabSubIndex: Int,
    ): GameCareerTabInfo {
        return if (isGameRecordTab(gameCareerTabIndex)) {
            GameCareerTabInfo.GameRecordInfo(gameRecordTabIndex, gameRecordTabSubIndex)
        } else {
            GameCareerTabInfo.CollectionTab
        }
    }

    private fun CollectionAlbum?.CollectionPageState(): CollectionPageState {
        return if (this == null) {
            CollectionPageState.Idle
        } else {
            CollectionPageState.LoadSuccess(
                headInfo = CollectionHeadInfo(
                    collectionValue,
                    collectionRank
                ), gradeOverview = items.map { it.toCollectionPageItem() })
        }
    }
}

data class AppbarState(
    val isCollapsed: Boolean,
    val isTotalExpanded: Boolean
)

sealed class JackarooGameCareerEvent {
    data class SwitchGameCareerTab(val tabIndex: Int) : JackarooGameCareerEvent()
    data class SwitchGameGameRecordTab(val tabIndex: Int) : JackarooGameCareerEvent()
    data class SelectGameType(val index: Int) : JackarooGameCareerEvent()

    data class ShowGradeDetail(val grade: Int) : JackarooGameCareerEvent()
}

internal sealed interface CollectionPageState {
    data object Idle : CollectionPageState
    data class LoadSuccess(
        val headInfo: CollectionHeadInfo,
        val gradeOverview: List<CollectionPageItem>
    ) : CollectionPageState

    data class LoadFail(val msg: String) : CollectionPageState
}

internal sealed interface GameCareerTabInfo {
    data object CollectionTab : GameCareerTabInfo
    data class GameRecordInfo(val index: Int, val subIndex: Int) : GameCareerTabInfo
}

sealed interface JackarooCollectionIntent {
    class ShowDialog(val level: Int) : JackarooCollectionIntent
    class ShowMore(val type: Int) : JackarooCollectionIntent
}