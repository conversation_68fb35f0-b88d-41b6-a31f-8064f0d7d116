package com.wejoy.jackaroo.record

import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.configservice.editionentity.liveData
import com.huiwan.configservice.model.PropItem
import com.huiwan.user.UserServiceKt
import com.huiwan.user.entity.User
import com.three.http.core.KtResult
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wejoy.jackaroo.api.getAchievement
import com.wejoy.jackaroo.api.getCollectionAlbum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

object GameCareerCache {
    private val tag = "GameCareerCache"

    @Volatile
    private var userRecordInfo: UserData<UserRecordInfo>? = null

    @Volatile
    private var collectionAlbum: UserData<CollectionAlbum>? = null

    @Volatile
    private var userInfo: UserData<User>? = null

    private val propItemCache = ConcurrentHashMap<Int, List<PropItem>>()

    init {
        PropItemConfig::class.liveData().observeForever {
            propItemCache.clear()
        }
    }

    fun clear() {
        userRecordInfo = null
        collectionAlbum = null
        userInfo = null
    }

    private fun <T> getOrUpdate(
        uid: Int,
        scope: CoroutineScope,
        userdata: UserData<T>?,
        userDataUpdater: (data: UserData<T>) -> Unit,
        dataFetcher: suspend () -> KtResult<T>
    ): StateFlow<T?> {
        val dataFlow = MutableStateFlow<T?>(null)
        scope.launch {
            if (userdata == null || userdata.uid != uid) {
                val ret = dataFetcher()
                if (ret !is KtResultSuccess) {
                    ToastUtil.show(ret.failedDesc)
                    return@launch
                } else {
                    userDataUpdater(UserData(uid, ret.data))
                    dataFlow.emit(ret.data)
                }
            } else {
                dataFlow.emit(userdata.data)
            }
        }
        return dataFlow.asStateFlow()
    }

    fun userRecordInfoFlow(uid: Int, scope: CoroutineScope): StateFlow<UserRecordInfo?> {
        return getOrUpdate(
            uid, scope, userRecordInfo,
            dataFetcher = { getAchievement(uid) },
            userDataUpdater = { userRecordInfo = it })
    }

    fun collectionAlbumFlow(uid: Int, scope: CoroutineScope): StateFlow<CollectionAlbum?> {
        return getOrUpdate(
            uid, scope, collectionAlbum,
            dataFetcher = { getCollectionAlbum(uid) },
            userDataUpdater = { collectionAlbum = it })
    }

    fun userFlow(uid: Int, scope: CoroutineScope): StateFlow<User?> {
        return getOrUpdate(
            uid, scope, userInfo, dataFetcher = {
                val user = UserServiceKt.get().getCacheUser(uid)
                if (user == null) {
                    KtResultFailed(-1, "fail to load user")
                } else {
                    KtResultSuccess(user)
                }
            }, userDataUpdater = { userInfo = it })
    }

    /**
     * 根据道具类型列表构建道具等级映射表
     *
     * 该方法将指定类型的道具按照等级和类型进行分组，生成一个映射表。
     * 映射表的key是通过位运算组合的复合键：(grade << 8) | type
     * 这样可以在一个整数中同时编码等级和类型信息，提高查找效率。
     *
     * @param types 需要处理的道具类型列表
     * @return 返回以复合键为key，道具列表为value的映射表
     *         key格式：高8位存储等级(grade)，低8位存储类型(type)
     */
    private fun getPropItemGradeMap(types: List<Int>): Map<Int, List<PropItem>> {
        val localMap = mutableMapOf<Int, MutableList<PropItem>>()

        PropItemConfig::class.instance().allPropItemList.forEach { propItem ->
            // 检查道具类型是否在指定列表中，并且有永久价格
            if (propItem.type in types &&
                propItem.getPriceList().firstOrNull { it.timeLimit == 0 } != null
            ) {
                // 从道具扩展信息中解析等级，默认为0
                val grade = propItem.parseExtAsInt(KEY_GRADE) ?: 0

                // 使用位运算生成复合键：等级左移8位后与类型进行或运算
                // 这样可以在一个int中同时存储等级和类型信息
                val compositeKey = grade shl 8 or propItem.type

                // 获取或创建对应键的道具列表，并添加当前道具
                val propList = localMap.getOrPut(compositeKey) { mutableListOf() }
                propList.add(propItem)
            }
        }

        // 对每个列表按itemId降序排序，时间复杂度O(n log n)，但只在构建时执行一次
        // 相比每次插入都维护PriorityQueue的O(n log n)总复杂度，这种方式更高效
        return localMap.mapValues { (_, propList) ->
            propList.sortedByDescending { it.itemId }
        }
    }

    /**
     * 获取指定类型和等级的道具列表
     *
     * 该方法使用缓存机制来提高性能，首先检查缓存中是否存在所需的道具列表。
     * 如果缓存中不存在，则通过getPropItemGradeMap方法生成并缓存结果。
     *
     * @param type 道具类型
     * @param grade 道具等级
     * @return 返回符合条件的道具列表，如果没有找到则返回空列表
     */
    fun getPropItemList(type: Int, grade: Int): List<PropItem> {
        val compositeKey = grade shl 8 or type

        propItemCache[compositeKey]?.let { cachedList ->
            return cachedList
        }

        val gradeMap = getPropItemGradeMap(listOf(type))
        propItemCache.putAll(gradeMap)
        return gradeMap[compositeKey] ?: emptyList()
    }

    /**
     * 访问指定等级和类型列表的所有道具
     *
     * 该方法采用访问者模式，对符合条件的道具执行指定的操作。
     * 使用智能缓存策略：优先从缓存中获取已有数据，只对缺失的类型进行数据加载。
     * 这种方式可以最大化利用缓存，减少不必要的计算开销。
     *
     * @param grade 道具等级
     * @param types 道具类型列表
     * @param visitor 访问者函数，对每个道具执行的操作
     */
    fun visitPropItem(grade: Int, types: List<Int>, visitor: (PropItem) -> Unit) {
        val propListMap = mutableMapOf<Int, List<PropItem>>()

        val uncachedTypes = mutableListOf<Int>()

        types.forEach { type ->
            val compositeKey = grade shl 8 or type
            val cachedList = propItemCache[compositeKey]

            if (cachedList != null) {
                propListMap[compositeKey] = cachedList
            } else {
                uncachedTypes.add(type)
            }
        }

        if (uncachedTypes.isNotEmpty()) {
            val newGradeMap = getPropItemGradeMap(uncachedTypes)
            propItemCache.putAll(newGradeMap)

            // 将新生成的道具列表添加到本次访问的映射表中
            uncachedTypes.forEach { type ->
                val compositeKey = grade shl 8 or type
                propListMap[compositeKey] = newGradeMap[compositeKey] ?: emptyList()
            }
        }

        propListMap.values.forEach { propList ->
            propList.forEach(visitor)
        }
    }

    //道具 的 extraJsonString 中ItemGrade字段的key
    const val KEY_GRADE = "item_grade"

    private fun PropItem.parseExtAsInt(key: String): Int? {
        kotlin.runCatching {
            return parseExtraPrimitive(key)?.toInt()
        }
        return null
    }
}

private data class UserData<T>(
    val uid: Int,
    val data: T,
)