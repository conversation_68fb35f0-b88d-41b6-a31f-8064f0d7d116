package com.wejoy.jackaroo.record

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.huiwan.base.ktx.dp
import com.huiwan.base.util.ColorUtil
import com.huiwan.configservice.constentity.JKHomeConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.widget.CornerProgressBar
import com.huiwan.widget.image.DrawableUtil
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.databinding.CollectionGradeViewBinding

class CollectionGradeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val binding: CollectionGradeViewBinding = CollectionGradeViewBinding.inflate(
        LayoutInflater.from(context), this
    )

    init {
        binding.progressBar.init()
    }

    private fun CornerProgressBar.init() {
        background =
            DrawableUtil.genColorRadius(Color.parseColor("#FFE6E7EC"), 12)
        corner = 12.dp
    }

    fun update(grade: Int, owedNum: Int, totalNum: Int) {
        JKHomeConfig::class.instance().collectionGradeConfig[grade]?.let {
            WpImageLoader.load(
                it.gradeIconUrl,
                binding.collectionGradeIv,
                ImageLoadInfo.newInfo().width(32.dp).height(32.dp)
            )
            binding.progressBar.progressSolidColor = ColorUtil.getColor(
                it.progressColor
            )
        }
        val progress = if (totalNum == 0) {
            0f
        } else if (totalNum <= owedNum) {
            1f
        } else {
            owedNum.toFloat() / totalNum
        }
        binding.progressBar.progress = progress
        binding.ownedNumTv.text = owedNum.toString()
        binding.totalTv.text = "/$totalNum"

    }

}