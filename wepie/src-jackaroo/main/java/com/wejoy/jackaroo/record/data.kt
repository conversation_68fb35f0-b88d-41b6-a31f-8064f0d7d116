package com.wejoy.jackaroo.record

import com.google.gson.annotations.SerializedName
import com.huiwan.base.ktx.isWideScreen
import com.huiwan.configservice.constentity.CollectionGradeConfigItem
import com.huiwan.configservice.constentity.JKHomeConfig
import com.huiwan.configservice.constentity.propextra.JackarooSkinExtra
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.configservice.model.PropItem

data class CollectionAlbum(
    @SerializedName("chessman_in_use")
    val inUseChessPropId: Int = 0,
    //    总的已获得道具数
    @SerializedName("obtained_num")
    val totalObtainedNum: Int = 0,
    // 收藏值
    @SerializedName("collection_value")
    val collectionValue: Int = 0,
    //收藏榜排名
    @SerializedName("collection_rank")
    val collectionRank: Int = 0,
    @SerializedName("collections")
    val items: List<CollectionGradeItem> = emptyList()
) {
    fun calTotalCollectionNum(): Int {
        return items.fold(0) { acc, item -> acc + item.calGradeTotalNum() }
    }
}

data class CollectionGradeItem(
    @SerializedName("item_grade")
    val grade: Int = 0,
    @SerializedName("item_grade_name")
    val gradeName: String = "",
    //该等级已获得道具数
    @SerializedName("obtained_num")
    val obtainedNum: Int = 0,
    //在收藏册tab，预览的最多5个道具
    @SerializedName("grade_props")
    val overViewObtainedIdList: List<Int> = emptyList(),
    @SerializedName("props")
    val props: List<CollectionGradePropItem> = emptyList()
) {
    companion object {
        //外部预览时最多展示的道具数量
        val MAX_OVERVIEW_PROP_NUM = if (!isWideScreen) {
            5
        } else {
            9
        }
    }

    @Transient
    private var totalNum = 0

    fun calGradeTotalNum(): Int {
        if (totalNum > 0) {
            return totalNum
        }
        val propIdSet = mutableSetOf<Int>()
        val obtainedIdSet = props.flatMap { it.obtainedPropIds }.toSet()
        GameCareerCache.visitPropItem(grade, props.map { it.type }) {
            if (it.vipLevel < LIMIT_VIP_LEVEL) {
                propIdSet.add(it.itemId)
            }
        }
        propIdSet.addAll(obtainedIdSet)
        totalNum = propIdSet.size
        return totalNum
    }

    fun getNotObtainedLargestNId(n: Int): List<Int> {
        if (n <= 0) {
            return emptyList()
        }
        val notObtainedIdList = mutableListOf<Int>()
        props.forEach { item ->
            val c = GameCareerCache.getPropItemList(item.type, grade).filter {
                it.vipLevel < LIMIT_VIP_LEVEL
            }
            notObtainedIdList.addAll(c.take(n).map { it.itemId })
        }
        return notObtainedIdList.sortedByDescending { it }.take(n)
    }
}

data class CollectionGradePropItem(
    /**
     * 道具类型
     */
    @SerializedName("type")
    val type: Int = 0,
    /**
     * 该类型道具名称  比如 棋子，出牌特效
     */
    @SerializedName("type_name")
    val typeName: String = "",
    @SerializedName("obtained_props")
    val obtainedPropIds: List<Int> = emptyList()
)

const val LIMIT_VIP_LEVEL = 9

data class GameCareerInfo(
    val jackarooGameInfo: JackarooGameInfo? = null,
    val collectionAlbum: CollectionAlbum? = null
)

internal data class CollectionHeadInfo(
    //收藏值
    val collectionValue: Int,
    //收藏榜排名
    val collectionRank: Int
)


fun getChessGradeConfig(inUseChessPropId: Int): CollectionGradeConfigItem? {
    val grade = PropItemConfig::class.java.instance().getPropItem(inUseChessPropId)
        ?.getExtraByType(JackarooSkinExtra::class.java)?.itemGrade ?: -1
    if (grade < 0) {
        return null
    } else {
        return JKHomeConfig::class.java.instance().collectionGradeConfig[grade]
    }
}

data class JackarooCollectionInfo(
    val grade: Int = 0,
    val obtainedNum: Int = 0,
    val totalNum: Int = 0,
    val typeList: List<JackarooCollectionType> = listOf()
)

data class JackarooCollectionType(
    val type: Int,
    val name: String,
    val showList: List<IJackarooCollection> = emptyList(),
    val obtainedList: List<PropItem> = emptyList(),
    val lockedList: List<PropItem> = emptyList()
)

internal data class CollectionPropItemData(
    val isEmpty: Boolean,
    val propModel: Any,
    val propName: String,
    val hasObtained: Boolean,
)