package com.wejoy.jackaroo.level

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.content.Context
import android.content.DialogInterface
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.PathInterpolator
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.PressUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.SoundPoolUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.constants.GameType
import com.huiwan.decorate.JackarooLevelViewHolder
import com.huiwan.lib.api.ApiService
import com.huiwan.user.entity.UserUpgradeInfoLevel
import com.huiwan.user.entity.UserUpgradeInfoReward
import com.huiwan.user.http.UserApiKt
import com.huiwan.widget.HWEffectTextView
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.three.http.core.KtResultFailed
import com.wejoy.weplay.ex.animator.startSuspend
import com.wepie.lib.api.plugins.share.IShareApi
import com.wepie.lib.api.plugins.share.ShareCallback
import com.wepie.lib.api.plugins.share.ShareInfo
import com.wepie.lib.api.plugins.share.ShareResult
import com.wepie.lib.api.plugins.share.ShareType
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import org.jetbrains.annotations.TestOnly

class JackarooLevelUpDialogFragment : BaseDialogFragment() {

    private var continuation: CancellableContinuation<Int>? = null
    private var level: UserUpgradeInfoLevel = UserUpgradeInfoLevel()

    private lateinit var levelView: ViewGroup
    private lateinit var nextLevelLay: ViewGroup

    private var isFirst = false

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.jackaroo_level_up_dialog_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isFirst = true
        updateCancelable(false)
        WpImageLoader.load(
            WpImageLoader.getAssetUri("svga/jackaroo_level_up_bg_anim.svga"),
            view.findViewById(R.id.jackaroo_level_up_bg_anim_iv)
        )
        WpImageLoader.load(
            WpImageLoader.getAssetUri("svga/jackaroo_level_up_top_anim.svga"),
            view.findViewById(R.id.jackaroo_level_up_top_anim_iv)
        )
        WpImageLoader.load(
            ResUtil.getStr(R.string.jackaroo_level_up_dialog_bg_url),
            view.findViewById(R.id.jackaroo_level_up_bg_iv),
            ImageLoadInfo.newInfo().placeholder(R.drawable.jackaroo_level_up_dialog_bg)
        )
        levelView = view.findViewById(R.id.jackaroo_level_view)
        JackarooLevelViewHolder(levelView).setLevel(level.toLevel)

        view.findViewById<TextView>(R.id.jackaroo_level_up_reward_desc_tv)?.apply {
            isVisible = level.toLevel - level.fromLevel > 1
            text = ResUtil.getStr(R.string.jackaroo_level_up_desc, level.fromLevel, level.toLevel)
        }

        view.findViewById<HWEffectTextView>(R.id.jackaroo_level_up_ok_tv)?.apply {
            initButton(this) { getRewards() }
        }
        view.findViewById<HWEffectTextView>(R.id.jackaroo_level_up_share_tv)?.apply {
            initButton(this) { doShare() }
        }

        view.findViewById<RecyclerView>(R.id.jackaroo_level_up_reward_rv)?.apply {
            initRewardListView(this)
        }
        view.findViewById<RecyclerView>(R.id.jackaroo_level_up_next_reward_rv)?.apply {
            initRewardListView(this, true)
        }
        nextLevelLay = view.findViewById(R.id.jackaroo_level_up_next_reward_lay)
    }

    private fun initButton(view: HWEffectTextView, click: suspend () -> Unit) {
        PressUtil.addPressEffect(view)
        view.setOnClickListener { lifecycleScope.launch { click.invoke() } }
    }

    private fun initRewardListView(rv: RecyclerView, isNextLevelReward: Boolean = false) {
        val itemPadding: Int
        val iconBgRes: Int
        val textColor: Int
        val data: List<UserUpgradeInfoReward>
        if (isNextLevelReward) {
            itemPadding = ScreenUtil.dip2px(32F)
            iconBgRes = R.drawable.jackaroo_next_level_reward_item_bg
            textColor = Color.WHITE
            data = level.nextLevelRewards
        } else {
            itemPadding = ScreenUtil.dip2px(16F)
            iconBgRes = R.drawable.jackaroo_level_reward_item_bg
            textColor = ColorUtil.getColor("#6D4D38")
            data = level.currentLevelRewards
        }

        val dp4 = ScreenUtil.dip2px(4F)
        rv.layoutManager = LinearLayoutManager(requireContext(), RecyclerView.HORIZONTAL, false)
        rv.addItemDecoration(
            SpaceItemDecoration(
                Rect(itemPadding, 0, 0, 0), Rect(dp4, 0, dp4, 0)
            )
        )
        rv.adapter = JackarooLevelRewardAdapter(data, iconBgRes, textColor)
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setDimAmount(0.8F)
        if (isFirst) {
            showAnim()
            isFirst = false
        }
    }

    private fun showAnim() {
        SoundPoolUtil.getInstance().playRaw(R.raw.jackaroo_level_up)
        lifecycleScope.launch {
            val lay: View = requireView()
            dialogScaleAnim(lay, object : AnimatorListener {
                override fun onAnimationStart(animation: Animator) = Unit

                override fun onAnimationEnd(animation: Animator) {
                    onEnterAnimEnd()
                }

                override fun onAnimationCancel(animation: Animator) = Unit

                override fun onAnimationRepeat(animation: Animator) = Unit
            })
        }
    }

    private suspend fun dialogScaleAnim(lay: View, animatorListener: AnimatorListener? = null) {
        val updateListener = ValueAnimator.AnimatorUpdateListener {
            val s = it.animatedValue as Float
            lay.scaleX = s
            lay.scaleY = s
        }

        val animator1 = ValueAnimator.ofFloat(0.3f, 1.1f)
        animator1.addUpdateListener(updateListener)
        animator1.interpolator = PathInterpolator(0.25F, 0.00F, 0.29F, 1.00F)
        animator1.duration = 160

        val animator2 = ValueAnimator.ofFloat(1.1f, 1f)
        animator2.addUpdateListener(updateListener)
        animator2.interpolator = PathInterpolator(0.33F, 0.00F, 0.67F, 1.00F)
        animator2.duration = 170

        val set = AnimatorSet()
        set.playSequentially(animator1, animator2)
        animatorListener?.let {
            set.addListener(it)
        }
        set.startSuspend()
    }

    private fun onEnterAnimEnd() {
        nextLevelLay.isVisible = level.currentLevelRewards.isNotEmpty()
        lifecycleScope.launch {
            dialogScaleAnim(levelView, object : AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                    levelView.isVisible = true
                }

                override fun onAnimationEnd(animation: Animator) = Unit

                override fun onAnimationCancel(animation: Animator) = Unit

                override fun onAnimationRepeat(animation: Animator) = Unit
            })
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        continuation?.resumeWith(Result.success(1))
    }

    private suspend fun getRewards() {
        val result = UserApiKt.receiveLevelReward(level.toLevel)
        if (result is KtResultFailed) {
            ToastUtil.show(result.failedDesc)
        }
        dismissAllowingStateLoss()
    }

    private suspend fun doShare() {
        val shareInfo = ShareInfo()
        shareInfo.setTitle(ConfigHelper.getInstance().myShareTitle)
        shareInfo.content = ConfigHelper.getInstance().myShareDesc

        shareInfo.setShareContentType(ShareInfo.SHARE_CONTENT_TYPE_IMG)
        shareInfo.screenName = TrackScreenName.SHARE_PAGE
        shareInfo.scene = TrackString.SCENE_LEVEL_UP
        shareInfo.gameType = GameType.GAME_TYPE_JACKAROO
        val bgColor = ContextCompat.getColor(levelView.context, R.color.main_activity_bg)
        shareInfo.picBgDrawable = ColorDrawable(bgColor)

        val levelBitmap =
            Bitmap.createBitmap(levelView.width, levelView.height, Bitmap.Config.ARGB_8888)
        levelView.draw(Canvas(levelBitmap))
        val holder = JackarooLevelUpShareViewHolder(requireContext(), level, levelBitmap)

        delay(150)
        val bitmap = holder.getBitmap()
        shareInfo.setBitmap(bitmap)
        val shareApi = ApiService.of(IShareApi::class.java)
        shareApi.showPictureShareDialog(context, shareInfo, object : ShareCallback {
            override fun onShare(data: ShareResult): Boolean {
                if (data.shareType == ShareType.saveBmp) {
                    ToastUtil.show(data.msg)
                }
                return false
            }
        })

    }

    private suspend fun showDialog(manager: FragmentManager) = suspendCancellableCoroutine {
        show(manager, "")
        continuation = it
    }

    companion object {

        init {
            WpImageLoader.downloadOnly(
                ResUtil.getStr(R.string.jackaroo_level_up_share_bg_url),
                ImageLoadInfo.newInfo(),
                null
            )
        }

        @JvmStatic
        fun show(
            context: Context,
            popups: List<UserUpgradeInfoLevel>,
            listener: DialogInterface.OnDismissListener? = null
        ) {
            val activity = ContextUtil.getFragmentActivityFromContext(context) ?: return
            activity.lifecycleScope.launch {
                popups.forEach {
                    val fragment = JackarooLevelUpDialogFragment()
                    fragment.initFullScreen()
                    fragment.initNormalStyle()
                    fragment.level = it
                    fragment.showDialog(activity.supportFragmentManager)
                }
                listener?.onDismiss(null)
            }
        }

        @Suppress("unused")
        @TestOnly
        @JvmStatic
        fun showTest(context: Context) {
            show(
                context, listOf(
                    UserUpgradeInfoLevel(
                        10, 11,
                        listOf(
                            UserUpgradeInfoReward(1706635, 7),
                            UserUpgradeInfoReward(1702955, 7)
                        ),
                        listOf(
                            UserUpgradeInfoReward(1706635, 7)
                        )
                    )
                )
            )
        }
    }
}