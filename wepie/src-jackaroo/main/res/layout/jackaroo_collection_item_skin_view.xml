<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="12dp">

    <FrameLayout
        android:id="@+id/jackaroo_skin_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="#E6E7EC">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/jackaroo_skin_iv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:scaleType="fitXY"
            tools:layout_height="108dp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/jackaroo_skin_lock_iv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black_alpha30"
            android:scaleType="center"
            android:visibility="gone"
            app:srcCompat="@drawable/jackaroo_skin_lock" />

    </FrameLayout>

    <TextView
        android:id="@+id/jackaroo_skin_name_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxWidth="108dp"
        android:maxLines="1"
        android:textColor="@color/color_text_secondary"
        android:textSize="14dp"
        tools:text="121231231231233" />

</androidx.appcompat.widget.LinearLayoutCompat>