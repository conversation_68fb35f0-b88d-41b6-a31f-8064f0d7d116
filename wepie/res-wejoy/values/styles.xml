<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="setting_common_layout_bg">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@drawable/setting_item_bg</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="setting_common_text_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_marginStart">16dp</item>
        <item name="android:textColor">@color/color_text_accent_dark</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="setting_common_right_arrow_style">
        <item name="android:layout_width">wrap_content</item><!--11dp-->
        <item name="android:layout_height">wrap_content</item><!--11dp-->
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginEnd">16dp</item>
        <item name="srcCompat">@drawable/forward_icon_new</item>
        <item name="android:scaleX">@integer/image_scale_x</item>
    </style>

    <style name="setting_common_btn_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/wp56</item>
        <item name="android:background">@drawable/setting_item_bg</item>
        <item name="android:gravity">center</item>
        <item name="android:textAlignment">gravity</item>
        <item name="android:layout_marginBottom">26dp</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:text">@string/activity_setting_sign_out</item>
        <item name="android:textColor">@color/color_text_accent_dark</item>
        <item name="android:textSize">16sp</item>
    </style>

    <declare-styleable name="VoiceEnterNameView">
        <attr name="isVip" format="boolean"/>
    </declare-styleable>

<!--    android:layout_marginStart="12dp"-->
<!--    android:layout_marginEnd="12dp"-->
</resources>