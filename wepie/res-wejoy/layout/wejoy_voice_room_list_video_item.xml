<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="20dp">

    <com.huiwan.widget.BorderImageView
        android:id="@+id/video_cover_iv"
        android:layout_width="114dp"
        android:layout_height="64dp"
        android:layout_marginStart="20dp"
        app:cornerRadius="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/wejoy_default_video_cover" />

    <com.huiwan.widget.BorderImageView
        android:id="@+id/room_owner_area_iv"
        android:layout_width="18dp"
        android:layout_height="14dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="13dp"
        app:borderColor="#DADAE0"
        app:borderWidth="0.5dp"
        app:cornerRadius="2dp"
        app:layout_constraintStart_toEndOf="@+id/video_cover_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/voice_room_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="6dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:textAlignment="viewStart"
        android:textColor="@color/main_activity_text_primary"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/voice_room_marking_icon"
        app:layout_constraintStart_toEndOf="@+id/room_owner_area_iv"
        app:layout_constraintTop_toTopOf="@+id/video_cover_iv"
        app:layout_goneMarginEnd="20dp"
        app:layout_goneMarginStart="12dp"
        tools:text="Million heroes 19:00 games" />

    <ImageView
        android:id="@+id/voice_room_marking_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="20dp"
        android:maxHeight="20dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#300f"
        tools:layout_height="18dp"
        tools:layout_width="60dp"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/video_room_online_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toBottomOf="@+id/video_cover_iv"
        app:layout_constraintStart_toEndOf="@+id/video_cover_iv"
        app:srcCompat="@drawable/ic_room_online_count"
        app:tint="@color/main_activity_text_secondary" />

    <TextView
        android:id="@+id/video_room_online_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/main_activity_text_secondary"
        android:textSize="@dimen/wp12"
        app:layout_constraintBottom_toBottomOf="@+id/video_room_online_iv"
        app:layout_constraintStart_toEndOf="@+id/video_room_online_iv"
        app:layout_constraintTop_toTopOf="@+id/video_room_online_iv"
        tools:text="1111" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/video_room_video_iv"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toBottomOf="@+id/video_cover_iv"
        app:layout_constraintStart_toEndOf="@+id/video_room_online_tv"
        app:srcCompat="@drawable/ic_video_room" />

    <com.huiwan.widget.MarqueeTextView
        android:id="@+id/video_room_label_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="20dp"
        android:ellipsize="marquee"
        android:gravity="start|center_vertical"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:textAlignment="viewStart"
        android:textColor="@color/main_activity_text_secondary"
        android:textSize="@dimen/wp12"
        app:layout_constraintBottom_toBottomOf="@+id/video_room_video_iv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/video_room_video_iv"
        app:layout_constraintTop_toTopOf="@+id/video_room_video_iv"
        tools:text="小黄和他的朋友" />

    <include
        layout="@layout/translate_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="84dp"
        app:layout_constraintTop_toBottomOf="@+id/video_cover_iv" />

</androidx.constraintlayout.widget.ConstraintLayout>