<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:background="@color/color_setting_bg"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/lang_tv"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="20dp"
        android:gravity="center_vertical|start"
        android:textColor="@color/color_text_accent_dark"
        android:textSize="16sp"
        tools:text="@string/wp_lang" />

    <Space
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/select_iv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="20dp"
        android:scaleType="centerInside"
        android:src="@drawable/green_check_icon"
        app:tint="@color/color_accent_ex" />

</LinearLayout>