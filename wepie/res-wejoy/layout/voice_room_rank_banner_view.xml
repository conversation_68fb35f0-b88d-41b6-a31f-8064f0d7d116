<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingDefaultResource"
    tools:layout_height="98dp">

    <ImageView
        android:id="@+id/voice_room_rank_banner_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:src="@drawable/voice_room_rank_banner_pic"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/voice_room_rank_thirdly_avatar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/shape_99ffffff_corner12"
        android:padding="1dp"
        android:src="@drawable/default_head_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.41"
        app:layout_constraintHorizontal_bias="0.965"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.67"
        app:layout_constraintWidth_percent="0.12" />

    <ImageView
        android:id="@+id/voice_room_rank_first_avatar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/shape_99ffffff_corner12"
        android:padding="1dp"
        android:src="@drawable/default_head_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.41"
        app:layout_constraintHorizontal_bias="0.79"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.23"
        app:layout_constraintWidth_percent="0.12" />

    <ImageView
        android:id="@+id/voice_room_rank_second_avatar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/shape_99ffffff_corner12"
        android:padding="1dp"
        android:src="@drawable/default_head_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.41"
        app:layout_constraintHorizontal_bias="0.625"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.53"
        app:layout_constraintWidth_percent="0.12" />

</androidx.constraintlayout.widget.ConstraintLayout>