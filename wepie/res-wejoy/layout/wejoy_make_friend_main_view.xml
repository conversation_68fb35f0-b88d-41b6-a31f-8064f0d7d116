<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:background="@color/white"
    tools:parentTag="com.wejoy.weplay.module.makefriend.WejoyMakeFriendMainView">

    <View
        android:id="@+id/status_bar_holder"
        android:layout_width="1dp"
        android:layout_height="1dp" />

    <View
        android:id="@+id/title_guide_line"
        android:layout_width="1dp"
        android:layout_height="1dp"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/wejoy_make_friend_tab_title_height"
        app:layout_constraintTop_toBottomOf="@id/status_bar_holder" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/create_room_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:padding="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/title_guide_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/status_bar_holder"
        app:srcCompat="@drawable/wejoy_action_bar_create_room" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/search_room_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:padding="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/title_guide_line"
        app:layout_constraintEnd_toStartOf="@+id/create_room_imv"
        app:layout_constraintTop_toBottomOf="@id/status_bar_holder"
        app:srcCompat="@drawable/wejoy_action_bar_search" />

    <com.huiwan.widget.WejoyLabelLayout
        android:id="@+id/room_type_label_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="8dp"
        android:clipChildren="false"
        app:layout_constraintBottom_toBottomOf="@+id/title_guide_line"
        app:layout_constraintEnd_toStartOf="@+id/search_room_imv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/status_bar_holder"
        app:tabGravity="start"
        app:tabIndicator="@null"
        app:tabMinWidth="0dp"
        app:tabMode="scrollable"
        app:tabPaddingEnd="10dp"
        app:tabPaddingStart="10dp"
        app:tabRippleColor="@null" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/wejoy_voice_room_type_vp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_guide_line" />

    <FrameLayout
        android:id="@+id/room_create_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/create_room_imv"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/room_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginEnd="8dp"
            android:background="@drawable/voice_room_create_bg"
            android:paddingTop="6dp"
            android:paddingBottom="14dp"
            tools:itemCount="3"
            tools:listitem="@layout/popup_voice_room_item" />

    </FrameLayout>

    <TextView
        android:id="@+id/show_advance_tips_tv"
        android:layout_width="170dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="14dp"
        android:background="@drawable/icon_show_enter_superior_room_tips_bg"
        android:gravity="center"
        android:paddingStart="8dp"
        android:paddingTop="12dp"
        android:paddingEnd="8dp"
        android:paddingBottom="10dp"
        android:text="@string/create_room_tips"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/status_bar_holder"
        tools:visibility="visible" />

</merge>