<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/empty_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="48dp"
        android:src="@drawable/base_empty_no_follow_room" />

    <TextView
        android:id="@+id/empty_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="16dp"
        android:text="@string/voice_room_list_view_1"
        android:textColor="#BD987E"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/go_to_voice_room_tv"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="24dp"
        android:background="@drawable/sel_24c572_corner20"
        android:backgroundTint="@color/sel_accent_ex_accent_gray"
        android:gravity="center"
        android:paddingHorizontal="38dp"
        android:text="@string/make_friend_goto_room"
        android:textColor="@color/white"
        android:textSize="@dimen/wp15" />

</LinearLayout>