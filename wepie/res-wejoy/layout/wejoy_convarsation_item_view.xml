<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:orientation="vertical"
    tools:parentTag="android.widget.LinearLayout">

    <View
        android:id="@+id/top_paddimg"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="#f7f7f7"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/notification_open_lay"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:background="#f7fffa"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:text="@string/conversation_room_list_item_1"
            android:textColor="@color/color_accent"
            android:textSize="12dp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/close_lay_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            app:srcCompat="@drawable/notification_lay_close_btn"
            app:tint="@color/color_accent" />

        <TextView
            android:layout_width="60dp"
            android:layout_height="24dp"
            android:layout_centerVertical="true"
            android:layout_marginEnd="12dp"
            android:layout_toStartOf="@id/close_lay_btn"
            android:background="@drawable/sel_stroke_accent_corner100"
            android:gravity="center"
            android:text="@string/go_set"
            android:textAlignment="center"
            android:textColor="@color/color_accent" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/con_item_lay"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:background="@drawable/msg_center_item_simple_bg">

        <com.huiwan.widget.CustomCircleImageView
            android:id="@+id/con_item_head_img"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:scaleType="centerCrop" />

        <com.huiwan.widget.CircleFrameLayout
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            app:cf_radius="24dp">

            <com.wepie.wespy.module.chat.ui.adapter.ConGroupHeadView
                android:id="@+id/con_item_group_head"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/shape_dddee0_corner6"
                android:clipChildren="true"
                android:clipToPadding="true"
                android:visibility="gone" />

        </com.huiwan.widget.CircleFrameLayout>

        <!-- 消息数量 -->
        <TextView
            android:id="@+id/con_item_msg_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/con_item_head_img"
            android:layout_alignEnd="@id/con_item_head_img"
            android:layout_marginTop="-5dp"
            android:layout_marginEnd="-5dp"
            android:background="@drawable/large_red_dot"
            android:gravity="center"
            android:textAlignment="center"
            android:textColor="#ffffff"
            android:textSize="12dp" />

        <ImageView
            android:id="@+id/con_item_small_dot"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_alignTop="@id/con_item_head_img"
            android:layout_alignEnd="@id/con_item_head_img"
            android:layout_marginTop="-4dp"
            android:layout_marginEnd="-4dp"
            android:background="@drawable/small_red_dot" />

        <!-- 消息时间 -->
        <TextView
            android:id="@+id/con_item_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/con_item_head_img"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="2.5dp"
            android:layout_marginEnd="16dp"
            android:textColor="@color/main_activity_text_secondary"
            android:textSize="12dp"
            tools:text="12:34" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/con_item_name_layout"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:layout_alignTop="@id/con_item_head_img"
            android:layout_marginStart="12dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="8dp"
            android:layout_toStartOf="@id/con_item_time"
            android:layout_toEndOf="@id/con_item_head_img"
            android:clipChildren="false"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/con_name_icon"
                android:layout_width="wrap_content"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="4dp"
                android:background="@drawable/group_family_icon_bg"
                android:drawableStart="@drawable/group_family_icon"
                android:drawablePadding="2dp"
                android:gravity="center_vertical"
                android:paddingStart="2dp"
                android:paddingEnd="4dp"
                android:text="@string/common_family_text"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/con_item_name"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- 发消息人名字 -->
            <com.huiwan.decorate.NameTextView
                android:id="@+id/con_item_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:textAlignment="viewStart"
                android:textColor="@color/main_activity_text_primary"
                android:textSize="16dp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/con_tag"
                app:layout_constraintStart_toEndOf="@id/con_name_icon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="111111111111" />

            <TextView
                android:id="@+id/con_tag"
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingStart="4dp"
                android:paddingTop="1dp"
                android:paddingEnd="4dp"
                android:paddingBottom="1dp"
                android:textSize="11dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/con_item_name"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <RelativeLayout
            android:id="@+id/con_item_game_right_lay"
            android:layout_width="wrap_content"
            android:layout_height="23dp"
            android:layout_alignBottom="@id/con_item_head_img"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="1.5dp">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="bottom"
                android:layout_marginBottom="3dp"
                android:layout_toEndOf="@+id/con_item_game_state_lay">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/con_item_notify_img"
                    android:layout_width="14dp"
                    android:layout_height="13dp"
                    android:layout_marginStart="4dp"
                    android:visibility="gone"
                    app:srcCompat="@drawable/group_notify_off"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/group_team_status_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#2AB85A"
                    android:textSize="11dp"
                    android:visibility="gone"
                    tools:text="八分音符组队中" />

            </FrameLayout>

            <LinearLayout
                android:id="@+id/con_item_game_state_lay"
                android:layout_width="wrap_content"
                android:layout_height="23dp"
                android:background="@drawable/wejoy_shape_playing_24c572"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:visibility="visible">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/con_item_state_lock_ic"
                    android:layout_width="10dp"
                    android:layout_height="12dp"
                    android:layout_marginEnd="4dp"
                    android:visibility="gone"
                    app:srcCompat="@drawable/icon_locked"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/con_item_state_tx"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="2dp"
                    android:gravity="center"
                    android:maxLines="1"
                    android:paddingBottom="1dp"
                    android:textAlignment="center"
                    android:textColor="#FF24C572"
                    android:textSize="11dp"
                    tools:text="Playing who\'s the spy" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/con_item_state_arrow_ic"
                    android:layout_width="6dp"
                    android:layout_height="10dp"
                    android:scaleType="center"
                    android:scaleX="@integer/image_scale_x"
                    tools:srcCompat="@drawable/arrow_green" />

            </LinearLayout>

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignStart="@id/con_item_name_layout"
            android:layout_alignBottom="@id/con_item_head_img"
            android:layout_toStartOf="@id/con_item_game_right_lay"
            android:gravity="center_vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/msg_send_text_fail"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="top"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="4dp"
                android:visibility="gone"
                app:srcCompat="@drawable/chat_send_fail"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/at_me_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="4dp"
                android:layout_marginBottom="3dp"
                android:text="@string/convarsation_item_view_4"
                android:textColor="#FF4646"
                android:textSize="13dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/draft_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="4dp"
                android:layout_marginBottom="3dp"
                android:text="@string/convarsation_item_view_5"
                android:textColor="#FF7700"
                android:textSize="13dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <!-- 消息内容 -->
            <TextView
                android:id="@+id/con_item_msg_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:layout_marginBottom="3dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/main_activity_text_secondary"
                android:textSize="14dp"
                tools:text="hhhhhhhhhhhhddd" />

        </LinearLayout>

    </RelativeLayout>

</merge>