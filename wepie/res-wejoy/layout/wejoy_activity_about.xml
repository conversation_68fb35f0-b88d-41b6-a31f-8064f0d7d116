<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="@color/white">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/head_image"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="92dp"
            android:scaleType="centerCrop"
            android:src="@drawable/about_icon" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="31dp"
            android:text="@string/activity_setting_app_name"
            android:textColor="#101010"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/app_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="5dp"
            android:text="V 1.0"
            android:textColor="#101010"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/build_time_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:textColor="#333333"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <!-- 功能介绍 -->
        <RelativeLayout
            android:id="@+id/game_rule_layout"
            style="@style/setting_common_layout_bg"
            android:layout_marginTop="10dp"
            android:visibility="gone">

            <TextView
                style="@style/setting_common_text_style"
                android:text="@string/activity_setting_game_info" />

            <ImageView style="@style/setting_common_right_arrow_style" />

        </RelativeLayout>

        <!-- 检查更新 -->
        <RelativeLayout
            android:id="@+id/check_update_layout"
            style="@style/setting_common_layout_bg"
            android:visibility="gone">

            <TextView
                android:id="@+id/check_update_text"
                style="@style/setting_common_text_style"
                android:text="@string/activity_setting_upgrade" />

            <ImageView style="@style/setting_common_right_arrow_style" />

            <ImageView
                android:id="@+id/check_update_dot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/check_update_text"
                android:layout_gravity="center"
                android:layout_toEndOf="@id/check_update_text"
                android:src="@drawable/small_red_dot"
                android:visibility="invisible" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/website_layout"
            style="@style/setting_common_layout_bg"
            android:visibility="gone">

            <TextView
                style="@style/setting_common_text_style"
                android:text="@string/activity_about_3" />

            <ImageView
                android:id="@+id/website_right_arrow"
                style="@style/setting_common_right_arrow_style" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="4dp"
                android:layout_toStartOf="@+id/website_right_arrow"
                android:maxLines="1"
                android:singleLine="true"
                android:text="@string/activity_about_4"
                android:textColor="@color/color_setting_text_gray_tips"
                android:textSize="12sp" />

        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:overScrollMode="never"
            tools:itemCount="2"
            tools:listitem="@layout/wejoy_activity_about_list_item" />

        <LinearLayout
            android:id="@+id/protocol_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/user_protocol_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/user_agreements"
                android:textColor="@color/color_setting_text_gray_tips"
                android:textSize="11sp" />

            <TextView
                android:id="@+id/privacy_protocol_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:text="@string/privacy_agreements"
                android:textColor="@color/color_setting_text_gray_tips"
                android:textSize="11sp" />

        </LinearLayout>

        <TextView
            android:id="@+id/copy_right_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="53dp"
            android:textColor="@color/color_setting_text_gray_tips"
            android:textSize="11sp"
            tools:text="Copyright © 2021-2023 WEJOY PTE. LTD. All Rights Reserved." />
    </LinearLayout>
</LinearLayout>