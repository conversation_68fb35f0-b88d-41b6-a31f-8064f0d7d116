<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/head_image_iv"
        android:layout_width="86dp"
        android:layout_height="86dp"
        app:civ_border_color="@color/red"
        app:civ_border_width="4dp"
        app:civ_border_overlay="false"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

    </com.huiwan.widget.CustomCircleImageView>
</androidx.constraintlayout.widget.ConstraintLayout>