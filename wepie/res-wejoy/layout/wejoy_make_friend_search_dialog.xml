<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="303dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/shape_ffffff_corner12"
    android:orientation="vertical"
    android:paddingHorizontal="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp">

        <TextView
            android:id="@+id/search_room_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/make_friend_search_room_title"
            android:textColor="#333333"
            android:textSize="@dimen/wp16" />

        <TextView
            android:id="@+id/search_user_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/make_friend_search_user_title"
            android:textColor="#333333"
            android:textSize="@dimen/wp16" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/scroll_indicator_iv"
        android:layout_width="24dp"
        android:layout_height="4dp"
        android:layout_marginTop="4dp"
        android:src="@drawable/shape_24c572_line28"
        android:visibility="visible"
        app:tint="@color/color_accent_ex" />

    <EditText
        android:id="@+id/search_room_edit_text"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/shape_fafafa_corner32"
        android:hint="@string/game_room_input_number"
        android:maxLines="1"
        android:paddingHorizontal="12dp"
        android:singleLine="true"
        android:text=""
        android:textAlignment="viewStart"
        android:textColor="#333333"
        android:textColorHint="#CCCCCC"
        android:textCursorDrawable="@drawable/cursor_accent_ex"
        android:textSize="@dimen/wp14" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="24dp"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/search_room_cancel_tx"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginEnd="15dp"
            android:layout_weight="1"
            android:background="@drawable/sel_00000000_corner20_strock_24c572"
            android:backgroundTint="@color/sel_accent_ex_accent_gray"
            android:gravity="center"
            android:text="@string/search_room_dialog_4"
            android:textAlignment="center"
            android:textColor="@color/color_accent_ex"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/search_room_enter_tx"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/sel_24c572_corner20"
            android:backgroundTint="@color/sel_accent_ex_accent_gray"
            android:gravity="center"
            android:text="@string/search_room_dialog_5"
            android:textAlignment="center"
            android:textColor="#ffffff"
            android:textSize="16dp" />

    </LinearLayout>

</LinearLayout>