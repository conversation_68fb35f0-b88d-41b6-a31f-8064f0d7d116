<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/wejoy_hot_voice_room_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="@color/main_activity_text_primary"
        android:textSize="@dimen/wp20"
        android:textStyle="bold"
        tools:text="国家" />

    <Space
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="5dp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/more"
        android:textColor="@color/main_activity_text_secondary"
        android:textSize="@dimen/wp14" />

    <ImageView
        style="@style/voice_room_popular_title_arrow"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:layout_gravity="center_vertical|end"
        android:adjustViewBounds="true"
        android:scaleX="@integer/image_scale_x" />

</LinearLayout>