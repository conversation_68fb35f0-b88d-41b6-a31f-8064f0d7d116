<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_f6f6f6_corner16"
    tools:layout_height="98dp">

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/head_iv"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:src="@drawable/default_head_icon"
        app:civ_corner_radius="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.huiwan.widget.BorderImageView
        android:id="@+id/room_owner_area_iv"
        android:layout_width="18dp"
        android:layout_height="14dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="10dp"
        app:borderColor="#DADAE0"
        app:borderWidth="0.5dp"
        app:cornerRadius="2dp"
        app:layout_constraintBottom_toBottomOf="@+id/room_name_tv"
        app:layout_constraintEnd_toStartOf="@+id/room_name_tv"
        app:layout_constraintStart_toEndOf="@+id/head_iv"
        app:layout_constraintTop_toTopOf="@+id/room_name_tv" />

    <TextView
        android:id="@+id/room_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="16dp"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textAlignment="viewStart"
        android:textColor="@color/main_activity_text_primary"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/room_owner_area_iv"
        app:layout_constraintTop_toTopOf="@+id/head_iv"
        app:layout_goneMarginStart="10dp"
        tools:text="Million heroes 19:00 games" />

    <TextView
        android:id="@+id/room_label_tv"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:minWidth="30dp"
        android:paddingStart="4dp"
        android:paddingTop="1dp"
        android:paddingEnd="4dp"
        android:paddingBottom="1dp"
        android:textSize="11sp"
        app:layout_constraintStart_toEndOf="@+id/head_iv"
        app:layout_constraintTop_toBottomOf="@+id/room_name_tv"
        tools:background="@drawable/room_label_bg"
        tools:text="音乐"
        tools:textColor="#43A1F0"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/room_people_num_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="4dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:minHeight="18dp"
        android:singleLine="true"
        android:textColor="@color/main_activity_text_secondary"
        android:textSize="12dp"
        app:layout_constraintStart_toEndOf="@+id/room_label_tv"
        app:layout_constraintTop_toBottomOf="@+id/room_name_tv"
        app:layout_goneMarginStart="12dp"
        tools:text="12 people in total" />

    <TextView
        android:id="@+id/room_type_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:background="@drawable/self_advance_room_tag_bg"
        android:includeFontPadding="false"
        android:paddingHorizontal="8dp"
        android:paddingTop="1dp"
        android:paddingBottom="3dp"
        android:textColor="@color/white"
        android:textSize="@dimen/wp12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="主播房" />

</androidx.constraintlayout.widget.ConstraintLayout>