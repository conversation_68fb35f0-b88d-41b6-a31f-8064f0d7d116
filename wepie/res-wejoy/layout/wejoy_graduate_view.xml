<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="303dp"
        android:layout_height="404dp"
        android:layout_gravity="center">

        <View
            android:layout_width="303dp"
            android:layout_height="404dp"
            android:background="@drawable/wejoy_graduate_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/graduate_title_tv"
            android:layout_width="222dp"
            android:layout_height="37dp"
            android:gravity="center"
            android:text="@string/congrats_for_graduation"
            android:textSize="26dp"
            android:textColor="#72423A"
            android:layout_marginTop="116dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/graduate_description_tv"
            android:layout_width="258dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/graduate_from_weplay_cute_newbie_village"
            android:textSize="13dp"
            android:layout_marginTop="12dp"
            android:lineSpacingExtra="6dp"
            android:textColor="#72423A"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/graduate_title_tv"/>
        
        <ImageView
            android:id="@+id/title_icon_iv"
            android:layout_width="144dp"
            android:layout_height="36dp"
            android:layout_marginTop="19dp"
            android:scaleType="fitXY"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/graduate_description_tv" />
        
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/receive_lay"
            android:layout_width="246dp"
            android:layout_height="75dp"
            android:background="@drawable/wejoy_graduate_receive_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:layout_width="222dp"
                android:layout_height="26dp"
                android:gravity="center"
                android:layout_marginTop="9dp"
                android:text="@string/receive_happily"
                android:textSize="18dp"
                android:textColor="#FFFBDB"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>