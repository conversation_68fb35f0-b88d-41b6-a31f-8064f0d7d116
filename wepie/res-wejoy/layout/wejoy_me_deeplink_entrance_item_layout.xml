<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/invite_lay"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:layout_marginTop="12dp"
    android:background="@drawable/me_tab_item_bg"
    android:visibility="visible">

    <ImageView
        android:id="@+id/entrance_iv"
        android:layout_width="@dimen/me_tab_icon_size"
        android:layout_height="@dimen/me_tab_icon_size"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp" />

    <TextView
        android:id="@+id/entrance_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="9dp"
        android:layout_toEndOf="@id/entrance_iv"
        android:textColor="@color/main_activity_text_primary"
        android:textSize="16dp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toStartOf="@+id/entrance_arrow"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/entrance_tip_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:textColor="@color/main_activity_text_tertiary"
            android:textSize="14sp"
            android:visibility="gone"
            tools:text="12445"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/entrance_dot"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/red_dot"
            android:visibility="gone" />

    </LinearLayout>

    <ImageView
        android:id="@+id/entrance_arrow"
        style="@style/me_item_arrow" />

</RelativeLayout>