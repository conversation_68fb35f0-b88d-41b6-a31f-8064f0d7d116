<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/main_activity_bg"
    tools:parentTag="com.wejoy.weplay.module.me.WejoyMeTabView">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include layout="@layout/me_tab_top_view" />

            <RelativeLayout
                android:id="@+id/album_lay"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/me_tab_item_bg">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/album_iv"
                    android:layout_width="@dimen/me_tab_icon_size"
                    android:layout_height="@dimen/me_tab_icon_size"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    app:srcCompat="@drawable/wejoy_slide_menu_photo" />

                <TextView
                    android:id="@+id/album_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="9dp"
                    android:layout_toEndOf="@+id/album_iv"
                    android:text="@string/me_tab_view_3"
                    android:textColor="@color/main_activity_text_primary"
                    android:textSize="16dp"
                    android:textStyle="bold" />

                <ImageView style="@style/me_item_arrow" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/grade_lay"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:background="@drawable/me_tab_item_bg"
                android:visibility="visible">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/grade_iv"
                    android:layout_width="@dimen/me_tab_icon_size"
                    android:layout_height="@dimen/me_tab_icon_size"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    app:srcCompat="@drawable/wejoy_slide_menu_score" />

                <TextView
                    android:id="@+id/grade_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="9dp"
                    android:layout_toEndOf="@+id/grade_iv"
                    android:text="@string/user_game_career_title"
                    android:textColor="@color/main_activity_text_primary"
                    android:textSize="16dp"
                    android:textStyle="bold" />

                <ImageView style="@style/me_item_arrow" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/bag_lay"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:background="@drawable/me_tab_item_bg"
                android:visibility="visible">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/bag_iv"
                    android:layout_width="@dimen/me_tab_icon_size"
                    android:layout_height="@dimen/me_tab_icon_size"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    app:srcCompat="@drawable/wejoy_slide_menu_bag" />

                <TextView
                    android:id="@+id/bag_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="9dp"
                    android:layout_toEndOf="@+id/bag_iv"
                    android:text="@string/my_bag"
                    android:textColor="@color/main_activity_text_primary"
                    android:textSize="16dp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/bag_red_dot_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="28dp"
                    android:background="@drawable/small_red_dot"
                    android:visibility="gone" />

                <ImageView style="@style/me_item_arrow" />

            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:background="@drawable/me_tab_line_divider" />

            <LinearLayout
                android:id="@+id/deeplink_entrance_list_lay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone" />

            <View
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:background="@drawable/me_tab_line_divider" />

            <RelativeLayout
                android:id="@+id/praise_lay"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:background="@drawable/me_tab_item_bg"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/praise_iv"
                    android:layout_width="21dp"
                    android:layout_height="21dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:src="@drawable/wejoy_slide_menu_praise" />

                <TextView
                    android:id="@+id/praise_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="9dp"
                    android:layout_toEndOf="@+id/praise_iv"
                    android:textColor="#4A4A4A"
                    android:textSize="16dp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/market_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="34dp"
                    android:textColor="#999CB4"
                    android:textSize="14sp" />

                <ImageView style="@style/me_item_arrow" />

            </RelativeLayout>

            <!-- 多语言 -->
            <RelativeLayout
                android:id="@+id/lang_layout"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:background="@drawable/me_tab_item_bg">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/language_iv"
                    android:layout_width="@dimen/me_tab_icon_size"
                    android:layout_height="@dimen/me_tab_icon_size"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    app:srcCompat="@drawable/wejoy_language_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="9dp"
                    android:layout_toEndOf="@+id/language_iv"
                    android:text="@string/activity_setting_lang"
                    android:textColor="@color/main_activity_text_primary"
                    android:textSize="16dp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/language_arrow"
                    style="@style/me_item_arrow" />

                <TextView
                    android:id="@+id/language_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:layout_toStartOf="@+id/language_arrow"
                    android:textColor="@color/main_activity_text_secondary"
                    android:textSize="14sp"
                    tools:text="English" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/customer_service_lay"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:background="@drawable/me_tab_item_bg"
                android:visibility="visible">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/customer_service_iv"
                    android:layout_width="@dimen/me_tab_icon_size"
                    android:layout_height="@dimen/me_tab_icon_size"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    app:srcCompat="@drawable/wejoy_customer_service_icon" />

                <TextView
                    android:id="@+id/customer_service_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="9dp"
                    android:layout_toEndOf="@+id/customer_service_iv"
                    android:text="@string/help_and_custom"
                    android:textColor="@color/main_activity_text_primary"
                    android:textSize="16dp"
                    android:textStyle="bold" />

                <ImageView style="@style/me_item_arrow" />

                <ImageView
                    android:id="@+id/customer_service_dot"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:layout_toEndOf="@id/customer_service_text"
                    android:background="@drawable/small_red_dot"
                    android:visibility="invisible" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/manual_service_lay"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:background="@drawable/me_tab_item_bg"
                android:visibility="visible">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/manual_service_iv"
                    android:layout_width="@dimen/me_tab_icon_size"
                    android:layout_height="@dimen/me_tab_icon_size"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    app:srcCompat="@drawable/wejoy_manual_service_icon" />

                <TextView
                    android:id="@+id/manual_service_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="9dp"
                    android:layout_toEndOf="@+id/manual_service_iv"
                    android:text="@string/manual_service"
                    android:textColor="@color/main_activity_text_primary"
                    android:textSize="16dp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/manual_service_arrow"
                    style="@style/me_item_arrow" />

                <TextView
                    android:id="@+id/manual_service_count_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="4dp"
                    android:layout_toStartOf="@+id/manual_service_arrow"
                    android:textColor="@color/main_activity_text_tertiary"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:text="5 messages" />

            </RelativeLayout>

            <!--            <RelativeLayout-->
            <!--                android:id="@+id/real_name_lay"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="52dp"-->
            <!--                android:background="@drawable/me_tab_item_bg"-->
            <!--                android:visibility="gone">-->

            <!--                <ImageView-->
            <!--                    android:id="@+id/real_name_iv"-->
            <!--                    android:layout_width="21dp"-->
            <!--                    android:layout_height="21dp"-->
            <!--                    android:layout_alignParentStart="true"-->
            <!--                    android:layout_centerVertical="true"-->
            <!--                    android:layout_marginStart="16dp"-->
            <!--                    android:src="@drawable/certificate_icon" />-->

            <!--                <TextView-->
            <!--                    android:id="@+id/real_name_text"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_centerVertical="true"-->
            <!--                    android:layout_marginStart="9dp"-->
            <!--                    android:layout_toEndOf="@+id/real_name_iv"-->
            <!--                    android:text="@string/me_tab_view_20"-->
            <!--                    android:textColor="#4A4A4A"-->
            <!--                    android:textSize="16dp" />-->

            <!--                <ImageView style="@style/discover_item_arrow" />-->

            <!--            </RelativeLayout>-->

            <RelativeLayout
                android:id="@+id/setting_lay"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:layout_marginBottom="4dp"
                android:background="@drawable/me_tab_item_bg"
                android:visibility="visible">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/setting_iv"
                    android:layout_width="@dimen/me_tab_icon_size"
                    android:layout_height="@dimen/me_tab_icon_size"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    app:srcCompat="@drawable/wejoy_slide_menu_setting" />

                <TextView
                    android:id="@+id/setting_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="9dp"
                    android:layout_toEndOf="@+id/setting_iv"
                    android:text="@string/settings"
                    android:textColor="@color/main_activity_text_primary"
                    android:textSize="16dp"
                    android:textStyle="bold" />

                <ImageView style="@style/me_item_arrow" />

                <ImageView
                    android:id="@+id/setting_red_dot"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_alignTop="@id/setting_text"
                    android:layout_gravity="center"
                    android:layout_toEndOf="@id/setting_text"
                    android:src="@drawable/small_red_dot"
                    android:visibility="invisible" />

                <ImageView
                    android:id="@+id/setting_red_dot_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="28dp"
                    android:background="@drawable/small_red_dot"
                    android:visibility="invisible" />

            </RelativeLayout>

        </LinearLayout>

    </ScrollView>

    <com.wejoy.weplay.module.me.WejoyMeTabGuideBindView
        android:id="@+id/guide_bind_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="gone" />

</merge>