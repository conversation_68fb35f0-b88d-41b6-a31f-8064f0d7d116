<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/voice_room_list_lay"
    android:layout_width="match_parent"
    android:layout_height="84dp"
    android:clipChildren="false"
    tools:background="@color/main_activity_bg">

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/head_iv"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:src="@drawable/default_head_icon"
        app:civ_corner_radius="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/voice_room_lock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="-4dp"
        android:layout_marginBottom="-4dp"
        android:src="@drawable/voice_room_lock"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/head_iv"
        app:layout_constraintEnd_toEndOf="@+id/head_iv"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/voice_room_marking_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="20dp"
        android:maxHeight="20dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#300f"
        tools:layout_height="18dp"
        tools:layout_width="60dp"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/room_name_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="8dp"
        android:clipChildren="false"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/voice_game_info_lay"
        app:layout_constraintEnd_toStartOf="@+id/voice_room_marking_icon"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/head_iv">

        <com.huiwan.widget.BorderImageView
            android:id="@+id/room_owner_area_iv"
            android:layout_width="18dp"
            android:layout_height="14dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="4dp"
            app:borderColor="#DADAE0"
            app:borderWidth="0.5dp"
            app:cornerRadius="2dp" />

        <TextView
            android:id="@+id/room_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:includeFontPadding="false"
            android:singleLine="true"
            android:textColor="@color/main_activity_text_primary"
            android:textSize="16dp"
            tools:text="Million heroes 19:00 games" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/voice_game_info_lay"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_marginStart="10dp"
        android:layout_marginBottom="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/head_iv"
        app:layout_constraintStart_toEndOf="@+id/head_iv">

        <com.huiwan.widget.CustomCircleImageView
            android:id="@+id/voice_game_icon_iv"
            android:layout_width="16dp"
            android:layout_height="16dp"
            app:civ_corner_radius="3dp"
            tools:src="@drawable/voice_room_game_jk_1v1" />

        <TextView
            android:id="@+id/voice_game_mode_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            android:textFontWeight="700"
            android:textSize="12dp"
            android:textStyle="bold"
            tools:text="1v1" />

        <TextView
            android:id="@+id/voice_game_coin_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:includeFontPadding="false"
            android:textColor="@color/main_activity_text_secondary"
            android:textSize="12dp"
            tools:text="2000 Coins" />

        <View
            android:layout_width="1dp"
            android:layout_height="8dp"
            android:layout_marginStart="4dp"
            android:background="@color/main_activity_text_secondary" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/vr_item_user_iv"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="2dp"
            app:srcCompat="@drawable/ic_room_online_count"
            app:tint="@color/main_activity_text_secondary" />

        <TextView
            android:id="@+id/vr_item_usernum_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:textColor="@color/main_activity_text_secondary"
            android:textSize="12dp"
            tools:text="20" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/voice_game_waiting_lay"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/shape_1a43a1f0_corner_10"
        android:orientation="horizontal"
        android:paddingVertical="1dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/voice_game_info_lay"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/voice_game_info_lay">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/voice_room_widget_game_icon"
            app:tint="#43A1F0" />

        <TextView
            android:id="@+id/voice_game_gamer_count_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="22dp"
            android:includeFontPadding="false"
            android:textColor="#43A1F0"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="3/4" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:layout_marginStart="-4dp"
            android:layout_marginEnd="4dp"
            android:adjustViewBounds="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/voice_game_gamer_count_tv"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_arrow_right"
            app:tint="#43A1F0" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/voice_game_gaming_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/voice_game_info_lay"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/voice_game_info_lay"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="16dp"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            app:srcCompat="@drawable/wejoy_game_play_icon" />

        <TextView
            android:id="@+id/voice_game_gaming_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:gravity="start|center_vertical"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:text="@string/game_status_gaming"
            android:textColor="@color/main_activity_text_secondary"
            android:textSize="12dp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>