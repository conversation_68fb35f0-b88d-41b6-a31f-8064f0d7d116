<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/main_activity_bg"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:show_status_view="true" />

    <ScrollView
        style="@style/scrollbar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/action_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/welcome_title_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="68dp"
                android:text="@string/welcome_to_weplay"
                android:textColor="@color/main_activity_text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/complete_data_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/complete_profile_and_know_more_friends"
                android:textColor="@color/main_activity_text_secondary"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/welcome_title_tx" />

            <RelativeLayout
                android:id="@+id/head_image_lay"
                android:layout_width="match_parent"
                android:layout_height="86dp"
                android:layout_marginTop="62dp"
                android:gravity="center"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/complete_data_tx">

                <com.wejoy.weplay.module.login.register.HeadRecycleView
                    android:id="@+id/head_select_rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:paddingStart="62dp"
                    android:paddingEnd="62dp"
                    tools:itemCount="8"
                    tools:layoutManager="LinearLayoutManager"
                    tools:listitem="@layout/wejoy_user_head_item"
                    tools:orientation="horizontal"
                    tools:scrollToPosition="4" />

                <ImageView
                    android:id="@+id/left_gradient_iv"
                    android:layout_width="76dp"
                    android:layout_height="match_parent"
                    android:alpha="1.0"
                    android:background="@drawable/shape_gradient_left"
                    android:scaleX="@integer/image_scale_x" />

                <ImageView
                    android:id="@+id/right_gradient_iv"
                    android:layout_width="76dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:alpha="1.0"
                    android:background="@drawable/shape_gradient_right"
                    android:scaleX="@integer/image_scale_x" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="86dp"
                    android:layout_height="86dp"
                    android:layout_centerInParent="true">

                    <com.huiwan.widget.CustomCircleImageView
                        android:id="@+id/head_image"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:visibility="invisible"
                        tools:ignore="MissingConstraints" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        app:layout_constraintBottom_toBottomOf="@+id/head_image"
                        app:layout_constraintRight_toRightOf="@+id/head_image"
                        app:srcCompat="@drawable/new_camera_icon" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </RelativeLayout>

            <EditText
                android:id="@+id/nick_name_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:layout_marginTop="50dp"
                android:layout_marginRight="40dp"
                android:background="@color/translucent"
                android:hint="@string/nick_name"
                android:maxLength="16"
                android:maxLines="1"
                android:paddingVertical="12dp"
                android:singleLine="true"
                android:textAlignment="center"
                android:textColor="@color/main_activity_text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/head_image_lay" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/delete_nick_name_btn"
                android:layout_width="16dp"
                android:layout_height="38dp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="@+id/nick_name_text"
                app:layout_constraintEnd_toEndOf="@+id/nick_name_text"
                app:layout_constraintTop_toTopOf="@+id/nick_name_text"
                app:srcCompat="@drawable/wejoy_complete_user_info_close_icon" />

            <View
                android:id="@+id/nick_name_line"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:alpha="0.3"
                android:background="#999cb4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/nick_name_text" />

            <include
                android:id="@+id/gender_woman_lay"
                layout="@layout/wejoy_gender_select_layout"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginStart="20dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="15dp"
                android:gravity="center"
                android:layoutDirection="ltr"
                android:maxWidth="160dp"
                android:orientation="horizontal"
                app:layout_constraintEnd_toStartOf="@+id/gender_man_lay"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/nick_name_line"
                app:layout_constraintVertical_chainStyle="spread" />

            <include
                android:id="@+id/gender_man_lay"
                layout="@layout/wejoy_gender_select_layout"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="20dp"
                android:gravity="center"
                android:layoutDirection="ltr"
                android:maxWidth="160dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/gender_woman_lay"
                app:layout_constraintTop_toBottomOf="@+id/nick_name_line" />

            <TextView
                android:id="@+id/gender_tips_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:text="@string/gender_can_not_be_change_tips"
                android:textColor="#827F7E"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/gender_woman_lay" />

            <View
                android:id="@+id/gender_line"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginStart="40dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="40dp"
                android:background="#F2F2F2"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/gender_tips_tv" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/location_layout"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:layout_marginTop="42dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/gender_line">

                <com.huiwan.widget.BorderImageView
                    android:id="@+id/location_head_iv"
                    android:layout_width="20dp"
                    android:layout_height="15dp"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="6dp"
                    android:visibility="gone"
                    app:borderColor="#DADAE0"
                    app:borderWidth="0.5dp"
                    app:cornerRadius="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/location_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:gravity="center"
                    android:paddingStart="3dp"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/main_activity_text_primary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/location_head_iv"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="11dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/location_text"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/wejoy_complete_right_select" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/complete_button"
                android:layout_width="247dp"
                android:layout_height="56dp"
                android:layout_marginStart="64dp"
                android:layout_marginTop="98dp"
                android:layout_marginEnd="64dp"
                android:background="@drawable/wejoy_complete_user_info_submit_bg"
                android:gravity="center"
                android:text="@string/save"
                android:textColor="#ffffff"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/location_layout" />

            <TextView
                android:id="@+id/select_later"
                android:layout_width="match_parent"
                android:layout_height="26dp"
                android:layout_marginTop="20dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:text="@string/fill_in_later"
                android:textColor="@color/color_accent"
                android:textSize="14dp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/complete_button" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</merge>