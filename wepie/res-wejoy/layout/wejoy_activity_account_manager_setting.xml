<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_setting_bg"
    android:orientation="vertical">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/mobile_layout"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/setting_item_bg">

            <ImageView
                android:id="@+id/phone_bind_state"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:src="@drawable/phone_login_img" />

            <TextView
                android:id="@+id/mobile_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="49dp"
                android:text="@string/activity_edit_userinfo_6"
                android:textColor="#1B1D38"
                android:textSize="16dp" />

            <ImageView
                android:id="@+id/mobile_right_icon"
                style="@style/list_right_arrow" />

            <TextView
                android:id="@+id/mobile_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="20dp"
                android:layout_toStartOf="@+id/mobile_right_icon"
                android:layout_toEndOf="@+id/mobile_title"
                android:ellipsize="end"
                android:gravity="end"
                android:maxLines="1"
                android:singleLine="true"
                android:text="@string/activity_edit_userinfo_7"
                android:textColor="#FF8D17"
                android:textSize="13dp" />
        </RelativeLayout>

        <View
            android:id="@+id/line_space_view"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/bind_other_account"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/bind_other_account_tittle"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_marginStart="16dp"
                android:gravity="center"
                android:text="@string/account_manage_bind_other"
                android:textColor="#999CB4"
                android:textSize="12dp" />

            <!--<TextView
                android:id="@+id/bind_other_account_count"
                android:layout_width="wrap_content"
                android:text="最多可绑定一个"
                android:gravity="center"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="10dp"
                android:layout_height="30dp"
                android:textSize="10dp"
                android:textColor="#999999" />-->
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/account_info_recycle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:itemCount="4"
            tools:layoutManager="LinearLayoutManager"
            tools:listitem="@layout/bind_account_info_item"
            tools:orientation="vertical" />

        <!-- 申请注销 -->
        <RelativeLayout
            android:id="@+id/apply_close_account_lay"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/setting_item_bg">

            <TextView
                style="@style/list_left_text_16dp"
                android:text="@string/activity_setting_del_account" />

            <ImageView style="@style/list_right_arrow" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="bottom"
            android:orientation="vertical">

            <TextView
                android:id="@+id/bind_account_content_tx"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="34dp"
                android:text="@string/account_manage_bottom_tip"
                android:textColor="#999CB4"
                android:textSize="12dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>

