<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <!-- 申请注销 -->
    <RelativeLayout
        android:id="@+id/account_info_item"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/setting_item_bg">
        <ImageView
            android:id="@+id/account_bind_state"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:layout_marginStart="16dp"
            android:layout_centerVertical="true"
            android:src="@drawable/facebook_login_img" />

        <TextView
            android:id="@+id/account_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="49dp"
            android:textColor="#1B1D38"
            android:textSize="16dp"
            android:layout_centerVertical="true"
            android:text="Facebook"/>

        <TextView
            android:id="@+id/bind_other_account_count"
            android:layout_width="wrap_content"
            android:text="@string/binded"
            android:gravity="center"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="34dp"
            android:layout_height="30dp"
            android:textSize="10dp"
            android:layout_centerVertical="true"
            android:visibility="gone"
            android:textColor="#BDBDBD"/>

        <ImageView
            android:id="@+id/right_arrow"
            style="@style/list_right_arrow" />
    </RelativeLayout>

</LinearLayout>
