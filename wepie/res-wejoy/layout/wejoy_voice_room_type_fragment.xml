<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="12dp">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/make_friends_appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/translucent"
        app:layout_scrollFlags="scroll">

        <FrameLayout
            android:id="@+id/head_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/translucent"
            android:orientation="vertical"
            android:paddingBottom="4dp"
            app:layout_scrollFlags="scroll" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/translucent"
            android:paddingStart="10dp"
            android:paddingEnd="10dp">

            <com.huiwan.widget.WejoyLabelLayout
                android:id="@+id/label_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:tabGravity="start"
                app:tabIndicator="@null"
                app:tabMinWidth="0dp"
                app:tabMode="scrollable"
                app:tabPaddingEnd="10dp"
                app:tabPaddingStart="10dp"
                app:tabRippleColor="@android:color/transparent"
                tools:layout_height="50dp" />
        </FrameLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <com.wejoy.weplay.module.makefriend.Pager2HostFrameLayout
        android:id="@+id/wejoy_room_list_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="8dp"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/room_list_view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.wejoy.weplay.module.makefriend.Pager2HostFrameLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>