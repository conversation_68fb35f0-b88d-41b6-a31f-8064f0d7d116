<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.huiwan.widget.banner.BannerViewPager
        android:id="@+id/banner_view_lay"
        android:layout_width="match_parent"
        android:layout_height="98dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toTopOf="parent" />

    <com.huiwan.widget.banner.ViewPagerLineIndicator
        android:id="@+id/banner_pager_indicator"
        android:layout_width="0dp"
        android:layout_height="8dp"
        android:layout_marginBottom="8dp"
        app:indicator_margin="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/banner_view_lay"
        app:layout_constraintEnd_toEndOf="@+id/banner_view_lay"
        app:layout_constraintStart_toStartOf="@+id/banner_view_lay"
        app:not_selected_color="#3fffffff"
        app:selected_color="#ffffff" />

    <include
        android:id="@+id/voice_room_rank_lay"
        layout="@layout/jackaroo_ranks_in_voice_room"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/banner_view_lay" />


</androidx.constraintlayout.widget.ConstraintLayout>