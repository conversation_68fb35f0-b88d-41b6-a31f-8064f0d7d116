<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="20dp">

    <ImageView
        android:id="@+id/room_bg_iv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:scaleX="@integer/image_scale_x"
        android:src="@drawable/wejoy_related_create_room_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/head_iv"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginStart="16dp"
        app:civ_corner_radius="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/head_iv"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:text="@string/create_voice_room_title"
            android:textColor="@color/main_activity_text_primary"
            android:textSize="@dimen/wp16" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:text="@string/create_voice_room_desc"
            android:textColor="@color/white_alpha60"
            android:textSize="@dimen/wp12" />

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatImageView
        style="@style/create_voice_room_arrow"
        android:layout_marginEnd="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>