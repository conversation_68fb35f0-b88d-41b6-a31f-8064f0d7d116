<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white">

    <com.huiwan.base.ui.tab.HWUITabLayout
        android:id="@+id/jackaroo_game_mode_tab"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:hwTabLayout="button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/jackaroo_game_mode_lay"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/jackaroo_game_mode_tab" />

</androidx.constraintlayout.widget.ConstraintLayout>