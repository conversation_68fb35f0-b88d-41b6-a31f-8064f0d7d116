<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_setting_bg"
    android:orientation="vertical">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/share_game_state_layout"
        style="@style/setting_common_layout_bg"
        android:layout_marginTop="12dp">

        <TextView
            style="@style/setting_common_text_style"
            android:text="@string/privacy_share_game_status" />

        <ImageView style="@style/setting_common_right_arrow_style" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/joined_room_layout"
        style="@style/setting_common_layout_bg">

        <TextView
            style="@style/setting_common_text_style"
            android:text="@string/voice_room_joined_sub_title" />

        <ImageView style="@style/setting_common_right_arrow_style" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/record_state_layout"
        style="@style/setting_common_layout_bg">

        <TextView
            style="@style/setting_common_text_style"
            android:text="@string/privacy_record_stats" />

        <ImageView style="@style/setting_common_right_arrow_style" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/blacklist_layout"
        style="@style/setting_common_layout_bg">

        <TextView
            style="@style/setting_common_text_style"
            android:text="@string/privacy_contact_blacklist" />

        <ImageView style="@style/setting_common_right_arrow_style" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/invite_blacklist_layout"
        style="@style/setting_common_layout_bg">

        <TextView
            style="@style/setting_common_text_style"
            android:text="@string/privacy_game_invite_blocklist" />

        <ImageView style="@style/setting_common_right_arrow_style" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/forbid_recommend_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:background="@drawable/setting_item_bg"
        android:minHeight="64dp"
        android:visibility="gone">

        <TextView
            android:id="@+id/forbid_recommend_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:includeFontPadding="false"
            android:text="@string/privacy_no_recommend"
            android:textColor="@color/color_text_accent_dark"
            android:textSize="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/forbid_recommend_title"
            android:layout_alignStart="@id/forbid_recommend_title"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="12dp"
            android:layout_toStartOf="@id/forbid_recommend_bt"
            android:includeFontPadding="false"
            android:text="@string/privacy_no_recommend_tip"
            android:textColor="#999CB4"
            android:textSize="12dp" />

        <com.wepie.wespy.helper.view.SwitchView
            android:id="@+id/forbid_recommend_bt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/hide_location_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:background="@drawable/setting_item_bg"
        android:minHeight="64dp"
        android:visibility="gone">

        <TextView
            android:id="@+id/hide_location_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:includeFontPadding="false"
            android:text="@string/privacy_location_hide"
            android:textColor="@color/color_text_accent_dark"
            android:textSize="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/hide_location_title"
            android:layout_alignStart="@id/hide_location_title"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="12dp"
            android:layout_toStartOf="@id/hide_location_bt"
            android:includeFontPadding="false"
            android:text="@string/privacy_location_hide_tip"
            android:textColor="#999CB4"
            android:textSize="12dp" />

        <com.wepie.wespy.helper.view.SwitchView
            android:id="@+id/hide_location_bt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp" />
    </RelativeLayout>
</LinearLayout>