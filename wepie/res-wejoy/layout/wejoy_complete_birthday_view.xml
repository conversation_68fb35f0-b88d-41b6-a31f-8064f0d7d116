<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        style="@style/scrollbar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/action_bar">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="57dp"
                android:text="@string/welcome_to_weplay"
                android:textColor="#000000"
                android:textSize="18sp"
                android:textStyle="bold"
                android:typeface="normal" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/activity_select_top_hint"
                android:textColor="#999CB4"
                android:textSize="12sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/activity_birthday_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:layout_marginTop="87dp"
                android:layout_marginRight="40dp"
                android:background="@color/translucent"
                android:gravity="center"
                android:hint="@string/activity_edit_userinfo_9"
                android:textColor="#1B1D38"
                android:textColorHint="#cccccc"
                android:textSize="16sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="20dp"
                android:alpha="0.3"
                android:background="#999cb4" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/activity_birthday_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:drawablePadding="4dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="#FF3939"
                android:textSize="12sp"
                android:visibility="invisible"
                app:drawableStartCompat="@drawable/complete_birthday_tips_icon"
                tools:text="Sorry, under 13 years old cannot sign up"
                tools:visibility="visible" />

            <com.wepie.wespy.helper.selecttime.SelectTimeView
                android:id="@+id/activity_birthday_select"
                android:layout_width="match_parent"
                android:layout_height="232dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="56dp"
                android:layout_marginEnd="16dp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/activity_select_birthday"
                android:textColor="#999CB4"
                android:textSize="12sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/activity_birthday_bt"
                android:layout_width="247dp"
                android:layout_height="56dp"
                android:layout_marginStart="64dp"
                android:layout_marginTop="40dp"
                android:layout_marginEnd="64dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/wejoy_complete_user_info_submit_bg"
                android:enabled="false"
                android:gravity="center"
                android:text="@string/next_step"
                android:textColor="#ffffff"
                android:textSize="16sp"
                android:textStyle="bold"
                android:typeface="normal" />

        </androidx.appcompat.widget.LinearLayoutCompat>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>