<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/main_activity_bg"
    tools:parentTag="com.wejoy.weplay.module.chat.conversation.WejoyConversationView">

    <com.wejoy.weplay.module.chat.conversation.WejoyConActionBarView
        android:id="@+id/chat_list_tl"
        android:layout_width="match_parent"
        android:layout_height="72dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/chat_menu_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/chat_list_tl">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/tab_discover_circle_lay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/tab_discover_circle_icon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/jackaroo_moment_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tab_circle_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_horizontal"
                android:text="@string/home_discover_view_2"
                android:textColor="@color/main_activity_text_primary"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tab_discover_circle_icon" />

            <ImageView
                android:id="@+id/tab_circle_dot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/small_red_dot"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="@id/tab_discover_circle_icon"
                app:layout_constraintTop_toTopOf="@id/tab_discover_circle_icon"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/tab_discover_family_lay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <com.huiwan.widget.CustomCircleImageView
                android:id="@+id/tab_discover_family_icon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/jackaroo_family_icon"
                app:civ_corner_radius="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tab_family_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_horizontal"
                android:text="@string/home_discover_view_6"
                android:textColor="@color/main_activity_text_primary"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tab_discover_family_icon" />

            <ImageView
                android:id="@+id/tab_family_dot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/small_red_dot"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="@id/tab_discover_family_icon"
                app:layout_constraintTop_toTopOf="@id/tab_discover_family_icon"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/tab_discover_church_lay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <com.huiwan.widget.CustomCircleImageView
                android:id="@+id/tab_discover_church_icon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/jackaroo_church_icon"
                app:civ_corner_radius="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tab_church_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_horizontal"
                android:text="@string/chapel"
                android:textColor="@color/main_activity_text_primary"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tab_discover_church_icon" />

            <TextView
                android:id="@+id/tab_church_dot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:background="@drawable/large_red_dot"
                android:gravity="center"
                android:textColor="#ffffff"
                android:textSize="11dp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="@id/tab_discover_church_icon"
                app:layout_constraintTop_toTopOf="@id/tab_discover_church_icon"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/chat_list_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:cacheColorHint="#00000000"
        android:divider="@color/color_transparent"
        android:dividerHeight="0dp"
        android:listSelector="#00000000"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/chat_menu_lay" />

    <View
        android:id="@+id/top_padding_v"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="#f7f7f7"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/chat_list_tl" />

    <com.huiwan.base.ui.empty.HWUIEmptyView
        android:id="@+id/empty_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:visibility="gone"
        app:icon_type="base_empty_no_message"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:text="@string/msg_center_view_1"
        app:text_color="#BD987E" />

    <com.wepie.wespy.module.chat.conversation.CustomListPopupWindow
        android:id="@+id/list_popupwindow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

</merge>
