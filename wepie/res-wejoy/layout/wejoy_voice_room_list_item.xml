<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/voice_room_list_lay"
        android:layout_width="match_parent"
        android:layout_height="84dp"
        android:background="@drawable/voice_room_list_bg"
        android:clipChildren="false">

        <ImageView
            android:id="@+id/voice_room_list_anim_iv"
            android:layout_width="178dp"
            android:layout_height="84dp"
            android:layout_marginStart="-178dp"
            android:src="@drawable/voice_room_list_anim" />

        <ImageView
            android:id="@+id/voice_room_list_star_iv"
            android:layout_width="84dp"
            android:layout_height="84dp"
            android:layout_alignParentEnd="true"
            android:src="@drawable/voice_room_list_star" />

        <com.huiwan.widget.CustomCircleImageView
            android:id="@+id/head_iv"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:src="@drawable/default_head_icon"
            app:civ_corner_radius="16dp" />

        <TextView
            android:id="@+id/voice_room_manage_tv"
            android:layout_width="64dp"
            android:layout_height="22dp"
            android:layout_alignStart="@id/head_iv"
            android:layout_alignBottom="@id/head_iv"
            android:background="@drawable/room_list_admin_bg"
            android:fontFamily="@font/tajawal"
            android:gravity="center_horizontal|bottom"
            android:text="@string/room_grid_item_2"
            android:textAlignment="center"
            android:textColor="#ffffff"
            android:textSize="11dp" />

        <ImageView
            android:id="@+id/voice_room_lock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignEnd="@id/head_iv"
            android:layout_alignBottom="@id/head_iv"
            android:layout_marginEnd="-4dp"
            android:layout_marginBottom="-4dp"
            android:src="@drawable/voice_room_lock"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/voice_room_marking_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            android:maxHeight="20dp"
            android:visibility="gone"
            tools:background="#300f"
            tools:layout_height="18dp"
            tools:layout_width="60dp"
            tools:visibility="visible" />

        <FrameLayout
            android:id="@+id/voice_room_medal_container"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="17dp"
            android:layout_marginEnd="20dp"
            android:gravity="center"
            tools:background="#ffffff"
            tools:layout_height="22dp"
            tools:layout_width="30dp" />

        <LinearLayout
            android:id="@+id/room_name_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/room_label_lay"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="11dp"
            android:layout_toStartOf="@id/voice_room_medal_container"
            android:layout_toEndOf="@id/head_iv"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.huiwan.widget.BorderImageView
                android:id="@+id/room_owner_area_iv"
                android:layout_width="18dp"
                android:layout_height="14dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="4dp"
                app:borderColor="#DADAE0"
                app:borderWidth="0.5dp"
                app:cornerRadius="2dp" />

            <TextView
                android:id="@+id/room_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/main_activity_text_primary"
                android:textSize="16dp"
                tools:text="Million heroes 19:00 games" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/room_label_lay"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_alignBottom="@+id/head_iv"
            android:layout_marginStart="10dp"
            android:layout_marginBottom="6dp"
            android:layout_toEndOf="@id/head_iv"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/room_label_tv"
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:layout_marginEnd="2dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:minWidth="30dp"
                android:paddingStart="4dp"
                android:paddingTop="1dp"
                android:paddingEnd="4dp"
                android:paddingBottom="1dp"
                android:textSize="11sp"
                android:visibility="gone"
                tools:background="@drawable/room_label_bg"
                tools:text="音乐"
                tools:textColor="#43A1F0"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/item_info_lay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/head_iv"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/vr_item_user_iv"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_room_online_count"
                    app:tint="@color/main_activity_text_secondary" />

                <TextView
                    android:id="@+id/vr_item_usernum_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="17dp"
                    android:gravity="center"
                    android:textColor="@color/main_activity_text_secondary"
                    android:textSize="12dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/vr_item_user_iv"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="20" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/vr_item_icon_iv"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginStart="8dp"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/vr_item_usernum_tv"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/voice_room_widget_game_icon" />

                <com.huiwan.widget.MarqueeTextView
                    android:id="@+id/vr_item_info_mtv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:ellipsize="marquee"
                    android:gravity="start|center_vertical"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:paddingEnd="2dp"
                    android:singleLine="true"
                    android:textColor="@color/main_activity_text_secondary"
                    android:textSize="12dp"
                    android:visibility="invisible"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/vr_item_icon_iv"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

    </RelativeLayout>

    <include
        layout="@layout/translate_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="84dp" />

</LinearLayout>
