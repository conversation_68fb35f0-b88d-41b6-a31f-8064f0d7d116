<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="142dp"
    android:layout_height="wrap_content">

    <com.huiwan.widget.image.RoundedImageView
        android:id="@+id/activity_list_pic"
        android:layout_width="142dp"
        android:layout_height="80dp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:riv_corner_radius="8dp" />

    <ImageView
        android:layout_width="142dp"
        android:layout_height="26dp"
        android:src="@drawable/hot_activity_list_item_bottom_bg"
        app:layout_constraintBottom_toBottomOf="@id/activity_list_pic"
        app:layout_constraintStart_toStartOf="@id/activity_list_pic" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/activity_list_time_state"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="4dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/activity_list_pic"
        app:layout_constraintStart_toStartOf="@id/activity_list_pic"
        app:srcCompat="@drawable/voice_activity_list_live_icon"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/activity_list_time_state_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:fontFamily="@font/tajawal"
        android:lines="1"
        android:singleLine="true"
        android:text="@string/voice_room_activity_live"
        android:textColor="#FFFFFF"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/activity_list_time_state"
        app:layout_constraintStart_toEndOf="@id/activity_list_time_state"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/activity_list_time_not_start_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="4dp"
        android:fontFamily="@font/tajawal"
        android:lines="1"
        android:singleLine="true"
        android:textColor="#FFFFFF"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/activity_list_pic"
        app:layout_constraintStart_toStartOf="@id/activity_list_pic"
        tools:text="02/27 14:00"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/activity_list_activity_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:ellipsize="end"
        android:fontFamily="@font/tajawal"
        android:lines="1"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/activity_list_pic"
        tools:text="活动名活动名活动名活动名活动名活动名活动名活动名活动名活动名" />

    <TextView
        android:id="@+id/activity_list_room_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:fontFamily="@font/tajawal"
        android:lines="1"
        android:singleLine="true"
        android:textColor="@color/main_activity_text_secondary"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/activity_list_activity_name"
        tools:text="房间名房间名房间名房间名房间名房间名房间名房间名房间名房间名" />
</androidx.constraintlayout.widget.ConstraintLayout>