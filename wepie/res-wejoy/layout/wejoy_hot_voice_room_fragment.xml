<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/hot_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:srlEnableAutoLoadMore="false"
    app:srlEnableOverScrollBounce="false"
    app:srlEnableOverScrollDrag="false">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/make_friends_appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/translucent"
            app:elevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/translucent"
                android:orientation="vertical"
                app:layout_scrollFlags="scroll">

                <include
                    android:id="@+id/voice_activity_list_title_view"
                    layout="@layout/wejoy_hot_voice_room_title_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp" />

                <com.huiwan.widget.NestedScrollableHost
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/voice_activity_list_recycle_view"
                        style="@style/scrollbar"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="20dp"
                        android:cacheColorHint="@color/color_transparent"
                        android:divider="@null"
                        android:fadingEdge="none"
                        android:listSelector="@android:color/transparent"
                        android:overScrollMode="never"
                        tools:itemCount="4"
                        tools:layoutManager="LinearLayoutManager"
                        tools:listitem="@layout/wejoy_voice_room_hot_activity_list_item"
                        tools:orientation="horizontal" />
                </com.huiwan.widget.NestedScrollableHost>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/translucent"
                android:orientation="vertical"
                android:paddingHorizontal="20dp"
                android:paddingBottom="16dp"
                app:layout_scrollFlags="scroll">

                <include
                    android:id="@+id/region_title_view"
                    layout="@layout/wejoy_hot_voice_room_title_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <GridLayout
                    android:id="@+id/wejoy_hot_region_lay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="24dp"
                    android:columnCount="4"
                    android:rowCount="2">

                    <include
                        android:id="@+id/wejoy_hot_region_1"
                        layout="@layout/wejoy_hot_region_item_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_rowWeight="1"
                        android:layout_columnWeight="1" />

                    <include
                        android:id="@+id/wejoy_hot_region_2"
                        layout="@layout/wejoy_hot_region_item_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_rowWeight="1"
                        android:layout_columnWeight="1" />

                    <include
                        android:id="@+id/wejoy_hot_region_3"
                        layout="@layout/wejoy_hot_region_item_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_rowWeight="1"
                        android:layout_columnWeight="1" />

                    <include
                        android:id="@+id/wejoy_hot_region_4"
                        layout="@layout/wejoy_hot_region_item_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_rowWeight="1"
                        android:layout_columnWeight="1" />

                    <include
                        android:id="@+id/wejoy_hot_region_5"
                        layout="@layout/wejoy_hot_region_item_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_rowWeight="1"
                        android:layout_columnWeight="1"
                        android:layout_marginTop="16dp" />

                    <include
                        android:id="@+id/wejoy_hot_region_6"
                        layout="@layout/wejoy_hot_region_item_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_rowWeight="1"
                        android:layout_columnWeight="1"
                        android:layout_marginTop="16dp" />

                    <include
                        android:id="@+id/wejoy_hot_region_7"
                        layout="@layout/wejoy_hot_region_item_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_rowWeight="1"
                        android:layout_columnWeight="1"
                        android:layout_marginTop="16dp" />

                    <include
                        android:id="@+id/wejoy_hot_region_8"
                        layout="@layout/wejoy_hot_region_item_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_rowWeight="1"
                        android:layout_columnWeight="1"
                        android:layout_marginTop="16dp" />

                </GridLayout>

                <include
                    android:id="@+id/hot_room_list_title_view"
                    layout="@layout/wejoy_hot_voice_room_title_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </LinearLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <FrameLayout
            android:id="@+id/wejoy_hot_voice_room_list_lay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</com.scwang.smart.refresh.layout.SmartRefreshLayout>
