<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/chat_list_tl"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    tools:background="@color/main_activity_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/chat_list_tl_add_friend"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:paddingStart="10dp"
        android:paddingEnd="16dp"
        android:scaleType="centerInside"
        app:srcCompat="@drawable/wejoy_action_bar_add_black" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/chat_list_tl_friend_list"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_toStartOf="@id/chat_list_tl_add_friend"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:scaleType="centerInside"
        app:srcCompat="@drawable/wejoy_action_bar_connect_black" />

    <TextView
        android:id="@+id/title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:text="@string/con_action_bar_view_1"
        android:textColor="@color/main_activity_text_primary"
        android:textSize="26dp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/friends_ret_dot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="-14dp"
        android:layout_marginTop="9dp"
        android:layout_toEndOf="@+id/chat_list_tl_friend_list"
        android:background="@drawable/large_red_dot"
        android:gravity="center"
        android:textAlignment="center"
        android:textColor="#ffffff"
        android:textSize="11dp"
        android:visibility="invisible"
        tools:text="10" />

</RelativeLayout>