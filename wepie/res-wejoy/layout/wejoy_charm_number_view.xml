<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_lay"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/charm_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="11dp"
        android:includeFontPadding="false"
        android:paddingStart="11dp"
        android:paddingEnd="4dp"
        android:textColor="@color/white"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@id/charm_iv"
        app:layout_constraintStart_toStartOf="@id/charm_iv"
        app:layout_constraintTop_toTopOf="@id/charm_iv"
        tools:background="@drawable/charm_number_bg_16_17_18"
        tools:text="1000890" />

    <ImageView
        android:id="@+id/charm_iv"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/user_charm_level_16" />

</androidx.constraintlayout.widget.ConstraintLayout>