<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/room_intimacy_action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintEnd_toEndOf="parent"


        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/room_owner_intimacy_lay"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/sel_f8fafc"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/room_intimacy_action_bar">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="70dp"
            android:text="@string/activity_voice_room_setting_intimacy_owner"
            android:textColor="#343434"
            android:textSize="14dp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <com.wepie.wespy.helper.view.SwitchView
            android:id="@+id/room_owner_intimacy_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/room_normal_intimacy_lay"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@drawable/sel_f8fafc"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/room_owner_intimacy_lay">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="70dp"
            android:text="@string/activity_voice_room_setting_intimacy_normal"
            android:textColor="#343434"
            android:textSize="14dp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <com.wepie.wespy.helper.view.SwitchView
            android:id="@+id/room_normal_intimacy_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>