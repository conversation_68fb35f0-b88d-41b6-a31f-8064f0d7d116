<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/shadow_lay"
    android:orientation="vertical"
    android:background="@color/color_transparent60"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/image_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scaleType="centerInside"
        android:layout_margin="20dp"
        android:visibility="invisible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/share_eff0f1_corner16"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/guide_bind_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:layout_marginBottom="23dp"
            android:textSize="18sp"
            android:textColor="#333333"
            android:gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:text="@string/connect_account"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/close_img"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/close_icon"
            android:padding="6dp"
            android:gravity="center"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/bind_app_icon_lay"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:layout_marginTop="32dp"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="40dp"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/guide_bind_title"
            tools:background="@drawable/shape_0e558c_corner6">

    </androidx.appcompat.widget.LinearLayoutCompat>

        <TextView
            android:id="@+id/guide_bind_description_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="24dp"
            android:paddingBottom="24dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:text="@string/connect_account_tips"
            android:textColor="#999CB4"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/bind_app_icon_lay"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>