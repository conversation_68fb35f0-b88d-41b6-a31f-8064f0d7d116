<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_setting_bg"
    android:orientation="vertical">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        style="@style/scrollbar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="20dp"
                android:text="@string/preference_setting_desc"
                android:textColor="#999CB4"
                android:textSize="12dp" />

            <!-- 游戏年龄 -->
            <LinearLayout
                android:id="@+id/age_layout"
                style="@style/setting_common_layout_bg"
                android:layout_marginTop="10dp">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/setting_common_text_style"
                    android:text="@string/age_preference_title" />

                <Space
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/current_age_prefer_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#999CB4"
                    android:textSize="12dp"
                    tools:text="16岁以上" />

                <ImageView style="@style/setting_common_right_arrow_style" />

            </LinearLayout>

            <!-- 语言 -->
            <LinearLayout
                android:id="@+id/lang_select_layout"
                style="@style/setting_common_layout_bg"
                android:layout_marginTop="10dp">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/setting_common_text_style"
                    android:text="@string/lang_preference_title" />

                <Space
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/current_lang_prefer_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#999CB4"
                    android:textSize="12dp"
                    android:layout_marginEnd="28dp"
                    tools:text="中文" />

            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>