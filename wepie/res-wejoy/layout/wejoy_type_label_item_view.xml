<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    tools:layout_height="wrap_content">

    <TextView
        android:id="@+id/label_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="#999CB4"
        android:textSize="20dp"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBottomOf="@+id/guide_line"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="popular" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_line"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        app:layout_constraintGuide_end="14dp" />

</androidx.constraintlayout.widget.ConstraintLayout>