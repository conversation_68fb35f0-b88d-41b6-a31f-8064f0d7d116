<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_setting_bg"
    android:orientation="vertical">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        style="@style/scrollbar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="always"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/account_manager_layout"
                style="@style/setting_common_layout_bg"
                android:layout_marginTop="10dp">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/account_manage" />

                <ImageView
                    android:id="@+id/arrow_show_dot"
                    style="@style/setting_common_right_arrow_style" />

                <ImageView
                    android:id="@+id/red_dot_match"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toStartOf="@+id/arrow_show_dot"
                    android:background="@drawable/small_red_dot"
                    android:visibility="invisible"
                    tools:visibility="visible" />

            </RelativeLayout>

            <!-- 消息提醒 -->
            <RelativeLayout
                android:id="@+id/new_message_remind_layout"
                style="@style/setting_common_layout_bg"
                android:layout_marginTop="10dp">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_msg_notification" />

                <ImageView style="@style/setting_common_right_arrow_style" />

            </RelativeLayout>

            <!-- 隐私 -->
            <RelativeLayout
                android:id="@+id/privacy_lay"
                style="@style/setting_common_layout_bg">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_privacy" />

                <ImageView style="@style/setting_common_right_arrow_style" />
            </RelativeLayout>

            <!-- 通用 -->
            <RelativeLayout
                android:id="@+id/common_lay"
                style="@style/setting_common_layout_bg">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_common" />

                <ImageView style="@style/setting_common_right_arrow_style" />
            </RelativeLayout>

            <!-- 青少年模式 -->
            <!--            <RelativeLayout-->
            <!--                android:id="@+id/teen_lay"-->
            <!--                style="@style/setting_common_layout_bg">-->

            <!--                <TextView-->
            <!--                    style="@style/setting_common_text_style"-->
            <!--                    android:text="@string/activity_setting_teen_mode"/>-->

            <!--                <ImageView-->
            <!--                    style="@style/setting_common_right_arrow_style"/>-->

            <!--            </RelativeLayout>-->

            <!-- 清除缓存 -->
            <RelativeLayout
                android:id="@+id/clear_cache_lay"
                android:layout_marginTop="10dp"
                style="@style/setting_common_layout_bg">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_clear_cache"/>

                <ImageView
                    android:id="@+id/clear_cache_iv"
                    style="@style/setting_common_right_arrow_style" />

                <TextView
                    android:id="@+id/cache_size_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toStartOf="@id/clear_cache_iv"
                    tools:text="100.0 MB"
                    android:textColor="@color/color_settting_text_gray_tips"/>
            </RelativeLayout>

            <!-- 一键修复 -->
            <RelativeLayout
                android:id="@+id/game_fix_lay"
                style="@style/setting_common_layout_bg"
                android:layout_marginTop="10dp">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_quick_repair" />

                <ImageView style="@style/setting_common_right_arrow_style" />
            </RelativeLayout>

            <!-- 网络检测 -->
            <RelativeLayout
                android:id="@+id/check_net_lay"
                style="@style/setting_common_layout_bg">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_network_test" />

                <ImageView style="@style/setting_common_right_arrow_style" />
            </RelativeLayout>

            <!-- 网络检测 -->
            <RelativeLayout
                android:id="@+id/upload_log_lay"
                style="@style/setting_common_layout_bg">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_upload_log" />

                <ImageView style="@style/setting_common_right_arrow_style" />
            </RelativeLayout>

            <!-- 青少年模式 -->
            <RelativeLayout
                android:id="@+id/teen_mode_lay"
                style="@style/setting_common_layout_bg">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/teem_mode_activity_opening_title" />

                <ImageView
                    android:id="@+id/teen_mode_iv"
                    style="@style/setting_common_right_arrow_style" />

                <TextView
                    android:id="@+id/teen_mode_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="4dp"
                    android:layout_toStartOf="@id/teen_mode_iv"
                    android:textColor="@color/color_setting_text_gray_tips"
                    tools:text="已关闭" />
            </RelativeLayout>

            <!-- 给好评 -->
            <RelativeLayout
                android:id="@+id/setting_praise_lay"
                style="@style/setting_common_layout_bg"
                android:visibility="gone">

                <TextView
                    style="@style/setting_common_text_style" />

                <ImageView
                    android:id="@+id/setting_praise_iv"
                    style="@style/setting_common_right_arrow_style" />

            </RelativeLayout>

            <!-- 关于 -->
            <RelativeLayout
                android:id="@+id/about_layout"
                style="@style/setting_common_layout_bg"
                android:layout_marginTop="10dp">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_about" />

                <ImageView
                    android:id="@+id/about_iv"
                    style="@style/setting_common_right_arrow_style" />

                <TextView
                    android:id="@+id/about_version_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="4dp"
                    android:layout_toStartOf="@id/about_iv"
                    android:textColor="@color/color_setting_text_gray_tips"
                    tools:text="V1.0.0" />

            </RelativeLayout>

            <!-- 退出登陆 -->
            <TextView
                android:id="@+id/logout_btn"
                style="@style/setting_common_btn_style" />
        </LinearLayout>
    </ScrollView>

</LinearLayout>

