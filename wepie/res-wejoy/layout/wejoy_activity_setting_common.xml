<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_setting_bg"
    android:orientation="vertical">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        style="@style/scrollbar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 偏好设置 -->
            <RelativeLayout
                android:id="@+id/preference_layout"
                style="@style/setting_common_layout_bg">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_preference" />

                <ImageView style="@style/setting_common_right_arrow_style" />

            </RelativeLayout>

            <!-- 聊天背景 -->
            <RelativeLayout
                android:id="@+id/chat_bg_layout"
                style="@style/setting_common_layout_bg"
                android:layout_marginTop="10dp">

                <TextView
                    style="@style/setting_common_text_style"
                    android:text="@string/activity_setting_chat_bg" />

                <ImageView style="@style/setting_common_right_arrow_style" />

            </RelativeLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>