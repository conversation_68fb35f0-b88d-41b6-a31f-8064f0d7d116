<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:background="@drawable/shape_1affffff_corner12_stroke_33ffffff">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/activity_empty_star_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="16dp"
        android:scaleX="@integer/image_scale_x"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/wejoy_hot_voice_room_activity_star" />

    <TextView
        android:id="@+id/activity_empty_tv1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:lines="1"
        android:text="@string/voice_room_activity_empty_tip"
        android:textColor="@color/white"
        android:textSize="14dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/activity_empty_create_bt"
        app:layout_constraintStart_toEndOf="@id/activity_empty_star_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/activity_empty_create_bt"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/shape_00ccf9_corner16"
        android:gravity="center"
        android:lines="1"
        android:minWidth="68dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:text="@string/voice_room_activity_empty_go_create"
        android:textColor="#FFFFFF"
        android:textSize="12dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>