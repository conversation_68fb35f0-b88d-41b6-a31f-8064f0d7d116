package com.wepie.wespy.net.tcp.handler;

import android.text.TextUtils;

import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.GlobalEventFlow;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.component.game.chip.ChipApi;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.lib.api.plugins.HwApi;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.littlegame.model.GameChessboardRes;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.entity.AchieveInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.BuildConfig;
import com.wepie.wespy.R;
import com.wepie.wespy.base.GameStatusCheckManager;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.model.entity.marry.MyPropInfo;
import com.wepie.wespy.module.family.FamilyPushHandler;
import com.wepie.wespy.module.fdiscover.manager.DiscoverManager;
import com.wepie.wespy.module.firstcharge.FirstChargeConfig;
import com.wepie.wespy.module.firstcharge.util.FirstChargeUtil;
import com.wepie.wespy.module.gift.GiftInfoTransformHelper;
import com.wepie.wespy.module.notify.AppGiftInfo;
import com.wepie.wespy.module.notify.AppNotifyUtil;
import com.wepie.wespy.module.notify.CommonNotifyInfo;
import com.wepie.wespy.module.notify.RingFamilyRingInfo;
import com.wepie.wespy.module.notify.RingGiftInfo;
import com.wepie.wespy.module.notify.VipUpgradeInfo;
import com.wepie.wespy.module.shop.MyPropManager;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.dataservice.event.JackarooBattlePassRefresh;
import com.wepie.wespy.net.tcp.packet.ImPushPackets;
import com.wepie.wespy.utils.LogHandler;

import org.json.JSONException;
import org.json.JSONObject;

public class ImCommandHandler {
    private static final String TAG = "IM_CMD";
    public static final String NEW_SHARE_POST = "new_share_post";
    public static final String TASK_FINISHED = "task_finished";
    public static final String RED_DOT_UPDATE = "reddot_update";
    public static final String VIP_UPGRADE_NOTIFY = "open_vip";
    public static final String FAMILY = "family";
    public static final String BROADCAST_GIFT = "broadcast_gift";
    public static final String GIFT_RING = "gift_ring_get";
    public static final String FAMILY_WHEEL_RING = "family_wheel_ring_get";
    public static final String FAMILY_BOX_RING = "family_box_ring_get";
    public static final String SEND_GIFT_COMMON = "send_gift_common";
    public static final String HANDLE_LOG = "upload_log";
    public static final String All_NOTIFY = "all_user_notify";
    public static final String ALL_ROOT_NOTIFY = "all_root_user_notify";
    public static final String All_GAME_NOTIFY = "all_game_user_notify";
    public static final String ALL_ROOT_GAME_NOTIFY = "all_root_game_user_notify";
    public static final String REC_GIFT_PACKAGE = "recv_gift_package";
    public static final String BUY_GIFT_PACKAGE = "buy_gift_package";
    public static final String FORCE_REFRESH_AUTH_INFO = "modify_face_verify";
    public static final String CUSTOM_CMD = "custom_command";
    public static final String GAME_WIN_SCENE = "game_win";
    public static final String BALOOT_CHIP_UPDATE = "baloot_chip_change";
    public static final String APP_PUSH = "app_push";
    public static final String UPLOAD_AUDIO = "upload_audio"; // 服务器 push 上传当前的录音文件，第一个版本用于举报谩骂录音的处理.
    public static final String JK_BOARD_UPDATE = "command_update_jackaroo_board";
    public static final String JK_BATTLE_PASS_UPDATE = "battle_pass";

    public static final String HOME_CHIP_UPDATE = "generate_home_chip_change";

    public static void handlePushImCommand(GeneratedMessageLite<?, ?> message, RspHeadInfo rspHeadInfo) {
        ImPushPackets.ImCmdPush pushMsg = (ImPushPackets.ImCmdPush) message;
        String command = pushMsg.getCommand();
        String payload = pushMsg.getPayload();
        HLog.d(TAG, HLog.USR, "receive im cmd: {}, {}", command, payload);

        switch (command) {
            case NEW_SHARE_POST:
                try {
                    JSONObject json = new JSONObject(payload);
                    long timestamp = json.getLong("timestamp");
                    int uid = 0;
                    if (json.has("send_uid")) {
                        uid = json.getInt("send_uid");
                    }
                    DiscoverManager.getInstance().checkHasNewDiscover(timestamp, uid);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            case TASK_FINISHED:
                EventDispatcher.postShowTaskReddot(true);
                break;
            case RED_DOT_UPDATE:
                try {
                    RedDotInfo redDotInfo = JsonUtil.fromJson(payload, RedDotInfo.class);
                    RedDotUtil.get().update(redDotInfo);
                    RedDotUtil.get().check2UpdateRedDot(false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            case VIP_UPGRADE_NOTIFY:
                try {
                    JSONObject json = new JSONObject(payload);
                    if (json.has("uid") && json.has("level")) {
                        int uid = json.getInt("uid");
                        int level = json.getInt("level");
                        AppNotifyUtil.notifyVipUpGrade(new VipUpgradeInfo(uid, level));
                    } else {
                        TimeLogger.err("payload info error: " + payload);
                    }
                } catch (Exception e) {
                    TimeLogger.err("unknown upgrade notify");
                }
                break;
            case FAMILY:
                FamilyPushHandler.handleFamilyPush(payload);
                break;
            case BROADCAST_GIFT:
                handleBroadGift(payload);
                break;
            case FAMILY_BOX_RING:
                handleRingFamilyWheel(payload, ResUtil.getStr(R.string.family_box_text));
                break;
            case FAMILY_WHEEL_RING:
                handleRingFamilyWheel(payload, ResUtil.getStr(R.string.family_gacha_text));
                break;
            case GIFT_RING:
                handleRingGift(payload);
                break;
            case SEND_GIFT_COMMON:
                handleSendGiftCommon(payload);
                break;
            case All_NOTIFY:
            case All_GAME_NOTIFY:
                handleAllNotify(payload);
                break;
            case ALL_ROOT_NOTIFY:
            case ALL_ROOT_GAME_NOTIFY:
                if (isSuperUser()) {
                    handleAllNotify(payload);
                }
                break;
            case HANDLE_LOG:
                LogHandler.handle(payload);
                break;
            case REC_GIFT_PACKAGE:
                handleGiftPackage(payload);
                break;
            case BUY_GIFT_PACKAGE:
                FirstChargeUtil.showFirstCharge(false, false, FirstChargeConfig.DEFAULT);
                break;
            case FORCE_REFRESH_AUTH_INFO:
                ApiService.of(AuthApi.class).forceRefreshConfigs(payload);
                break;
            case CUSTOM_CMD:
                ApiService.of(HwApi.class).handleCustomCommand(payload);
                break;
            case GAME_WIN_SCENE:
                handleGameWinScene(payload);
                break;
            case BALOOT_CHIP_UPDATE:
                // handle by baloot self module
                break;
            case HOME_CHIP_UPDATE:
                ApiService.of(ChipApi.class).updateChip();
                break;
            case APP_PUSH:
                AppPushHandler.handle(payload);
                break;
            case UPLOAD_AUDIO:
                AudioPushHandler.uploadLocalAudio(payload);
                break;
            case JK_BOARD_UPDATE:
                handleChessboardAdd(payload);
                break;
            case JK_BATTLE_PASS_UPDATE:
                handleJackarooBattlePass(payload);
                break;
            default: {
                HLog.d(TAG, "unknown command {} in push im {}", command);
            }
        }
    }

    private static boolean isSuperUser() {
        if (BuildConfig.DEBUG && PrefUtil.getInstance().getBoolean(PrefUtil.KEY_IS_SUPER_USER, false)) {
            return true;
        }
        return ConfigHelper.getInstance().superAdminDevice();
    }

    private static void handleGiftPackage(String payload) {
        try {
            if (TextUtils.isEmpty(payload)) {
                TimeLogger.err("payload empty");
                return;
            }
            JSONObject json = new JSONObject(payload);
            int type = json.getInt("gift_package_type");
            FirstChargeUtil.showFirstCharge(false, true, type);
        } catch (Exception e) {
            TimeLogger.err("parse gift info error: " + e.getMessage());
        }
    }

    private static void handleBroadGift(String payload) {
        try {
            JSONObject json = new JSONObject(payload);
            if (json.has("broad_content") &&
                    json.has("gift_id") &&
                    json.has("send_uid") &&
                    json.has("recv_uid") &&
                    json.has("combo_id") &&
                    json.has("combo_times")) {
                AppGiftInfo giftInfo = new AppGiftInfo(
                        json.getInt("send_uid"),
                        json.getInt("recv_uid"),
                        json.getInt("gift_id"),
                        json.optString("combo_id", ""),
                        json.optInt("combo_times", 0),
                        json.getString("broad_content"),
                        json.optString("follow_btn_url"),
                        json.optString("follow_btn_deeplink")
                );
                AppNotifyUtil.notifyAppGift(giftInfo);
            } else {
                TimeLogger.err("payload info error: " + payload);
            }
        } catch (Exception e) {
            TimeLogger.err("parse gift info error: " + e.getMessage());
        }
    }

    private static void handleRingGift(String payload) {
        try {
            if (TextUtils.isEmpty(payload)) {
                TimeLogger.err("payload empty");
                return;
            }
            JSONObject json = new JSONObject(payload);
            int sender = json.getInt("send_uid");
            int receiver = json.getInt("recv_uid");
            int propId = json.getInt("prop_id");
            int giftId = json.getInt("gift_id");
            AppNotifyUtil.notifyRingGift(new RingGiftInfo(sender, receiver, propId, giftId));
        } catch (Exception e) {
            TimeLogger.err("parse gift info error: " + e.getMessage());
        }
    }

    private static void handleRingFamilyWheel(String payload, String source) {
        try {
            if (TextUtils.isEmpty(payload)) {
                TimeLogger.err("payload empty");
                return;
            }
            JSONObject json = new JSONObject(payload);
            int uid = json.getInt("uid");
            int propId = json.getInt("prop_id");
            AppNotifyUtil.notifyRingFamilyWheel(new RingFamilyRingInfo(uid, propId, source));
        } catch (Exception e) {
            TimeLogger.err("parse gift info error: " + e.getMessage());
        }
    }

    private static void handleSendGiftCommon(String payload) {
        try {
            if (TextUtils.isEmpty(payload)) {
                TimeLogger.err("payload empty");
                return;
            }
            GiftShowInfo showInfo = GiftInfoTransformHelper.fromGiftCommonSend(payload);
            if (showInfo != null) {
                EventDispatcher.postCommonGiftEvent(showInfo);
            } else {
                TimeLogger.err("error parse show info");
            }
        } catch (Exception e) {
            TimeLogger.err("parse gift info error: " + e.getMessage());
        }
    }

    private static void handleAllNotify(String payload) {
        try {
            AppNotifyUtil.notifyAllUser(new CommonNotifyInfo(payload));
        } catch (Exception e) {
            TimeLogger.err("parse all notify info error: " + e.getMessage());
        }
    }

    private static void handleGameWinScene(String payload) {
        try {
            AchieveInfo info = JsonUtil.fromJson(payload, AchieveInfo.class);
            // 当前是赢了之后才会 push, 这里产品逻辑要求最好是赢了才记录。
            GameStatusCheckManager.updatePlayedGameInfo(info);
        } catch (Exception e) {
            HLog.d(TAG, "error inflate achieve info {}", e);
        }
    }

    private static void handleChessboardAdd(String payload) {
        try {
            JsonElement je = JsonParser.parseString(payload);
            int boardId = je.getAsJsonObject().get("board_id").getAsInt();
            MyPropInfo myPropInfo = MyPropManager.getInstance().findMyPropInfoById(boardId);
            if (myPropInfo == null) {
                HLog.d(TAG, HLog.USR, "no board find, refresh list {}", boardId);
                MyPropManager.getInstance().getPropList(null);
            }
            HLog.d(TAG, HLog.USR, "load board res from push {}", boardId);
            GameChessboardRes.loadChessboardRes();
        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "error handle handleChessboardAdd {}, {}", payload, e);
        }
    }

    private static void handleJackarooBattlePass(String payload) {
        try {
            GlobalEventFlow.INSTANCE.postEvent(new JackarooBattlePassRefresh());
        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "error handle jk battle pass {}, {}", payload, e);
        }
    }
}
