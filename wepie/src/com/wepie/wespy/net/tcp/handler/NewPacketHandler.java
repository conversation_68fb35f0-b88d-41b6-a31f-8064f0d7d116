package com.wepie.wespy.net.tcp.handler;

import androidx.annotation.NonNull;

import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.constants.HttpCode;
import com.huiwan.libtcp.TcpConnect;
import com.huiwan.libtcp.TcpMainUtil;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.three.http.core.HttpUtil;
import com.wejoy.gamematch.net.GameMatchSender;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.libcaptcha.CaptchaCloseType;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.model.event.hwroom.MatchFailEvent;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.game.game.base.YiDunDataCallback;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.risk.RiskCaptchaHelper;
import com.wepie.wespy.net.tcp.packet.ConnectorPackets;
import com.wepie.wespy.net.tcp.packet.HWPushPackets;
import com.wepie.wespy.net.tcp.packet.HeadPackets;
import com.wepie.wespy.net.tcp.packet.PushPackets;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONObject;

/**
 * Created by geeksammao on 19/10/2017.
 */

public class NewPacketHandler {
    private static final String TAG = "NewPacketHandler";

    public static void invokeHandleMethod(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        if (checkAndIntercept(rspHeadInfo)) {
            invokeFail(rspHeadInfo);
            return;
        }
        try {
            int command = rspHeadInfo.command;
            if (command == HeadPackets.CommandType.PUSH_VALUE) {
                handleGamePush(rspHeadInfo, message);
            } else if (command == HeadPackets.CommandType.HWTCP_PUSH_VALUE) {
                handleHWPush(rspHeadInfo, message);
            } else {
                handleResponse(rspHeadInfo, message);
            }
        } catch (Exception e) {
            FLog.e(new IllegalStateException("err tcp result, command:" + rspHeadInfo.command + " type:" + rspHeadInfo.type, e));
        }
    }

    private static boolean checkAndIntercept(RspHeadInfo rspHeadInfo) {
        if (rspHeadInfo.code == HttpCode.CODE_VISITOR_LIMITED) {
            ToastUtil.show(rspHeadInfo.desc);
            JumpUtil.gotoAccountManagerSettingActivity(LibBaseUtil.getApplication());
            return true;
        }
        return false;
    }

    private static void handleResponse(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        int code = rspHeadInfo.code;
        if (code != 200) {
            handleError(rspHeadInfo, message);
        } else {
            handleNormal(rspHeadInfo, message);
        }
    }

    private static void handleGamePush(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        switch (rspHeadInfo.type) {
            case PushPackets.PushType.SEND_ROOM_MSG_VALUE:
                VoiceRoomHandler.handlePushRoomMsg(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_ROOM_SYS_VALUE:
                VoiceRoomHandler.handlePushRoomSys(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_ROOM_GIFT_VALUE:
                VoiceRoomHandler.handlePushRoomGift(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_GAME_MATCH_VALUE:
                GameMatchHandler.handlerGameMatchPush(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_GROUP_MSG_VALUE:
                GroupHandler.handlePushGroupMsg(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_VOICE_MATCH_VALUE:
                VoiceMatchHandler.handlePushVoiceMatchMsg(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_IM_COMMAND_VALUE:
                ImCommandHandler.handlePushImCommand(message, rspHeadInfo);
                break;
//            case PushPackets.PushType.SEND_FIXROOM_MSG_VALUE:
//                FixRoomHandler.handlePushMsg(message, rspHeadInfo);
//                break;
//            case PushPackets.PushType.SEND_SPY_VALUE:
//                SpyBridge.handlePushMsg(message, rspHeadInfo);
//                break;
            case PushPackets.PushType.SEND_MARRY_VALUE:
                MarryHandler.handlePushMsg(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_MULTI_GIFT_VALUE:
                VoiceRoomHandler.handlePushRoomMulGift(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_DRAW_VALUE:
                DrawPacketHandler.handlePushMsg(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_CP_VALUE:
                CpRoomHandler.handlePushMsg(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_MUSIC_VALUE:
                VoiceMusicHandler.handlePushMsg(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_AUCTION_VALUE:
                AuctionRoomHandler.handPushMsg(message, rspHeadInfo);
                break;
//            case PushPackets.PushType.SEND_SPY_V2_VALUE:
//                SpyBridge.handlePushMsgV2(message, rspHeadInfo);
//                break;
//            case PushPackets.PushType.SEND_MUSIC_HUM_VALUE:
//                MusicHumBridge.handlePushMsg(message, rspHeadInfo);
//                break;
            case PushPackets.PushType.SEND_TMPROOM_WIDGET_VALUE:
                VoiceRoomHandler.handlePushTmpWidget(message, rspHeadInfo);
                break;
            case PushPackets.PushType.SEND_VIDEO_VALUE:
                VideoRoomHandler.handleVideoRoomPush(message, rspHeadInfo);
                break;
//            case PushPackets.PushType.SEND_QA_GAME_VALUE:
//                GTABridge.handlePushMsg(message, rspHeadInfo);
//                break;
            default: {
                HLog.d(TAG, "un-handled push type: {}", rspHeadInfo.type);
            }
        }
    }

    private static void handleHWPush(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        int type = rspHeadInfo.type;
        HLog.d(TAG, HLog.USR, "handleHWPush, type=" + HWPushPackets.HWTcpPushType.forNumber(type));
        switch (type) {
            case HWPushPackets.HWTcpPushType.HWTCP_PUSH_UNDEFINED_VALUE:
                break;
            case HWPushPackets.HWTcpPushType.HWTCP_PUSH_ROOM_SYS_VALUE:
                HWRoomHandler.handlerPush(message, rspHeadInfo);
                break;
            case HWPushPackets.HWTcpPushType.HWTCP_PUSH_MATCH_VALUE:
                HWMatchHandler.handlerPush(message, rspHeadInfo);
                break;
            case HWPushPackets.HWTcpPushType.HWTCP_PUSH_CHAT_VALUE:
                HWChatHandler.handlerPush(message, rspHeadInfo);
                break;
//            case HWPushPackets.HWTcpPushType.HWTCP_PUSH_BALOOT_VALUE:
//                由 baloot 模块自己取出消息处理
//            case HWPushPackets.HWTcpPushType.HWTCP_PUSH_MAJIANG_TW_VALUE:
//                MJPacketHandler.INSTANCE.handleRspPacket(true, message, rspHeadInfo);
//                break;
        }
    }

    private static void handleError(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        int command = rspHeadInfo.command;
        int code = rspHeadInfo.code;
        if (code == RspHeadInfo.ERROR_CODE_CHILD_LIMIT_NEED_CERT) {
//            DialogUtil.showNeedCertificateDialog(rspHeadInfo.desc);
            rspHeadInfo.desc = "";
        } else if (code == RspHeadInfo.ERROR_CODE_CHILD_LIMIT_OVER_LIMIT) {
//            DialogUtil.showChildLimitDialog(rspHeadInfo.desc);
            rspHeadInfo.desc = "";
        } else if (code == RspHeadInfo.COMMON_ERRCODE_RISK_VERIFY) {
            // 调用风控接口检查验证结果
            DialogUtil.showCaptchaDialogActivity(rspHeadInfo.desc, "", new YiDunDataCallback<>() {
                @Override
                public void onClose(@NonNull CaptchaCloseType closeType) {
                    RiskCaptchaHelper.INSTANCE.postNotifyVerifyCloseRequest("", closeType == CaptchaCloseType.VERIFY_SUCCESS_CLOSE);
                }

                @Override
                public void onCall(String data) {
                    verifyRisk(data);
                }
            });
            rspHeadInfo.desc = "";
        } else if (command == HeadPackets.CommandType.TMPROOM_VALUE) {
            VoiceRoomHandler.handleFailMsg(message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.GROUP_VALUE &&
                code == RspHeadInfo.ERROR_CODE_GROUP_NOT_IN) {
            GroupHandler.handleKicked(rspHeadInfo.gid, code, false);
        } else if (command == HeadPackets.CommandType.GROUP_VALUE &&
                code == RspHeadInfo.ERROR_CODE_GROUP_NOT_EXIST) {
            GroupHandler.handleKicked(rspHeadInfo.gid, code, false);
        } else if (command == HeadPackets.CommandType.GROUP_VALUE &&
                code == RspHeadInfo.ERROR_CODE_GROUP_FAMILY_KICK) {
            GroupHandler.handleKicked(rspHeadInfo.gid, code, true);
        } else if (command == HeadPackets.CommandType.GAMEMATCH_VALUE) {
            GameMatchHandler.handlerGameMatchError(message, rspHeadInfo);
//        } else if (command == HeadPackets.CommandType.FIXROOM_VALUE) {
//            FixRoomHandler.handleErrorMsg(message, rspHeadInfo);
//        } else if (command == HeadPackets.CommandType.SPY_VALUE) {
//            SpyBridge.handleErrorMsg(message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.MARRY_VALUE) {
            MarryHandler.handleErrorMsg(message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.MUSIC_VALUE) {
            VoiceMusicHandler.handleFailMsg(message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.CP_VALUE) {
            CpRoomHandler.handleFailMsg(message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.AUCTION_VALUE) {
            AuctionRoomHandler.handleFailMsg(message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.HWROOM_VALUE) {
            HWRoomHandler.handlerFailMsg(message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.HWChat_VALUE) {
            HWChatHandler.handlerFailMsg(message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.HWGift_VALUE) {
            HWGiftHandler.handlerFailMsg(message, rspHeadInfo);
//        }else if(command==HeadPackets.CommandType.MAJIANGTW_VALUE){
//            MJPacketHandler.INSTANCE.handleRspPacket(false, message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.Video_VALUE) {
            VideoRoomHandler.handleFailMsg(message, rspHeadInfo);
        } else if (command == HeadPackets.CommandType.HWMATCH_VALUE) {
            EventBus.getDefault().post(new MatchFailEvent(rspHeadInfo));
        }

        rspHeadInfo.message = message;
        invokeFail(rspHeadInfo);
    }

    private static void handleNormal(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        int command = rspHeadInfo.command;
        switch (command) {
            case HeadPackets.CommandType.CONNECTOR_VALUE:
                if (rspHeadInfo.type == ConnectorPackets.ConnectorType.BIND_USER_VALUE) {
                    handleBindUser(rspHeadInfo, message);
                }
                break;
            case HeadPackets.CommandType.GROUP_VALUE:
                GroupHandler.handlerMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.TMPROOM_VALUE:
                VoiceRoomHandler.handleMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.TMPWIDGET_VALUE:
                VoiceRoomHandler.handleWidgetMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.GAMEMATCH_VALUE:
                invokeSuccess(rspHeadInfo, message);
                GameMatchHandler.handlerGameMatch(rspHeadInfo, message);
                break;
            case HeadPackets.CommandType.VOICEMATCH_VALUE:
                break;
//            case HeadPackets.CommandType.FIXROOM_VALUE:
//                FixRoomHandler.handleFixRoom(message, rspHeadInfo);
//                break;
//            case HeadPackets.CommandType.SPY_VALUE:
//                SpyBridge.handleMsg(message, rspHeadInfo);
//                break;
            case HeadPackets.CommandType.MARRY_VALUE:
                MarryHandler.handleMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.DRAW_VALUE:
                DrawPacketHandler.handleMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.CP_VALUE:
                CpRoomHandler.handlerCpRoom(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.MUSIC_VALUE:
                VoiceMusicHandler.handleMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.AUCTION_VALUE:
                AuctionRoomHandler.handleMsg(message, rspHeadInfo);
                break;
//            case HeadPackets.CommandType.MUSIC_HUM_VALUE:
//                MusicHumBridge.handleMsg(message, rspHeadInfo);
//                break;
            case HeadPackets.CommandType.HWROOM_VALUE:
                HWRoomHandler.handlerMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.HWMATCH_VALUE:
                HWMatchHandler.handlerMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.HWChat_VALUE:
                HWChatHandler.handlerMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.HWGift_VALUE:
                HWGiftHandler.handlerMsg(message, rspHeadInfo);
                break;
            case HeadPackets.CommandType.BigWinner_VALUE:
                BigWinnerHandler.handle(message, rspHeadInfo);
                break;
//            case HeadPackets.CommandType.BALOOT_VALUE:
//            //走默认流程即可
//            case HeadPackets.CommandType.MAJIANGTW_VALUE:
//                MJPacketHandler.INSTANCE.handleRspPacket(false, message, rspHeadInfo);
//                break;
            case HeadPackets.CommandType.Video_VALUE:
                VideoRoomHandler.handleMsg(message, rspHeadInfo);
                break;
//            case HeadPackets.CommandType.QAGAME_VALUE:
//                GTABridge.handleMsg(message, rspHeadInfo);
//                break;
            default:
        }
        invokeSuccess(rspHeadInfo, message);
    }

    private static void handleBindUser(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        ConnectorPackets.BindUserRsp bindUserRsp = (ConnectorPackets.BindUserRsp) message;
        TcpConnect.getInstance().setConnectorHeartBeatRate(bindUserRsp.getHeartbeatFreq());
        GroupHandler.syncAllGroup();
        VoiceRoomHandler.syncRoom();

        //team sync
        int tid = LittleGame.getTeamInfo().getTid();
        if (tid > 0) {
            GameMatchSender.syncTeamReq(null);
        }

        //sync 常驻房
//        FixRoomService.getInstance().getFixRoomListWithSync(null);

        invokeSuccess(rspHeadInfo, message);
        EventDispatcher.postTcpBindUser();
    }

    public static void invokeFail(RspHeadInfo rspHeadInfo) {
        TcpMainUtil.invokeFail(rspHeadInfo);
    }

    public static void invokeSuccess(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        TcpMainUtil.invokeSuccess(rspHeadInfo, message);
    }

    public static void verifyRisk(String verifyInfo) {
        String captchaId = "";
        String validate = "";
        try {
            JSONObject verifyObj = new JSONObject(verifyInfo);
            captchaId = verifyObj.optString("captcha_id");
            validate = verifyObj.optString("validate");
        } catch (Exception e) {
            HLog.e(TAG, HLog.USR, "verifyRisk Exception={}", e);
        }

        HttpUtil.newBuilder().uri(UrlConfig.URL_VERIFY_RISK)
                .addParam("target_uid", String.valueOf(LoginHelper.getLoginUid()))
                .addParam("captcha_id", captchaId)
                .addParam("validate", validate)
                .build()
                .post(new LifeDataCallback<String>(GlobalLife.INSTANCE) {
                    @Override
                    public void onSuccess(Result<String> result) {

                    }

                    @Override
                    public void onFail(int code, String msg) {
                        ToastUtil.show(msg);
                    }
                });
    }
}
