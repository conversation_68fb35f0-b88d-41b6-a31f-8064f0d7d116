package com.wepie.wespy.net.okhttp;

import android.text.TextUtils;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.common.GlobalConfig;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.configservice.international.service.GlobalConfigObserver;
import com.huiwan.constants.GameType;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.lib.api.plugins.IDeviceApi;
import com.huiwan.libtcp.AppDataInterface;
import com.huiwan.libtcp.TcpMainUtil;
import com.huiwan.libtcp.TcpSocketInterface;
import com.huiwan.libtcp.depend.ServerConfigCallback;
import com.huiwan.libtcp.depend.TcpAddressInterface;
import com.huiwan.libtcp.model.ServerConfig;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.LoginHelper;
import com.three.http.core.HttpDnsConfig;
import com.three.http.core.HttpUtil;
import com.three.http.dns.AliDns;
import com.wejoy.weplay.shumei.ShumeiUtil;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.BuildConfig;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.helper.shence.ShenceUtil;
import com.wepie.wespy.net.http.api.DeviceApi;
import com.wepie.wespy.net.okhttp.interceptor.WeCodeInterceptor;
import com.wepie.wespy.net.okhttp.interceptor.WeEncInterceptor;
import com.wepie.wespy.net.okhttp.interceptor.WeNetInterceptor;
import com.wepie.wespy.net.okhttp.interceptor.WeParamInterceptor;
import com.wepie.wespy.net.okhttp.interceptor.WeSignInterceptor;
import com.wepie.wespy.net.okhttp.interceptor.WeZipInterceptor;
import com.wepie.wespy.net.tcp.handler.NewPacketHandler;
import com.wepie.wespy.net.tcp.handler.PacketHandler;
import com.wepie.wespy.net.tcp.packet.ConnectorPackets;
import com.wepie.wespy.utils.PackageUtil;
import com.wepie.wespy.utils.ShellPackageUtil;
import com.wepie.wespy.utils.ShumeiDeviceProfile;
import com.wepie.wpdd.DeviceIdHelper;

import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Created by three on 2018/1/25.
 */

public class WeNetConfig {
    private static final String TAG = "WeNetConfig";
    private static final String CHAT_HOST = "super_chat_host";
    private static final String CHAT_PORT = "super_chat_port";
    private static final String CONN_HOST = "super_conn_host";
    private static final String CONN_PORT = "super_conn_port";

    public static void init() {
        httpInit();
        initGlobalConfig();
    }

    public static void socketInit(TcpSocketInterface tcpSocketInterface) {
        TcpMainUtil.init(new TcpAddressInterface() {
            @Override
            public void getChatServer(DataCallback<GlobalConfig.ConfigItem> callback) {
                getTcpConfigItem(GlobalConfigManager.chat_server, CHAT_HOST, CHAT_PORT, callback);
            }

            @Override
            public void getVoiceRoomServer(ServerConfigCallback serverConfigCallback) {
                getTcpConfigItem(GlobalConfigManager.connector_server, CONN_HOST, CONN_PORT, new DataCallback<GlobalConfig.ConfigItem>() {
                    @Override
                    public void onCall(GlobalConfig.ConfigItem data) {
                        ServerConfig config = new ServerConfig();
                        config.connserver.address = data;
                        config.connserver.useKcp = ConfigHelper.getInstance().useKcp();
                        serverConfigCallback.onSuccess(config);
                    }

                    @Override
                    public void onFailed(int code, String msg) {
                        if (serverConfigCallback != null) serverConfigCallback.onFail(code, msg);
                        HLog.aliLog(AliNetLogUtil.PORT.http, "getServiceConfig", AliNetLogUtil.TYPE.err,
                                "getServiceConfig api failed = " + msg);
                    }
                });
            }
        });

        TcpMainUtil.setHuiwanPacketHandlerInterface((rspHeadInfo, message) -> {
            NewPacketHandler.invokeHandleMethod(rspHeadInfo, message);
            return true;
        });

        TcpMainUtil.setPacketHandlerInterface(new PacketHandler());

        TcpMainUtil.registerTcpSocketInterface(tcpSocketInterface);
        TcpMainUtil.setAppDataInterface(new AppDataInterface() {
            @Override
            public int getUid() {
                return LoginHelper.getLoginUid();
            }

            @Override
            public int getMaxGameType() {
                return GameType.MAX_GAME_TYPE;
            }

            @Override
            public String getSid() {
                return LoginHelper.getSid();
            }

            @Override
            public ConnectorPackets.InstanceId getInstanceId() {
                return ShellPackageUtil.getInstanceId();
            }

            @Override
            public String getVersionName() {
                return PackageUtil.getVersionName();
            }

            @Override
            public String getEncryptDid() {
                return DeviceIdHelper.getEncryptDid();
            }

            @Override
            public boolean isLogin() {
                return LoginHelper.isLogin();
            }
        });
    }

    public static void httpInit() {
        HttpUtil.prepareCronet();
        HttpUtil.configBuilder()
                .domain(UrlConfig.getDomain())
                .timeOut(15)
                .needSign(true)
                .retryCount(0)
                .jsonDataKey("result")
                .jsonMessageKey("msg")
                .log(BuildConfig.DEBUG)
                .setICdn(WeNetConfig::getCdnConfigMap)
                .addInterceptor(new WeCodeInterceptor())
                .addInterceptor(new WeParamInterceptor(true))
                .addInterceptor(new WeSignInterceptor())
                .addInterceptor(new WeEncInterceptor())
                .addInterceptor(new WeZipInterceptor())
                .addInterceptor(new WeNetInterceptor());
    }

    private static void initGlobalConfig() {
        GlobalConfigObserver configObserver = new GlobalConfigObserver() {
            @Override
            public void updateCacheSuccess() {
                GlobalConfig.ConfigItem item = GlobalConfigManager.getInstance().getConfig(GlobalConfigManager.HTTP_API);
                ApiService.of(IDeviceApi.class).init(LibBaseUtil.getApplication());
                if (null != item) {
                    String domain = item.host + ":" + item.port;
                    UrlConfig.updateDomainCache(domain);
                    HttpUtil.configBuilder().domain(UrlConfig.getDomain());
                    ShumeiUtil.initConfig(LibBaseUtil.getApplication(), PackageUtil.getChannel(), domain, ShumeiDeviceProfile.INSTANCE.getShumeiCallback());
                    ShenceUtil.registerSuperProperties(false);
                }
            }
        };
        GlobalConfigManager.getInstance().addObserver(configObserver);
//        GlobalConfigManager.getInstance().init(UrlConfig.getGlobalDomain());
//        TrackUtil.request(com.huiwan.configservice.http.UrlConfig.GLOBAL_PATH, LibBaseUtil.isFirstIn());
    }

    private static Map<String, List<String>> getCdnConfigMap() {
        return GlobalConfigManager.getInstance().getCDNConfig();
    }

    /**
     * <p>取内存 config item. </p>
     * <p>有 debug, 用 bug host, port, 不走 http dns </p>
     * <p>没有 debug </p>
     * <p>开启 httpDns, 走 httpDns</p>
     * <p>没有开启 httpDns, 直接返回内存 config item</p>
     */
    private static void getTcpConfigItem(String itemKey, String dbgHostKey, String dbgPortKey, DataCallback<GlobalConfig.ConfigItem> callback) {
        if (callback == null) {
            HLog.e(TAG, HLog.USR, "get server item callback null");
            return;
        }
        GlobalConfig.ConfigItem item = GlobalConfigManager.getInstance().getConfig(itemKey);
        if (item == null) {
            HLog.e(TAG, HLog.USR, "get server item null for {}", itemKey);
            callback.onFailed(ServerConfig.GLOBAL_CONFIG_ERROR, ServerConfig.GLOBAL_CONFIG_ERROR_MSG);
            return;
        }
        GlobalConfig.ConfigItem itemToUse = new GlobalConfig.ConfigItem(item);
        boolean dbgHostChange = false;
        if (LibBaseUtil.buildDebug()) {
            try {
                String host = PrefUtil.getInstance().getString(dbgHostKey, "");
                String port = PrefUtil.getInstance().getString(dbgPortKey, "");
                if (!TextUtils.isEmpty(host)) {
                    itemToUse.host = host;
                    HLog.d(TAG, "host[{}], {}", itemKey, host);
                }
                if (!TextUtils.isEmpty(port)) {
                    itemToUse.port = Integer.parseInt(port);
                    HLog.d(TAG, "port[{}], {}", itemKey, port);
                }
                if (itemToUse.host != null && itemToUse.host.equals(item.host)) {
                    dbgHostChange = true;
                }
            } catch (Exception e) {
                HLog.e(TAG, "error parse host or port: {}", e);
            }
        }
        if (!dbgHostChange && HttpDnsConfig.INSTANCE.useHttpDns()) {
            ThreadUtil.runInSingleThread(() -> {
                String ip = AliDns.Companion.findIpByHostSync(itemToUse.host);
                if (ip != null) {
                    itemToUse.ip = ip;
                }
                ThreadUtil.runOnUiThread(() -> callback.onCall(itemToUse));
            });
        } else {
            callback.onCall(item);
        }

    }
}
