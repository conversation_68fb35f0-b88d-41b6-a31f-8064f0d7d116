package com.wepie.wespy.module.draw.core.share;

import android.content.Context;
import android.graphics.Bitmap;

import androidx.annotation.Nullable;

import com.huiwan.store.file.FileConfig;
import com.huiwan.store.file.FileManager;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.constants.GameType;
import com.huiwan.lib.api.ApiService;
import com.wepie.lib.api.plugins.share.IShareApi;
import com.wepie.lib.api.plugins.share.ShareCallback;
import com.wepie.lib.api.plugins.share.ShareInfo;
import com.wepie.lib.api.plugins.share.ShareResult;
import com.wepie.lib.api.plugins.share.ShareType;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.wespy.R;
import com.wepie.wespy.base.share.WeCircleShareInfo;
import com.wepie.wespy.model.entity.fixroom.DrawGuessGameInfo;
import com.wepie.wespy.model.entity.fixroom.DrawGuessGamerInfo;
import com.wepie.wespy.module.draw.core.info.PlayInfo;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019/3/25.
 */
public class DrawShareUtil {

    /**
     * 桌游房：
     * 画板上分享
     * 结束框分享
     * 语音房：
     * 画板上分享
     * 结束框分享
     */
    public static void showShareDialog(final Context context, final Bitmap bitmap, boolean fromDialog, final DrawGuessGameInfo gameInfo, int traceSeq) {
        boolean isFromFixRoom = false; // ContextUtil.getActivityFromContext(context) instanceof FixRoomActivity;

        int tmpShowId = 0;//语音房分享，弹窗上与画板上分享区分
        List<Integer> uidList = new ArrayList<>();//绘画的人
        String word;//绘画词语
        long timeTmp;//桌游写死1分钟，语音房时间实际计算

        DrawGuessGameInfo.TmpShowInfo tmpShowInfo = gameInfo.getTmpShowInfo();

        if (isFromFixRoom) {
            word = gameInfo.getWord();
            timeTmp = 60 * 1000;
            uidList.add(gameInfo.getDrawUid());
        } else {
            if (fromDialog) {
                tmpShowId = tmpShowInfo.getId();
                uidList.addAll(tmpShowInfo.getDrawUsers());
                timeTmp = tmpShowInfo.getTmpDrawTotalTimeMs();
                word = tmpShowInfo.getWord();
            } else {
                uidList.addAll(gameInfo.getTmpDrawerUidList());
                timeTmp = TimeUtil.getServerTime() - gameInfo.getTmpDrawStartTimeMs();
                word = gameInfo.getWord();
            }
        }

        if (timeTmp <= 1000 * 60) {//不足一分钟
            timeTmp = 1000 * 60;
        } else if (timeTmp > 1000 * 60 * 60 * 8) {
            timeTmp = 1000 * 60 * 60 * 8;
        }

        final long time = timeTmp;

        int drawBoardIdTmp = 0;
        DrawGuessGamerInfo guessGamerInfo = gameInfo.getDrawingGamer();
        if (guessGamerInfo != null) drawBoardIdTmp = guessGamerInfo.getDrawBoardId();
        final int drawBoardId = drawBoardIdTmp;
        showShareDialog(context, bitmap, uidList, word, time, drawBoardId, traceSeq, tmpShowId);
    }

    private static void showShareDialog(final Context mContext, final Bitmap pathBitmap, final List<Integer> uidList, final String word, final long time, final int drawBoardId, final int traceSeq, final int tmpShowId) {
        DrawShareUtil.getShareBitmap(mContext, pathBitmap, uidList, word, time, false, new LoadCallback() {
            @Override
            public void onFinish(Bitmap bitmap) {
                ShareInfo shareInfo = new ShareInfo();
                shareInfo.setTitle(ConfigHelper.getInstance().getMyShareTitle());
                shareInfo.setContent(ConfigHelper.getInstance().getMyShareDesc());
                shareInfo.setBitmap(bitmap);
                WeCircleShareInfo weCircleShareInfo = new WeCircleShareInfo();
                weCircleShareInfo.topicId = 0;
                weCircleShareInfo.bitmap = bitmap;
                weCircleShareInfo.drawTmpShowId = tmpShowId;
                weCircleShareInfo.drawTraceSeq = traceSeq;
                weCircleShareInfo.drawBoardId = drawBoardId;
                PlayInfo playInfo = new PlayInfo();
                weCircleShareInfo.drawPlayInfo = playInfo;
                playInfo.setDrawUserList(uidList);
                playInfo.setDrawTime(time);
                playInfo.setDrawBoardId(drawBoardId);

                shareInfo.setExtraInfo(weCircleShareInfo);

                //添加分享的打点信息
                shareInfo.setShareContentType(ShareInfo.SHARE_CONTENT_TYPE_IMG);
                shareInfo.screenName = TrackScreenName.SHARE_PAGE;
                shareInfo.scene = TrackString.SCENE_GAME;
                shareInfo.gameType = GameType.GAME_TYPE_DRAW_GUESS;
                ApiService.of(IShareApi.class).showPictureShareDialog(mContext, shareInfo, new ShareCallback() {
                    @Override
                    public boolean onShare(ShareResult data) {
                        if (data.shareType == ShareType.saveBmp) {
                            ToastUtil.show(data.msg);
                        }
                        return false;
                    }
                });
            }
        });
    }


    public static void getShareBitmap(final Context context, Bitmap pathBitmap, List<Integer> drawUserList, final String word, long time, boolean hasMargin, final LoadCallback callback) {
        DrawShareView drawShareView = new DrawShareView(context, hasMargin);
        drawShareView.refresh(pathBitmap, drawUserList, word, time, new LoadCallback() {
            @Override
            public void onFinish(@Nullable Bitmap bitmap) {
                if (bitmap == null) {
                    ToastUtil.show(R.string.draw_info_error);
                } else {
                    callback.onFinish(bitmap);
                }
            }
        });
    }

    /**
     * 保存一张图片（压缩策略： 图片质量从60开始上涨，保证图片在60K以上）
     *
     * @param bitmap 需要保存的bitmap
     * @return 是否保存成功
     */
    public static String saveBitmapTemp(Bitmap bitmap) {
        String name = "draw_circle_img_" + System.currentTimeMillis() + ".jpg";
        File file = FileManager.getFile(FileConfig.getImageCacheDirPath(), name);
        try {
            FileUtil.safeDeleteFile(file);
            FileUtil.createFile(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 60, baos);
            if (baos.size() / 1024 < 60) {
                baos.reset();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 70, baos);
                if (baos.size() / 1024 < 60) {
                    baos.reset();
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 80, baos);
                }
            }
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(baos.toByteArray());
            fos.flush();
            fos.close();
            baos.reset();
            baos.close();
            return file.getAbsolutePath();
        } catch (Exception e) {
            FileUtil.safeDeleteFile(file);
            return null;
        }
    }

}
