package com.wepie.wespy.module.login.login;

import android.app.Activity;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.common.GlobalConfig;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.international.regoin.RegionCancelEvent;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.configservice.international.service.GlobalConfigObserver;
import com.huiwan.constants.LoginSource;
import com.huiwan.lib.api.ApiService;
import com.huiwan.module.webview.WebActivity;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.BlockUser;
import com.huiwan.user.entity.FriendInfo;
import com.huiwan.user.entity.User;
import com.huiwan.user.http.callback.UserCallback;
import com.huiwan.voiceservice.VoiceManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.helper.BirthdayHelper;
import com.wejoy.weplay.login.ApmLoginActionKt;
import com.wepie.lib.api.plugins.track.LoginTraceHelper;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.liblog.LogUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.skynet.apm.Apm;
import com.wepie.wespy.R;
import com.wepie.wespy.base.OnJoinChannel;
import com.wepie.wespy.base.appfyers.AppsFlyerUtil;
import com.wepie.wespy.base.startup.LoginWaitInitializer;
import com.wepie.wespy.helper.customdialog.CustomDialogUtil;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.model.entity.UserSwitchInfo;
import com.wepie.wespy.module.abtest.AbTestNormalManager;
import com.wepie.wespy.module.chat.send.face.EmoticonHelper;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.login.helper.LogoutHelper;
import com.wepie.wespy.module.login.start.InitUtil;
import com.wepie.wespy.module.lookme.activity.VisitorManager;
import com.wepie.wespy.module.msgroaming.MsgRoamingUtil;
import com.wepie.wespy.net.http.api.SwitchApi;
import com.wepie.wespy.utils.FirebaseHelper;
import com.wepie.wpdd.DeviceIdHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.List;


/**
 * Created by zhuguangwen on 15/3/6.
 * email <EMAIL>
 */
public class UserLoginCallback implements UserCallback {

    private static final String TAG = "UserLoginCallback";
    private LoginSource loginSource = LoginSource.none;
    private final Activity loginActivity;
    private final ProgressDialogUtil dialog = new ProgressDialogUtil();

    public UserLoginCallback(Activity activity) {
        loginActivity = activity;
        EventBus.getDefault().register(this);
    }

    public UserLoginCallback(Activity activity, LoginSource loginSource) {
        loginActivity = activity;
        this.loginSource = loginSource;
        EventBus.getDefault().register(this);
    }

    @Override
    public void onSuccess(User user, List<FriendInfo> friendList, List<BlockUser> blockUserList, List<User> userList, boolean newUser, boolean needMobile, boolean hasPushId) {
        ApmLoginActionKt.loginActionSuccess();
        Apm.recordPeriod("login_action", false);
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        LogoutHelper.clearAllInstance();
        VoiceManager.getInstance().setOnJoinChannelListener(new OnJoinChannel());
        LoginHelper.saveLoginUser(user);
        LoginHelper.setLoginTime(System.currentTimeMillis());

        int uid = user.getUid();
        LogUtil.userInit(uid, DeviceIdHelper.getDid());
        UserService.get().onLogin(user, blockUserList);

        UserService.get().refreshInviteBlockList();
        VisitorManager.getInstance().getInvisibleUidList();//登录成功刷新隐身列表
        MsgRoamingUtil.onLogin();

        PrefUserUtil.getInstance().setBoolean(PrefUserUtil.IS_NEW_USER, newUser);
        LoginHelper.setIsNewUser(newUser);

        updateTcpInfo(needMobile);

        AppsFlyerUtil.login(newUser, uid, loginSource.name());
        String uidStr = Integer.toString(uid);
        ApiService.of(TrackApi.class).login(uidStr);
        LastLoginUtil.setLastLoginType();
        EmoticonHelper.getHelper().forceUpdateFavorite();
        LoginWaitInitializer.notifyLogin();
        FirebaseHelper.setUserID(LibBaseUtil.getApplication(), uidStr);
        HLog.d(TAG, HLog.USR, "onSuccess!");
        LoginTraceHelper.trackProcess("1", "authorization", loginSource.name(), "");
        ConfigHelper.getInstance().checkAllConfig();
    }

    // 只有登陆成功之后才能知道服务器分配的TCP端口号
    private void updateTcpInfo(boolean needMobile) {
        GlobalConfigManager manager = GlobalConfigManager.getInstance();
        GlobalConfigObserver observer = new GlobalConfigObserver() {
            @Override
            public void requestResultBack(boolean isSuccess, GlobalConfig globalConfig, int code, String msg) {
                HLog.d(TAG, HLog.USR, "updateTcpInfo, needMobile=" + needMobile + ", isSuccess=" + isSuccess + ", code=" + code + ", msg=" + msg);
                GlobalConfigManager.getInstance().removeObserver(this);
                GlobalConfigManager.getInstance().updateCacheIfNeed(globalConfig);
                loginSuccess();
            }
        };
        manager.addObserver(observer);
        manager.request(manager.getRegion());
        ILife life = ContextUtil.getLife(loginActivity);
        if (life != null) {
            life.onDestroy(() -> {
                manager.removeObserver(observer);
                return null;
            });
        }
    }

    private void loginSuccess() {
        dialog.hideLoading();
        updatePreferLanguage();
        if (BirthdayHelper.needJumpUserCompleteOrBirthPage()) {
            JumpUtil.gotoCompleteUserInfoOrBirthActivity(loginActivity);
        } else {
            InitUtil.initWithLogin();
            JumpUtil.gotoMainActivityFromLogin(loginActivity);
        }
        AbTestNormalManager.getInstance().requestAbTestData();
    }

    @Override
    public void onFailure(int code, String description) {
        ApmLoginActionKt.loginActionFailed();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        LoginTraceHelper.trackProcess("0", "authorization", loginSource.name(), description);
        dialog.hideLoading();

        Activity activity = loginActivity;
        if (loginActivity != null && !loginActivity.isFinishing()) {
            activity = ActivityTaskManager.getInstance().getTopActivity();
        }

        if (activity != null && !activity.isFinishing()) {
            Activity finalActivity = activity;
            CustomDialogUtil.showForbiddenUserDialog(activity, description, new DialogBuild.DialogCallback() {
                @Override
                public void onClickCancel() {
                    if (code == -1) { //网络链接错误
                        WebActivity.goWithoutActionBar(finalActivity,
                                ConfigHelper.getInstance().getRegionCommonConfig().getCustomerService().getRequestFailedContactLinkUrl());
                    } else {
                        WebActivity.goWithoutActionBar(finalActivity,
                                ConfigHelper.getInstance().getRegionCommonConfig().getCustomerService().getLoginErrorContactLinkUrl());
                    }
                }

                @Override
                public void onClickSure() {

                }
            }, null);
        }
    }

    @Override
    public void onStart() {
        dialog.showLoading(loginActivity, ResUtil.getStr(R.string.login_progress), false);
    }

    /**
     * 用户取消的时候关闭弹窗
     *
     * @param event
     */
    @Subscribe
    public void cancelLoading(RegionCancelEvent event) {
        dialog.hideLoading();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    @Override
    public void onFinish() {
    }

    private static void updatePreferLanguage() {
        SwitchApi.getUserSwitch(new LifeDataCallback<>(GlobalLife.INSTANCE) {

            @Override
            public void onSuccess(Result<UserSwitchInfo> result) {
                PrefUtil.getInstance().setString(PrefUtil.PREFER_LANG, result.data.preferenceLang.lang);
            }

            @Override
            public void onFail(int code, String msg) {
            }
        });
    }

}