package com.wepie.wespy.module.login.launch

import android.text.TextUtils
import com.huiwan.base.ActivityTaskManager
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.common.GlobalConfig
import com.huiwan.base.util.JsonUtil
import com.huiwan.base.util.NetWorkUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.ConfigHelper.UpdateCallback
import com.huiwan.configservice.international.service.GlobalConfigManager
import com.huiwan.configservice.international.service.GlobalConfigObserver
import com.huiwan.configservice.international.service.IGlobalConfigStrategy
import com.huiwan.store.PrefUserUtil
import com.huiwan.store.PrefUtil
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.three.http.callback.LifeDataCallback
import com.wejoy.weplay.ex.GlobalLife
import com.wejoy.weplay.helper.BirthdayHelper
import com.wepie.deeplinkbase.DeepLinkUtil
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.liblog.LogUtil
import com.wepie.liblog.main.HLog
import com.wepie.skynet.apm.Apm
import com.wepie.wespy.base.SecB
import com.wepie.wespy.helper.shence.ShenceEvent
import com.wepie.wespy.helper.shence.ShenceUtil
import com.wepie.wespy.model.entity.UserSwitchInfo
import com.wepie.wespy.module.abtest.AbTestNormalManager
import com.wepie.wespy.module.advertisement.AdvertiseManager
import com.wepie.wespy.module.advertisement.AdvertisementEntity
import com.wepie.wespy.module.login.start.InitUtil
import com.wepie.wespy.net.http.api.SwitchApi
import com.wepie.wespy.utils.TeenModeUtil
import com.wepie.wpdd.DeviceIdHelper
import com.wepie.wplink.WPLinkUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 启动逻辑中业务部分控制中心
 */
object LaunchCenter {

    private val _uiEvent = MutableSharedFlow<LaunchUiEvent>(0)

    /**
     * UI 跳转相关事件.
     */
    val uiEvent = _uiEvent.asSharedFlow()

    /**
     * 本地配置是否已经加载完成
     */
    private val localReady = AtomicBoolean(false)

    /**
     * 第一次同包分服配置准备好，是需要后续准备跳转登录页，其它情况
     * 是同包分服状态切换，此时无需关心跳转
     */
    private val firstGlobalReady = AtomicBoolean(true)

    private val strategy: IGlobalConfigStrategy by lazy {
        RetryGlobalConfigStrategy()
    }

    /**
     * 启动事件入口 Event
     */
    fun onEvent(event: LaunchEvent) {
        val key = "LaunchCenter#onEvent_${event.desc}"
        Apm.recordPeriod(key, true)
        handleEvent(event)
        Apm.recordPeriod(key, false)
    }

    /**
     * 启动事件分发
     */
    private fun handleEvent(event: LaunchEvent) {
        when (event) {
            LaunchEvent.AppLaunch -> {
                initGlobalConfig()
            }

            is LaunchEvent.ReLoadConfig -> {
                if (event.global) {
                    reloadGlobalConfig()
                } else {
                    checkRegionConfig(LoginHelper.isLogin())
                }
            }

            is LaunchEvent.GlobalConfigReady -> {
                onGlobalConfigReady(event.firstTime)
            }

            is LaunchEvent.RegionConfigReady -> {
                if (event.loginBefore) {
                    jumpAfterLogin()
                    loginDelayTasks()
                } else {
                    jumpLoginPage()
                }
            }

            LaunchEvent.UserSigned -> {
                checkRegionConfig(true)
            }

            LaunchEvent.StartPageShow -> {
                if (localReady.get()) {
                    if (LoginHelper.isLogin()) {
                        jumpAfterLogin()
                    } else {
                        jumpLoginPage()
                    }
                }
            }
        }
    }


    private val configObserver: GlobalConfigObserver = object : GlobalConfigObserver() {
        var showFailDialog = false
        override fun updateCacheSuccess() {
            Apm.recordPeriod("LaunchCenter#initGlobalConfig:net", false)
            val item = GlobalConfigManager.getInstance().getConfig(GlobalConfigManager.HTTP_API)
            if (null != item) {
                onEvent(LaunchEvent.GlobalConfigReady(firstGlobalReady.getAndSet(false)))
            }
        }

        override fun requestResultBack(
            isSuccess: Boolean,
            globalConfig: GlobalConfig?,
            code: Int,
            msg: String?
        ) {
            GlobalConfigManager.getInstance().removeObserver(this)
            if (!isSuccess) {
                Apm.recordTimePoint("LaunchCenter#initGlobalConfig:net_failed")
                Apm.reportTimePoint()
            }
            if (!GlobalConfigManager.getInstance().isReady && !showFailDialog && !isSuccess) {
                showFailDialog = true
                MainScope().launch {
                    _uiEvent.emit(LaunchUiEvent.ErrShowDialog(true, ""))
                }
            }
        }
    }

    /**
     * 初始化分服配置
     */
    private fun initGlobalConfig() {
        Apm.recordPeriod("LaunchCenter#initGlobalConfig", true)
        val manager = GlobalConfigManager.getInstance()
        manager.init(strategy)
        if (!manager.isReady) {
            manager.loadLocalConfig()
        }
        if (manager.isReady) {
            Apm.recordPeriod("LaunchCenter#initGlobalConfig:local", false)
            manager.request(manager.region)
            onEvent(LaunchEvent.GlobalConfigReady(firstGlobalReady.getAndSet(true)))
        } else {
            manager.addObserver(configObserver)
            manager.request("")
        }
        TrackUtil.request(
            com.huiwan.configservice.http.UrlConfig.GLOBAL_PATH,
            LibBaseUtil.isFirstIn()
        )
        SecB.logInfo()
    }

    /**
     * 请求失败时，用户点击弹窗后重试
     */
    private fun reloadGlobalConfig() {
        val manager = GlobalConfigManager.getInstance()
        manager.init(strategy)
        manager.addObserver(configObserver)
        manager.request("")
    }

    /**
     * 全局配置加载OK
     */
    private fun onGlobalConfigReady(firstTime: Boolean) {
        Apm.recordPeriod("LaunchCenter#onGlobalConfigReady", true)
        CoroutineScope(Dispatchers.IO).launch {
            Apm.recordTimePoint("LaunchCenter#onGlobalConfigReadyDispatch")
            val login = LoginHelper.isLogin()
            if (login) {
                LogUtil.userInit(
                    LoginHelper.getLoginUid(),
                    DeviceIdHelper.getDid()
                )
                UserService.get().refreshSelfInfo(null)
            } else if (firstTime) {
                jumpLoginPage()
            }
            ShenceUtil.registerSuperProperties(false)
            ShenceEvent.install()
            checkRegionConfig(login)
            SecB.checkSig()
        }
        SwitchApi.getUserSwitch(object : LifeDataCallback<UserSwitchInfo>(GlobalLife) {
            override fun onSuccess(result: com.three.http.callback.Result<UserSwitchInfo>?) {
                result?.data?.preferenceLang?.let {
                    PrefUtil.getInstance().setString(PrefUtil.PREFER_LANG, it.lang)
                }
                ShenceUtil.registerSuperProperties(false)
            }

            override fun onFail(code: Int, msg: String?) = Unit
        })
        Apm.recordPeriod("LaunchCenter#onGlobalConfigReady", false)
    }

    /**
     * 开始加载本服配置
     */
    private fun checkRegionConfig(login: Boolean) {
        CoroutineScope(Dispatchers.IO).launch {
            launch(Dispatchers.IO) {
                ConfigHelper.getInstance().updater.loadOptionLocalAndReqNone(this)
            }
            if (!ConfigHelper.getInstance().isLocalAvailable) {
                Apm.recordPeriod("LaunchCenter#ConfigHelperLoadRequiredLocal", true)
                ConfigHelper.getInstance().updater.loadRequiredLocalAndReqNone()
                Apm.recordPeriod("LaunchCenter#ConfigHelperLoadRequiredLocal", false)
            }
            if (ConfigHelper.getInstance().isLocalAvailable) {
                localReady.set(true)
                Apm.recordTimePoint("LaunchCenter#ConfigHelperRequiredLocalLoaded")
                onEvent(LaunchEvent.RegionConfigReady(login))
            } else {
                ConfigHelper.getInstance().updater.markNoLocalOnFirstLaunch()
                Apm.recordPeriod("LaunchCenter#ConfigHelperLoadRequireFromNet", true)
                ConfigHelper.getInstance().updateRequiredConfig(object : UpdateCallback {
                    override fun onSuccess() {
                        localReady.set(true)
                        Apm.recordPeriod(
                            "LaunchCenter#ConfigHelperLoadRequireFromNet:success",
                            false
                        )
                        onEvent(LaunchEvent.RegionConfigReady(login))
                    }

                    override fun onFailed(msg: String?) {
                        MainScope().launch {
                            _uiEvent.emit(LaunchUiEvent.ErrShowDialog(false, msg ?: ""))
                        }
                        val isConnect = NetWorkUtil.isNetworkConnected()
                        Apm.recordPeriod(
                            "LaunchCenter#ConfigHelperLoadRequireFromNet:fail:${isConnect}_$msg",
                            false
                        )
                    }
                })
            }
        }
    }

    /**
     * 一些可以稍微延迟处理的任务
     */
    private fun loginDelayTasks() {
        CoroutineScope(Dispatchers.IO).launch {
            AbTestNormalManager.getInstance().requestAbTestData()
            UserService.get().refreshSelfInfo(null)
            val hasTopActivity = (ActivityTaskManager.getInstance().topActivity != null)
            InitUtil.initWithLogin(hasTopActivity)
        }
    }

    /**
     * 进入登录页
     */
    private fun jumpLoginPage() {
        Apm.recordTimePoint("LaunchCenter#jumpLoginPage")
        checkDeepLink()
        CoroutineScope(Dispatchers.Main.immediate).launch {
            _uiEvent.emit(LaunchUiEvent.JumpToLogin)
        }
    }


    /**
     * 登录态后跳转。
     */
    private fun jumpAfterLogin() {
        Apm.recordTimePoint("LaunchCenter#jumpAfterLogin")
        HLog.d("LaunchCenter", HLog.USR, "jumpAfterLogin enter")
        checkDeepLink()
        CoroutineScope(Dispatchers.IO).launch {
            if (TextUtils.isEmpty(LoginHelper.getNickname()) || BirthdayHelper.needJumpUserCompleteOrBirthPage()) {
                _uiEvent.emit(LaunchUiEvent.JumpToCompleteUserInfo)
            } else {
                val hasAdvert =
                    PrefUserUtil.getInstance().getBoolean(PrefUserUtil.HAS_ADVERTISE, false)
                val deepLink = DeepLinkUtil.isHasDeepLink()
                val isTeenLimit = TeenModeUtil.getMode().isLimit
                // 如果是 deeplink 跳转的。 那么不跳转到我们自己的开屏。
                if (hasAdvert && !deepLink && !isTeenLimit) {
                    val adListNeedShow = suspendCancellableCoroutine {
                        AdvertiseManager.instance.initLocalData(object : AdvertiseManager.Callback {
                            override fun onInitOver(adListNeedShow: List<AdvertisementEntity>?) {
                                it.resumeWith(Result.success(adListNeedShow))
                            }
                        })
                    }
                    HLog.d(
                        "LaunchCenter", HLog.USR,
                        "adListNeedShow:${adListNeedShow.isNullOrEmpty()}"
                    )
                    if (!adListNeedShow.isNullOrEmpty()) {
                        _uiEvent.emit(LaunchUiEvent.JumpToAd)
                    } else {
                        _uiEvent.emit(LaunchUiEvent.JumpToMain)
                    }
                } else {
                    _uiEvent.emit(LaunchUiEvent.JumpToMain)
                }
            }
        }
    }

    private fun checkDeepLink() {
        // 先检查 referrer， 后确认新的
        // 新的覆盖旧的，操作比较极端，正常推进。
        val obj = WPLinkUtil.getLinkDataJsonString()
        WPLinkUtil.clear()
        if (obj != null) {
            DeepLinkUtil.parseFirebaseLink(JsonUtil.JsonObjectToHashMap(obj))
        }
    }

}