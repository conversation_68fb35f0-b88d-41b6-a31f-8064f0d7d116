package com.wepie.wespy.module.voiceroom.util;

import android.app.ForegroundServiceStartNotAllowedException;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;

import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.huiwan.constants.IntentConfig;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.NotificationHelper;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.BaseVoiceRoomActivity;

public class VoiceRoomNotifyService extends Service {
    private static final String TAG = "VoiceRoomNotifyService";
    private static int roomId = 0;
    private static final String STOP_SERVICE = "ACTION_STOP_SERVICE";
    private final Handler handler = new Handler(Looper.getMainLooper());

    private final Runnable restartForeground = () -> {
        roomId = VoiceRoomService.getInstance().getRid();
        boolean inVoiceRoom = VoiceRoomService.getInstance().isInVoiceRoom();
        if (roomId != 0 && inVoiceRoom) {
            HLog.d(TAG, "restart foreground room id {}", roomId);
            doStartForeground();
            delayStartTask();
        } else {
            HLog.d(TAG, "restart foreground room id 0 cancelled");
        }
    };

    public VoiceRoomNotifyService() {
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        HLog.d(TAG, "RoomService onCreate");
        roomId = VoiceRoomService.getInstance().getRid();
        FLog.d(TAG + " start foreground service onCreate " + roomId);
        doStartForeground();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        String action = intent.getAction();
        if (STOP_SERVICE.equals(action)) {
            HLog.d(TAG, HLog.USR, "onStartCommand stopSelf");
            roomId = 0;
            stopSelf();
        } else {
            roomId = intent.getIntExtra(IntentConfig.INT_ROOM_ID, 0);
            HLog.d(TAG, "RoomService onStartCommand rid = {}", roomId);
            FLog.d(TAG + " start foreground service onStartCommand " + roomId);
            doStartForeground();
            delayStartTask();
        }
        return START_NOT_STICKY;
    }

    private void delayStartTask() {
        handler.removeCallbacksAndMessages(null);
        handler.postDelayed(restartForeground, 15_000);
    }

    private void doStartForeground() {
        Intent startActivityIntent = new Intent(this, BaseVoiceRoomActivity.class);
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(roomId);
        String notifyContent;

        String roomIdStr = IDRegionUtil.INSTANCE.getFinalIDStrByType(roomId, roomInfo.game_type);
        if (roomInfo.isWeddingRoom()) {
            notifyContent = ResUtil.getStr(R.string.in_wedding_room_id, roomIdStr);
        } else if (roomInfo.isLoveHome()) {
            notifyContent = ResUtil.getStr(R.string.in_lover_room_id, roomIdStr);
        } else {
            notifyContent = ResUtil.getStr(R.string.in_voice_room_id, roomIdStr);
        }

        startActivityIntent.putExtra(IntentConfig.INT_ROOM_ID, roomId);
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0x98,
                startActivityIntent, flags);
        // service.startForeground
        try {
            NotificationHelper.showForegroundNotification(this, NotificationHelper.NOTIFICATION_ID_VOICE_ROOM, pendingIntent,
                    notifyContent, ResUtil.getStr(R.string.tap_return));
            HLog.d(TAG, "RoomService doStartForeground rid = {}", roomId);
        } catch (ForegroundServiceStartNotAllowedException e) {
            // 调用stopRoomService可能会出现这个异常
            HLog.d(TAG, HLog.USR, "RoomService doStartForeground err = {}", e);
        }
        FLog.d(TAG + " do start foreground service: " + roomId);
    }

    public static void startRoomService(Context context, int rid) {
        HLog.d(TAG, HLog.USR, "startRoomService rid={}, roomId={}, getRid()={}", rid, roomId, VoiceRoomService.getInstance().getRid());
        if (rid == roomId) {
            HLog.d(TAG, "RoomService startRoomService room id same return == {}", roomId);
            return;
        }
        if (VoiceRoomService.getInstance().getRid() <= 0) {
            HLog.d(TAG, "RoomService startRoomService service room id invalid: {}", VoiceRoomService.getInstance().getRid());
            return;
        }
        try {
            Intent intent = new Intent(context, VoiceRoomNotifyService.class);
            intent.putExtra(IntentConfig.INT_ROOM_ID, rid);
            if (Build.VERSION_CODES.O <= Build.VERSION.SDK_INT) {
                FLog.d(TAG + " try start foreground service " + rid);
                context.startForegroundService(intent);
            } else {
                context.startService(intent);
            }
        } catch (Exception e) {
            HLog.e(TAG, "startRoomService, {}", e);
        }
        HLog.d(TAG, "RoomService startRoomService rid=" + rid);
    }

    public static void stopRoomService(Context context) {
        HLog.d(TAG, HLog.USR, "stopRoomService");
        try {
            Intent intent = new Intent(context, VoiceRoomNotifyService.class);
            intent.setAction(STOP_SERVICE);
            context.startService(intent);
        } catch (Exception e) {
            HLog.e(TAG, "stopRoomService, {}", e);
        }
    }

    @Override
    public void onDestroy() {
        HLog.d(TAG, "onDestroy");
        roomId = 0;
        handler.removeCallbacksAndMessages(null);
        NotificationHelper.stopForegroundNotification(this);
        super.onDestroy();
    }
}
