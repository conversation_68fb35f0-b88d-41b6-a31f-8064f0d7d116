package com.wepie.wespy.module.voiceroom.util

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.wejoy.weplay.ex.cancellable.registerAutoCancel
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wepie.liblog.main.HLog
import com.wepie.wespy.consts.roomutil.RoomUtilManager
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.model.entity.voiceroom.VoiceUtilItem
import com.wepie.wespy.model.event.RoomInfoUpdateEvent
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class VoiceRoomUtilsViewModel : ViewModel() {

    private var mRoomInfo: VoiceRoomInfo

    private val _gameEntUtilsItemsLiveData = MutableLiveData<List<VoiceUtilItem>>()
    val gameEntUtilsItemsLiveData: LiveData<out List<VoiceUtilItem>> =
        _gameEntUtilsItemsLiveData

    private val _roomUtilItemsLiveData = MutableLiveData<List<VoiceUtilItem>>()
    val roomUtilItemsLiveData: LiveData<out List<VoiceUtilItem>> =
        _roomUtilItemsLiveData

    init {
        EventBus.getDefault().registerAutoCancel(this.toLife(), this)
        mRoomInfo = VoiceRoomService.getInstance().roomInfo
        init(mRoomInfo)
    }

    private fun init(info: VoiceRoomInfo) {
        val manager = RoomUtilManager.get()
        _gameEntUtilsItemsLiveData.value =
            manager.getItemList(info.game_type, VoiceUtilItem.GAME_ENTERTAINMENT_UTIL_TYPE)

        val roomUtilItems = manager.getItemList(info.game_type, VoiceUtilItem.ROOM_UTIL_TYPE)
        if (!info.isAdvancedOrAnchorOrFamilyRoom) {
            val each = roomUtilItems.iterator()
            while (each.hasNext()) {
                if (each.next().id == VoiceUtilItem.ITEM_MIC) {
                    each.remove()
                }
            }
        }
        _roomUtilItemsLiveData.value = roomUtilItems
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFamilyInfoUpdate(event: RoomInfoUpdateEvent) {
        if (!RoomInfoUpdateEvent.hasScene(event.features, RoomInfoUpdateEvent.F_FAMILY_INFO)) {
            return
        }
        init(VoiceRoomService.getInstance().roomInfo)
    }

    fun update(roomInfo: VoiceRoomInfo) {
        mRoomInfo = roomInfo
        val isAdmin = roomInfo.isSelfAdmin
        val isOwner = roomInfo.isSelfOwner
        val pastorState = roomInfo.isWeddingRoom && roomInfo.weddingInfo.isPastorState

        var curAbility: Int
        if (pastorState) {
            curAbility = ENABLE_A_G
        } else {
            curAbility =
                ENABLE_RP or ENABLE_A_G or ENABLE_H5 or ENABLE_LUCKY_NUM or ENABLE_JUMP_GAME
            if (isOwner) {
                curAbility = curAbility or ENABLE_PUZZLE
            }
            if (isOwner or isAdmin) {
                curAbility =
                    curAbility or (ENABLE_PK or ENABLE_SLOT or ENABLE_DRAW or ENABLE_MUSIC or ENABLE_AUDIO
                            or ENABLE_MIC or ENABLE_WINNER or ENABLE_SCORE or ENABLE_PK_NEW or ENABLE_BINGO)
            }
        }
        if (roomInfo.isFamilyRoom && isOwner) {
            curAbility = curAbility or ENABLE_HOT
        }
        val gameEntUtilsItems = _gameEntUtilsItemsLiveData.value!!
        val roomUtilItems = _roomUtilItemsLiveData.value!!
        updateUtilItems(curAbility, roomInfo, gameEntUtilsItems)
        updateUtilItems(curAbility, roomInfo, roomUtilItems)
        _gameEntUtilsItemsLiveData.value = gameEntUtilsItems
        _roomUtilItemsLiveData.value = roomUtilItems
    }

    private fun updateUtilItems(
        curAbility: Int, roomInfo: VoiceRoomInfo, utilItems: List<VoiceUtilItem>
    ) {
        for (item in utilItems) {
            item.isEnable = 1 shl item.id and curAbility != 0
            when (item.id) {
                VoiceUtilItem.ITEM_MUSIC -> {
                    item.setUseExtraName(roomInfo.inMusic)
                }

                VoiceUtilItem.ITEM_MIC -> {
                    item.setUseExtraName(roomInfo.inSeatQueueOpen)
                }

                VoiceUtilItem.ITEM_PUZZLE -> {
                    item.setUseExtraName(roomInfo.isPuzzleOpen)
                }
            }
        }
    }

    companion object {
        private const val ENABLE_DRAW = 1 shl VoiceUtilItem.ITEM_DRAW
        private const val ENABLE_SLOT = 1 shl VoiceUtilItem.ITEM_SLOT
        private const val ENABLE_RP = 1 shl VoiceUtilItem.ITEM_RP
        private const val ENABLE_PK = 1 shl VoiceUtilItem.ITEM_PK
        private const val ENABLE_A_G = 1 shl VoiceUtilItem.ITEM_A_G
        private const val ENABLE_MUSIC = 1 shl VoiceUtilItem.ITEM_MUSIC
        private const val ENABLE_AUDIO = 1 shl VoiceUtilItem.ITEM_AUDIO
        private const val ENABLE_MIC = 1 shl VoiceUtilItem.ITEM_MIC
        private const val ENABLE_WINNER = 1 shl VoiceUtilItem.ITEM_B_WINNER
        private const val ENABLE_SCORE = 1 shl VoiceUtilItem.ITEM_SCORE
        private const val ENABLE_PUZZLE = 1 shl VoiceUtilItem.ITEM_PUZZLE
        private const val ENABLE_H5 = 1 shl VoiceUtilItem.ITEM_H5
        private const val ENABLE_HOT = 1 shl VoiceUtilItem.ITEM_HOT
        private const val ENABLE_LUCKY_NUM = 1 shl VoiceUtilItem.ITEM_LUCKY_NUM
        private const val ENABLE_PK_NEW = 1 shl VoiceUtilItem.ITEM_PK_NEW
        private const val ENABLE_JUMP_GAME = 1 shl VoiceUtilItem.ITEM_JUMP_GAME
        private const val ENABLE_BINGO = 1 shl VoiceUtilItem.ITEM_BINGO
    }
}