package com.wepie.wespy.module.voiceroom.util

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.component.activity.BaseFragment
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.voiceroom.VoiceUtilItem
import com.wepie.wespy.module.voiceroom.main.bottom.Adapter
import com.wepie.wespy.module.voiceroom.main.bottom.BottomViewModel
import com.wepie.wespy.module.voiceroom.main.bottom.RoomUtilAdapter

class VoiceRoomUtilsFragment : BaseFragment() {

    private lateinit var model: VoiceRoomUtilsViewModel
    private lateinit var bottomViewModel: BottomViewModel

    private lateinit var gameEntertainmentTv: TextView
    private val gameEntertainmentAdapter = Adapter()
    private lateinit var roomUtilTitleTv: TextView
    private val roomUtilAdapter = RoomUtilAdapter()

    private val helper =
        RoomUtilHelper(this)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.room_bottom_util_view, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        model = ViewModelProvider(this).get(VoiceRoomUtilsViewModel::class.java)
        bottomViewModel = ViewModelProvider(requireActivity()).get(BottomViewModel::class.java)
        initGameEntRv(view)
        initRoomUtilRv(view)

        initData()
    }

    private fun initGameEntRv(view: View) {
        val gameEntertainmentUtilRv: RecyclerView =
            view.findViewById(R.id.game_entertainment_util_rv)
        gameEntertainmentUtilRv.layoutManager = GridLayoutManager(context, 5)
        gameEntertainmentUtilRv.adapter = gameEntertainmentAdapter
        gameEntertainmentAdapter.onItemClickListener = helper
    }

    private fun initRoomUtilRv(view: View) {
        val roomUtilRv: RecyclerView = view.findViewById(R.id.room_util_rv)
        roomUtilRv.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        roomUtilRv.adapter = roomUtilAdapter
        roomUtilAdapter.onItemClickListener = helper

        gameEntertainmentTv = view.findViewById(R.id.game_entertainment_title_tx)
        roomUtilTitleTv = view.findViewById(R.id.room_util_title_tx)
    }

    private fun initData() {
        model.gameEntUtilsItemsLiveData.observe(viewLifecycleOwner) {
            if (it.isEmpty()) {
                gameEntertainmentTv.isVisible = false
                roomUtilAdapter.refresh(emptyList())
            } else {
                gameEntertainmentTv.isVisible = true
                gameEntertainmentAdapter.refresh(it)
            }
        }
        model.roomUtilItemsLiveData.observe(viewLifecycleOwner) {
            var isEnable = false
            it.forEach { item ->
                if (item.isEnable) {
                    isEnable = true
                }
            }
            if (isEnable) {
                roomUtilTitleTv.visibility = View.VISIBLE
                roomUtilAdapter.refresh(it)
            } else {
                roomUtilTitleTv.visibility = View.GONE
                roomUtilAdapter.refresh(emptyList())
            }
        }
        bottomViewModel.voiceRoomInfoLiveData.observe(viewLifecycleOwner) {
            model.update(it)
            helper.setRoomInfo(it)
        }
    }

    fun notifyUtilAdapterRefresh() {
        var pkIndex = 0;
        for ((index, item) in roomUtilAdapter.dataList.withIndex()) {
            if (item.id == VoiceUtilItem.ITEM_PK_NEW) {
                pkIndex = index
                break
            }
        }
        roomUtilAdapter.notifyItemChanged(pkIndex)
    }

    fun hideBottomLay() {
        bottomViewModel.setFragmentState(1)
    }

    fun changeAudioEffectStatus() {
        bottomViewModel.changeEffectState()
    }
}