package com.wepie.wespy.module.voiceroom.bingo

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import android.widget.FrameLayout
import com.huiwan.base.ktx.inflates
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBasePlugin
import com.wepie.wespy.module.voiceroom.util.IRoomToolBar

/**
 * <AUTHOR>
 * @since 2023/7/13 18:03
 */
class BingoDefaultView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr), IBasePlugin, IBingo {


    init {
        inflates(this, R.layout.bingo_content_root, true) as ViewGroup
    }


    override fun checkBingoMutual(bingoOpen: Boolean): Boolean {
        return false
    }

    override fun openBingo() {
    }


    override fun injectRoomToolBar(bar: IRoomToolBar) {

    }

    override fun releaseBingo() {

    }

    override fun isBingoPlayer(info: VoiceRoomInfo, uid: Int): Boolean {
        return false
    }


}