package com.wepie.wespy.module.voiceroom.bingo

import android.view.View
import android.view.ViewGroup
import com.huiwan.base.ktx.gone
import com.huiwan.base.ktx.visible
import com.huiwan.base.util.ToastUtil
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.bingo.IBingoApi
import com.huiwan.lib.api.plugins.bingo.IBingoCore
import com.huiwan.lib.api.plugins.bingo.IBingoDepends
import com.huiwan.user.LoginHelper
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IMainPlugin
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IMainPlugin.OnVoiceRoomInfoChangeListener
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISmallFrameUtil
import com.wepie.wespy.module.voiceroom.util.IRoomToolBar

/**
 *
 * Bingo 语音房代理类
 */
class BingoDelegateImpl(val pluginTool: IPluginTool<IBingo>) : IBingo {

    private val tag = "BingoVoiceImpl"
    private var bingoCore: IBingoCore? = null
    var bar: IRoomToolBar? = null

    init {
        if (notSupportBingo()) {
            log("App not init Bingo")
        } else {
            log("init Bingo")
            initBingo()
            initOnUpdateRoomInfo(mainPlugin)
            autoShowBingo()
        }
    }


    private fun initBingo() {
        val plugin = mainPlugin

        // 依赖的数据
        val metadata = IBingoApi.BingoData(
            pluginTool.context(),
            pluginTool,
            mainPlugin.roomInfo.rid,
        )

        // 依赖的功能
        val bingoDepends = object : IBingoDepends {

            override fun showSmallView(view: View) {
                VoicePluginService.getPlugin(ISmallFrameUtil::class.java).add(view)
            }

            override fun closeSmallView(view: View) {
                VoicePluginService.getPlugin(ISmallFrameUtil::class.java).remove(view)
            }


            override fun addView(view: View, index: Int) {
                var rootView = pluginTool.getRealView<BingoDefaultView>()
                if (rootView == null) {
                    pluginTool.loadView(true)
                    rootView = pluginTool.getRealView()
                }
                if (rootView is ViewGroup && view.parent == null) {
                    val parent = rootView.findViewById<ViewGroup>(R.id.bingo_normal_touch)
                    parent.addView(view, index)
                }
            }

            override fun hideView() {
                val rootView = pluginTool.getRealView<BingoDefaultView>()
                rootView?.gone()
            }

            override fun showView() {
                val rootView = pluginTool.getRealView<BingoDefaultView>()
                rootView?.visible()
            }

            override fun removeView(view: View) {
                val rootView = pluginTool.getRealView<BingoDefaultView>()
                if (rootView is ViewGroup && view.parent != null) {
                    val parent = rootView.findViewById<ViewGroup>(R.id.bingo_normal_touch)
                    parent.removeView(view)
                }
            }

            override fun closeBottomToolbar() {
                bar?.closeBar()
            }

            override fun bingoIsOpen(): Boolean {
                return mainPlugin.roomInfo.isBingoOpen
            }

            override fun isRoomOwner(): Boolean {
                return mainPlugin.roomInfo.isSelfAdminOrOwner
            }

            override fun voiceIsClose(): Boolean {
                return VoiceRoomService.getInstance().isMuteRoom(mainPlugin.roomId)
            }
        }

        // 初始化
        bingoCore = ApiService.of(IBingoApi::class.java).createBingoCore(metadata, bingoDepends)

        // 房间状态变化
        val listener = OnVoiceRoomInfoChangeListener { voiceRoomInfo, features ->
            if (bingoIsOpen()) {
                bingoCore?.roomInfoUpdate(voiceRoomInfo.owner)
            }
        }
        plugin.addOnVoiceRoomInfoChangeListener(pluginTool, listener)

        pluginTool.onDestroy {
            log("onDestroy")
            releaseBingo()
        }

    }


    private fun autoShowBingo() {
        // bingo 进入，并且是房主，则主动跳出 选择弹窗
        if (mainPlugin.roomInfo.isBingoEnter && mainPlugin.roomInfo.owner == LoginHelper.getLoginUid()) {
            val bingoOpen: Boolean = bingoIsOpen()
            val memeOpen = false
            val drawOpen: Boolean = mainPlugin.roomInfo.isDrawing
            val winOpen: Boolean = mainPlugin.roomInfo.isBigWinnerOpen

            log("Bingo enter and is home owner, winOpen=$winOpen  memeOpen=$memeOpen drawOpen=$drawOpen bingoOpen=$bingoOpen")
            bingoCore?.openBingo(memeOpen, drawOpen, winOpen, bingoOpen)
        }
    }


    private fun initOnUpdateRoomInfo(plugin: IMainPlugin) {
        val listener = OnVoiceRoomInfoChangeListener { voiceRoomInfo, features ->
            val canLoad = voiceRoomInfo.isBingoOpen
            pluginTool.loadView(canLoad)
        }
        plugin.addOnVoiceRoomInfoChangeListener(pluginTool, listener)
        pluginTool.onDestroy {
            bar = null
        }
    }


    override fun checkBingoMutual(bingoOpen: Boolean): Boolean {
        return bingoCore?.checkBingoMutual(bingoOpen) ?: false
    }


    override fun openBingo() {
        if (notSupportBingo()) {
            ToastUtil.debugShow("not support Bingo")
            return
        }
        log("openBingo")
        pluginTool.loadView(true)
        val bingoOpen: Boolean = bingoIsOpen()
        val memeOpen = false
        val drawOpen: Boolean = mainPlugin.roomInfo.isDrawing
        val winOpen: Boolean = mainPlugin.roomInfo.isBigWinnerOpen

        log("Bingo enter and is home owner, winOpen=$winOpen  drawOpen=$drawOpen bingoOpen=$bingoOpen")
        bingoCore?.openBingo(memeOpen, drawOpen, winOpen, bingoOpen)
    }

    override fun injectRoomToolBar(bar: IRoomToolBar) {
        this.bar = bar
    }

    override fun releaseBingo() {
        log("releaseBingo")
        bingoCore?.releaseBingo()
    }

    override fun isBingoPlayer(info: VoiceRoomInfo, uid: Int): Boolean {
        val isPlayer = bingoCore?.isPlayer(uid) ?: false
        // 在座位上 并且 在Bingo 游戏中，则不允许换座位
        if (info.isUserSeated(uid) && info.isBingoOpen && isPlayer) {
            return true
        }
        return false
    }


    private fun bingoIsOpen(): Boolean {
        return mainPlugin.roomInfo.isBingoOpen
    }


    /**
     * bingo支持俄，美，葡，西
     */
    private fun notSupportBingo(): Boolean {
        return false
    }

    private fun log(msg: String) {
        HLog.e(tag, msg)
    }

}