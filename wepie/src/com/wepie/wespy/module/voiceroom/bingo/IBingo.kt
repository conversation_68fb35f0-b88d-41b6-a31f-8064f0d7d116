package com.wepie.wespy.module.voiceroom.bingo

import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBasePlugin
import com.wepie.wespy.module.voiceroom.util.IRoomToolBar

/**
 * <AUTHOR>
 * @since 2023/7/13 19:12
 */
interface IBingo : IBasePlugin {

    /**
     * 检查是否和 meme 互斥， true 表示互斥，当前 meme 开启，否则表示没有开启
     * @param memeOpen meme 是否开启
     */
    fun checkBingoMutual(bingoOpen: Boolean): Boolean

    fun openBingo()

    fun injectRoomToolBar(bar: IRoomToolBar)

    fun releaseBingo()

    fun isBingoPlayer(info: VoiceRoomInfo, uid: Int): Boolean
}