package com.wepie.wespy.module.voiceroom.guide

import android.content.Context
import android.graphics.drawable.Animatable
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.huiwan.base.ktx.dp
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ViewUtil
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoadListener
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.databinding.RoomGuideClickMsgInputViewBinding
import com.wepie.wespy.databinding.RoomGuideSitDownViewBinding
import com.wepie.wespy.databinding.RoomGuideSwitchModeViewBinding
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginUtil
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomBottomPlugin
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISeatView
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ITitlePlugin

class RoomGuidView : FrameLayout, IRoomGuidePlugin {

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    init {
        background = ColorDrawable(ResUtil.getColor(R.color.black_alpha70))
    }

    override fun onHide() {
    }

    override fun showSwitchRoomModeGuide(
        trackExt: Map<String, Any>,
        hide: (showSetting: Boolean) -> Unit
    ) {
        setOnClickListener { hide.invoke(false) }
        removeAllViews()
        val binding = RoomGuideSwitchModeViewBinding.inflate(
            LayoutInflater.from(context), this, true
        )
        binding.roomGuideMenuIv.post {
            val location = VoicePluginService.getPlugin(ITitlePlugin::class.java).menuCenterLocation
            if (layoutDirection == View.LAYOUT_DIRECTION_RTL) {
                ViewUtil.setMargins(
                    binding.roomGuideMenuIv,
                    0, location[1] - 19.dp, location[0] - 19.dp, 0
                )
            } else {
                ViewUtil.setMargins(
                    binding.roomGuideMenuIv,
                    0, location[1] - 19.dp, ScreenUtil.getScreenWidth() - location[0] - 19.dp, 0
                )
            }
        }
        binding.roomGuideMenuIv.setOnClickListener {
            hide.invoke(true)
        }
    }

    override fun showChatWithOtherGuide(hide: () -> Unit) {
        setOnClickListener { showSitDownGuide(hide) }
        removeAllViews()
        val binding = RoomGuideClickMsgInputViewBinding.inflate(
            LayoutInflater.from(context), this, true
        )
        binding.roomGuideSendMsgTv.post {
            val rect = VoicePluginService.getPlugin(IRoomBottomPlugin::class.java).msgInputRect
            val tv = binding.roomGuideSendMsgTv
            if (layoutDirection == View.LAYOUT_DIRECTION_RTL) {
                ViewUtil.setMargins(
                    tv, ScreenUtil.getScreenWidth() - rect.right, rect.top, 0, 0
                )
            } else {
                ViewUtil.setMargins(tv, rect.left, rect.top, 0, 0)
            }
            ViewUtil.setViewSize(tv, rect.width(), rect.height())
        }
    }

    private fun showSitDownGuide(hide: () -> Unit) {
        setOnClickListener { hide.invoke() }
        removeAllViews()
        this.post {
            val binding = RoomGuideSitDownViewBinding.inflate(
                LayoutInflater.from(context), this, true
            )
            val objectList = VoicePluginService.invokePluginsWithReturn(
                ISeatView::class.java, PluginUtil.getSeatPosition()
            )
            val seatInfos = VoiceRoomService.getInstance().roomInfo.seatInfos
            //优先找亲密位
            var emptySeatNum = seatInfos.firstOrNull {
                it.isIntimateSeat && it.isEmpty && !it.isSealed
            }?.seat_num ?: -1
            if (emptySeatNum < 0) {
                emptySeatNum = seatInfos.firstOrNull { it.isEmpty && !it.isSealed }?.seat_num ?: 2
            }
            val obj = objectList.firstOrNull { it.seatNum == emptySeatNum } ?: return@post

            val iv = binding.roomGuideMicIv
            ViewUtil.setViewSize(iv, obj.width.toInt(), obj.height.toInt())

            if (layoutDirection == View.LAYOUT_DIRECTION_RTL) {
                val startMargin = ScreenUtil.getScreenWidth() - obj.x - obj.width
                ViewUtil.setMargins(iv, startMargin.toInt(), obj.y.toInt(), startMargin.toInt(), 0)
            } else {
                ViewUtil.setMargins(iv, obj.x.toInt(), obj.y.toInt(), obj.x.toInt(), 0)
            }
            WpImageLoader.load(
                ResUtil.getString(R.string.guide_mic_anim_webp_url), null,
                ImageLoadInfo.newInfo().owner(this),
                object : WpImageLoadListener<String> {
                    override fun onComplete(model: String?, t: Drawable?): Boolean {
                        iv.setImageDrawable(t)
                        if (t is Animatable) {
                            t.start()
                        }
                        return true
                    }

                    override fun onFailed(model: String?, e: Exception?): Boolean {
                        return false
                    }
                })
        }
    }
}