package com.wepie.wespy.module.voiceroom.guide

import android.view.View
import com.huiwan.base.str.ResUtil
import com.huiwan.lib.api.impl
import com.huiwan.store.PrefUtil
import com.wepie.lib.api.plugins.track.TrackApi
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService

class RoomGuideDelegateImpl(
    private val pluginTool: IPluginTool<IRoomGuidePlugin>
) : IRoomGuidePlugin by pluginTool.proxy() {

    init {
        WpImageLoader.downloadOnly(
            ResUtil.getString(R.string.guide_mic_anim_webp_url),
            ImageLoadInfo.newInfo(),
            null
        )
    }

    override fun isShown(): Boolean {
        return pluginTool.getRealView<View>()?.isShown ?: false
    }

    override fun onHide() {
        pluginTool.getRealView<View>()?.performClick()
    }

    override fun showSwitchRoomModeGuide(
        trackExt: Map<String, Any>,
        hide: (showSetting: Boolean) -> Unit
    ) {
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        if (!roomInfo.isOnline) {
            return
        }
        val util = PrefUtil.getInstance()
        val count = util.getInt(KEY_SWITCH_MODE_GUIDE_COUNT, 0)
        if (count > 3) {
            return
        }
        val current = System.currentTimeMillis()
        val time = util.getLong(KEY_SWITCH_MODE_GUIDE_TIME, 0)
        if (current - time < 2 * 60 * 1000) {
            return
        }
        util.setInt(KEY_SWITCH_MODE_GUIDE_COUNT, count + 1)
        util.setLong(KEY_SWITCH_MODE_GUIDE_TIME, current)
        TrackApi::class.impl().trackEvent(TrackUtil.trackValue().appViewScreen, trackExt)
        pluginTool.loadView(true)
        pluginTool.proxy().showSwitchRoomModeGuide(trackExt) { showSetting ->
            pluginTool.loadView(false)
            hide.invoke(showSetting)
        }
    }

    override fun showChatWithOtherGuide(hide: () -> Unit) {
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        if (!roomInfo.isOnline) {
            return
        }
        val util = PrefUtil.getInstance()
        val flag = util.getBoolean(KEY_SHOW_JOIN_CHAT_GUIDE, false)
        if (flag) {
            return
        }
        util.setBoolean(KEY_SHOW_JOIN_CHAT_GUIDE, true)
        pluginTool.loadView(true)
        pluginTool.proxy().showChatWithOtherGuide {
            pluginTool.loadView(false)
            hide.invoke()
        }
    }

    companion object {
        private const val KEY_SWITCH_MODE_GUIDE_COUNT = "room_guide_switch_mode_count"
        private const val KEY_SWITCH_MODE_GUIDE_TIME = "room_guide_switch_mode_time"

        private const val KEY_SHOW_JOIN_CHAT_GUIDE = "room_guide_join_chat"

    }
}