package com.wepie.wespy.module.voiceroom.music.lrc;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.huiwan.base.util.log.TimeLogger;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2019-05-15
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class LrcView extends View implements ValueAnimator.AnimatorUpdateListener {
    private List<LrcEntry> entries = new ArrayList<>();
    private ValueAnimator animator;
    private long animStartTime = 0;
    private int drawOffset;
    private String curLrc = "";
    private boolean attached = true;

    public LrcView(@NonNull Context context) {
        this(context, null);
    }

    public LrcView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public void loadLrc(@NonNull String source, int timeInMill, boolean playing) {
        if (!curLrc.equals(source)) {
            curLrc = source;
            entries.clear();
            try {
                entries.addAll(LrcEntry.parse(source));
            } catch (Exception e) {
                TimeLogger.err("parse lrc error: " + e);
            }
            updateEntryLayouts();
        }
        if (playing) {
            startAnim(timeInMill);
        } else {
            updateOffset();
        }
        invalidate();
    }

    public void loadLrc(@NonNull String source) {
        loadLrc(source, 0, true);
    }

    public boolean sameLrc(String lrc) {
        return curLrc.equals(lrc);
    }

    public void seek2Time(int timeInMill) {
        startAnim(timeInMill);
    }

    public void pause() {
        cancelAnim();
    }

    public void resume(long timeInMill) {
        startAnim(timeInMill);
    }

    public void clearLrc() {
        curLrc = "";
        entries.clear();
        cancelAnim();
        invalidate();
    }

    private void cancelAnim() {
        if (animator != null) {
            animator.cancel();
        }
    }

    private void startAnim(long timeInMill) {
        cancelAnim();
        if (attached && !entries.isEmpty()) {
            animator = ValueAnimator.ofInt(0, 3600000);
            animator.setDuration(3600000);
            animator.addUpdateListener(this);
            animator.setInterpolator(new LinearInterpolator());
            animator.start();
            animStartTime = System.currentTimeMillis() - timeInMill;
        }
    }


    @Override
    public void onAnimationUpdate(ValueAnimator animation) {
        updateOffset();
    }

    private void updateOffset() {
        int preHeight = 0;
        long curPos = System.currentTimeMillis() - animStartTime;
        int center = getHeight() / 2;
        int cLineHeight = 0;
        int curIndex = -1;
        for (LrcEntry entry : entries) {
            if (entry.getTime() < curPos) {
                preHeight += entry.getLayoutHeight();
                curIndex++;
                entry.updatePos(-1);
            } else {
                entry.updatePos(1);
            }
        }
        if (curIndex < entries.size() && curIndex >= 0) {
            LrcEntry cur = entries.get(curIndex);
            cur.updatePos(0);
            cLineHeight = cur.getLayoutHeight();
        }
        // pre height 中包含正显示的歌词，需要减掉
        // drawOffset = center - (preHeight - cLineHeight) - cLineHeight / 2;
        drawOffset = center - preHeight + cLineHeight / 2;
        invalidate();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        updateEntryLayouts();
    }

    private void updateEntryLayouts() {
        for (LrcEntry entry : entries) {
            entry.updateWidth(getMeasuredWidth());
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (!entries.isEmpty()) {
            canvas.save();
            canvas.translate(0, drawOffset);
            for (LrcEntry entry : entries) {
                int h = entry.getLayoutHeight();
                entry.draw(canvas);
                canvas.translate(0, h);
            }
            canvas.restore();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        attached = false;
        cancelAnim();
        super.onDetachedFromWindow();
    }
}