package com.wepie.wespy.module.voiceroom.music.lrc;

import android.graphics.Canvas;
import android.graphics.Typeface;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.format.DateUtils;

import com.huiwan.base.util.FontUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * date 2019-05-15
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class LrcEntry {
    private TextPaint paint;
    private String line = "";
    private long time = 0;
    private StaticLayout layout;

    private LrcEntry(long time, String line) {
        this.time = time;
        this.line = line;
        paint = new TextPaint();
        paint.setTextSize(ScreenUtil.dip2px(16));
        paint.setAntiAlias(true);
        paint.setTypeface(FontUtil.getTypeface(Typeface.BOLD));
        paint.setColor(0xff24c572);
    }

    static List<LrcEntry> parse(String source) {
        String[] lrcLines = source.split("\n");
        List<LrcEntry> lrcEntryList = new ArrayList<>(lrcLines.length);
        long lastTime = 0;
        long offset = 0;
        for (String line : lrcLines) {
            int lastEnd = line.length();
            int lastStart = line.length();
            String l = "";
            boolean lNotFind = true;
            while (lastEnd > 0 && lastStart > 0) {
                int end = line.lastIndexOf(']', lastEnd-1);
                int start = line.lastIndexOf('[', lastStart-1);
                lastEnd = end;
                lastStart = start;
                if (start > -1 && end > start && line.length() > end - 1) {
                    String[] timeStrArr = line.substring(start + 1, end).split(":");
                    if (timeStrArr.length < 2) {
                        break;
                    }
                    if (lNotFind) {
                        lNotFind = false;
                        l = line.substring(end + 1);
                    }
                    try {
                        LrcEntry entry = null;
                        switch (timeStrArr[0]) {
                            case "ti":
                                lastTime += 100;
                                entry = new LrcEntry(lastTime, R.string.song + timeStrArr[1]);
                                break;
                            case "ar":
                                lastTime += 100;
                                entry = new LrcEntry(lastTime, R.string.artist + timeStrArr[1]);
                                break;
                            case "al":
                                lastTime += 100;
                                entry = new LrcEntry(lastTime, R.string.album + timeStrArr[1]);
                                break;
                            case "offset":
                                offset = Long.parseLong(timeStrArr[1]);
                                break;
                            case "au":
                            case "by":
                            case "re":
                            case "ve":
                                break;
                            default:
                                int m = Integer.parseInt(timeStrArr[0]);
                                float s = Float.parseFloat(timeStrArr[1]);
                                lastTime = m * DateUtils.MINUTE_IN_MILLIS + (int) (s * DateUtils.SECOND_IN_MILLIS);
                                entry = new LrcEntry(lastTime, l);

                        }
                        if (entry != null) {
                            lrcEntryList.add(entry);
                        }
                        } catch (Exception e) {
                        TimeLogger.err( "parse lrc error " + e.getMessage());
                    }
                }
            }
        }

        Collections.sort(lrcEntryList, new Comparator<LrcEntry>() {
            @Override
            public int compare(LrcEntry o1, LrcEntry o2) {
                return (int)(o1.getTime() - o2.getTime());
            }
        });

        for (LrcEntry entry: lrcEntryList) {
            entry.time -= offset;
        }

        return lrcEntryList;
    }

    long getTime() {
        return time;
    }

    /**
     * @param pos lt for pre, 0 for cur, gt for after
     */
    void updatePos(int pos) {
        if (pos == 0) {
            paint.setColor(0xff24c572);
        } else {
            paint.setColor(0x7fffffff);
        }
    }

    void updateWidth(int width) {
        if (layout == null || layout.getWidth() != width) {
            layout = new StaticLayout(line, paint, width, Layout.Alignment.ALIGN_CENTER, 1, ScreenUtil.dip2px(2), true);
        }
    }

    int getLayoutHeight() {
        return layout == null ? 0 : layout.getHeight();
    }

    void draw(Canvas canvas) {
        if (layout != null) {
            layout.draw(canvas);
        }
    }
}