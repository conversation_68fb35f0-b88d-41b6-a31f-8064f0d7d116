package com.wepie.wespy.module.contact.detail.item

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.configservice.constentity.ConstV3Info
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.user.entity.User
import com.huiwan.widget.inflate
import com.wejoy.jackaroo.record.CollectionAlbum
import com.wejoy.jackaroo.record.GameCareerCache
import com.wejoy.jackaroo.record.GameCareerInfo
import com.wejoy.jackaroo.record.JackarooGameInfo
import com.wejoy.jackaroo.record.JackarooGameRecordActivity
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.databinding.IncludeRankScoreLayoutBinding
import com.wepie.wespy.databinding.IncludeUserDetailSorceAndSingerLayoutBinding
import com.wepie.wespy.helper.shence.ShenceEvent
import com.wepie.wespy.module.common.jump.JumpUtil
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import java.util.Collections

class UserStatsDetailItem(
    uid: Int, activity: FragmentActivity
) : AbsUserDetailItem(uid, activity) {

    private val adapter = UserStatsAdapter(uid)

    init {
        val concatFlow = GameCareerCache.collectionAlbumFlow(uid, viewModel.viewModelScope).combine(
            GameCareerCache.userRecordInfoFlow(uid, viewModel.viewModelScope)
        ) { album, userRecord ->
            GameCareerInfo(userRecord?.rankStat, album)
        }
        activity.lifecycleScope.launch {
            concatFlow.collectLatest {
                adapter.updateGameCareerInfo(it)
            }
        }
        viewModel.userLiveData.observe(activity) {
            update(it)
        }
    }

    override fun getAdapter(): RecyclerView.Adapter<out RecyclerView.ViewHolder> = adapter
}

class UserStatsAdapter(private val uid: Int) : UserDetailAdapter<Int, UserStatsViewHolder>(0) {

    private var canShowDetail = true
    private var info: GameCareerInfo = GameCareerInfo()
    private val action = object : ItemAction {
        override fun onClickCollection(context: Context) {
            ShenceEvent.appClick(
                TrackScreenName.SELF_HOMEPAGE_2,
                TrackString.GAME_CAREER_COLLECTION,
                Collections.emptyMap()
            )
            JumpUtil.gotoUserGradeActivity(
                uid,
                context,
                JackarooGameRecordActivity.SUB_TAB_TYPE_COLLECTION
            )
        }

        override fun onClickGameRankLay(context: Context) {
            ShenceEvent.appClick(
                TrackScreenName.SELF_HOMEPAGE_2, TrackSource.RECORD, Collections.emptyMap()
            )
            JumpUtil.gotoUserGradeActivity(
                uid,
                context,
                JackarooGameRecordActivity.SUB_TAB_TYPE_GAME_RECORD
            )
        }
    }

    override fun updateUser(user: User) {
        super.updateUser(user)
        canShowDetail = user.achivement_not_show == 0 || LoginHelper.getLoginUid() == uid
        notifyItemChanged(0, canShowDetail)
    }

    fun updateGameCareerInfo(careerInfo: GameCareerInfo) {
        if (info == careerInfo) {
            return
        }
        val old = info
        info = careerInfo
        notifyItemChanged(0, genPayloadFromDiff(old, careerInfo))
    }

    private fun genPayloadFromDiff(oldInfo: GameCareerInfo, careerInfo: GameCareerInfo): Any? {
        return if (oldInfo.jackarooGameInfo == careerInfo.jackarooGameInfo) {
            careerInfo.collectionAlbum
        } else if (oldInfo.collectionAlbum == careerInfo.collectionAlbum) {
            careerInfo.jackarooGameInfo
        } else {
            null
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserStatsViewHolder {
        val view = parent.inflate(R.layout.include_user_detail_sorce_and_singer_layout)
        view.setOnDoubleClick { v ->
            JumpUtil.gotoUserGradeActivity(user.uid, v.context)
            ShenceEvent.appClick(
                TrackScreenName.SELF_HOMEPAGE_2,
                TrackString.GAME_CAREER_COLLECTION,
                Collections.emptyMap()
            )
        }
        return UserStatsViewHolder(view, action)
    }

    override fun getItemCount(): Int = if (UserService.get().isSystemUser(uid)) 0 else 1
    override fun onBindViewHolder(
        holder: UserStatsViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            return super.onBindViewHolder(holder, position, payloads)
        }
        payloads.forEach {
            when (it) {
                is JackarooGameInfo -> {
                    holder.bindRankInfo(it)
                }

                is CollectionAlbum -> {
                    holder.bindCollection(it)
                }

                is Boolean -> holder.bindStateSummaryVisibility(it)
                else -> {
                    super.onBindViewHolder(holder, position, payloads)
                }
            }
        }

    }

    override fun onBindViewHolder(holder: UserStatsViewHolder, position: Int) {
        holder.bindView(info, canShowDetail)
    }

    interface ItemAction {
        fun onClickCollection(context: Context)
        fun onClickGameRankLay(context: Context)
    }

}

class UserStatsViewHolder(
    itemView: View, action: UserStatsAdapter.ItemAction
) :
    RecyclerView.ViewHolder(itemView) {


    val binding: IncludeUserDetailSorceAndSingerLayoutBinding =
        IncludeUserDetailSorceAndSingerLayoutBinding.bind(itemView)


    init {

        binding.stateRankClickArea.setOnDoubleClick {
            action.onClickGameRankLay(
                it.context
            )
        }

        binding.stateCollectionClickArea.setOnDoubleClick {
            action.onClickCollection(it.context)
        }

    }

    fun bindView(gameCareerInfo: GameCareerInfo, showDetail: Boolean) {
        val info = gameCareerInfo.jackarooGameInfo ?: JackarooGameInfo()
        val collectionAlbum = gameCareerInfo.collectionAlbum ?: CollectionAlbum()
        bindStateSummaryVisibility(showDetail)
        bindRankInfo(info)
        bindChessSkinShowInfo(
            collectionAlbum.inUseChessPropId,
            collectionAlbum.collectionValue,
            collectionAlbum.totalObtainedNum,
            collectionAlbum.calTotalCollectionNum()
        )
    }

    fun bindStateSummaryVisibility(showDetail: Boolean) {
        binding.gameStateDescLay.isVisible = showDetail
    }

    fun bindCollection(collectionAlbum: CollectionAlbum) {
        bindChessSkinShowInfo(
            collectionAlbum.inUseChessPropId,
            collectionAlbum.collectionValue,
            collectionAlbum.totalObtainedNum,
            collectionAlbum.calTotalCollectionNum()
        )
    }

    private fun bindChessSkinShowInfo(
        inUseChessManId: Int,
        collectionValue: Int,
        curCollectNum: Int,
        totalCollectNum: Int
    ) {
        val propItem = PropItemConfig::class.instance().getPropItem(inUseChessManId)
        binding.jackarooChessPicShowLay.bind(propItem)
        binding.collectionSummaryTv.text = ResUtil.getStr(
            R.string.jackaroo_game_career_collection_summary,
            curCollectNum,
            totalCollectNum
        )
        binding.collectionValueTv.text = collectionValue.toString()
    }

    fun bindRankInfo(info: JackarooGameInfo) {
        binding.gameStatTotalCountDescTv.text = String.format(
            "%s %s",
            ResUtil.getString(R.string.jackaroo_game_record_total_round),
            info.playCount,
        )
        binding.gameStatWinRateDescTv.text = String.format(
            "%s %s",
            ResUtil.getString(R.string.jackaroo_game_record_win_rate),
            StringUtil.formatName(
                ResUtil.getStr(R.string.jackaroo_game_record_total_win_ratio, info.getWinRatio())
            )
        )
        val qualifyingGradeInfo = info.qualifyingGradeInfo
        var grade: ConstV3Info.QualifyingGrade? =
            ConstV3Info::class.instance().findQFGrade(qualifyingGradeInfo.grade)

        val currentQualifyingGradeStr =
            if (qualifyingGradeInfo.star > 0) qualifyingGradeInfo.star.toString() else null
        binding.gameCurrentRankLay.bind(
            ResUtil.getStr(R.string.jackaroo_game_record_current_qualifying),
            grade?.icon,
            currentQualifyingGradeStr
        )
        grade = ConstV3Info::class.instance().findQFGrade(qualifyingGradeInfo.highestGrade)
        val highestQualifyingGradeStr =
            if (qualifyingGradeInfo.highestStar > 0) qualifyingGradeInfo.highestStar.toString() else null
        binding.gameHighestRankLay.bind(
            ResUtil.getStr(R.string.jackaroo_game_record_highest_qualifying),
            grade?.icon,
            highestQualifyingGradeStr
        )
    }

    private fun IncludeRankScoreLayoutBinding.bind(
        title: String,
        rankIvModel: String?,
        qualifyingGradeStr: String?
    ) {
        gameRankTitleTv.text = title
        rankIvModel.takeUnless { it.isNullOrBlank() }?.let {
            WpImageLoader.load(it, gameRankIv)
        } ?: run {
            gameRankIv.setImageResource(R.drawable.jackaroo_game_record_no_qualifying)
        }
        gameQualifyingGradeLay.isVisible = qualifyingGradeStr != null
        gameQualifyingGradeKingTv.text = qualifyingGradeStr ?: ""
    }

}


