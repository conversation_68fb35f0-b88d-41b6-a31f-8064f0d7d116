package com.wepie.wespy.module.settings.edituser;

import android.os.Bundle;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.IMMHelper;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.inputfilter.LangEnterLengthFilter;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.entity.User;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.R;
import com.wepie.wespy.base.NameEditText;
import com.wepie.wespy.module.common.SimpleTextWatcher;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

public class EditNickNameActivity extends BaseActivity {

    public static final String TAG = EditNickNameActivity.class.getName();
    private NameEditText mNewNickNameText;
    private ImageView mDeleteEmailIcon;
    private TextView mNickGrayWords;
    private static final int MAX_INPUT = 16;
    private BaseWpActionBar actionBar;

    private void initViews() {
        actionBar = findViewById(R.id.action_bar);
        actionBar.addTitleRightTextWithBack(R.string.nick_name, ResUtil.getStr(R.string.confirm), v -> {
            IMMHelper.hideSoftInput(EditNickNameActivity.this, actionBar.getWindowToken());
            finish();
        }, v -> updateNickName());
        mNewNickNameText = findViewById(R.id.email_text);
        mNewNickNameText.requestFocus();
        mDeleteEmailIcon = findViewById(R.id.delete_email_btn);
        mNickGrayWords = findViewById(R.id.nick_gray_words);

        User curUser = LoginHelper.getLoginUser();
        String nickName = "";
        if (curUser != null) {
            nickName = curUser.getNickname();
        }
        mNewNickNameText.setText(nickName);
        int length = nickName.length();
        try {
            mNewNickNameText.setSelection(length);
        } catch (Exception e) {
            e.printStackTrace();
        }
        mDeleteEmailIcon.setVisibility(View.VISIBLE);
        mNewNickNameText.setFilters(new InputFilter[]{new LangEnterLengthFilter(LangEnterLengthFilter.NICKNAME_LIMIT)});
        if (GlobalConfigManager.getInstance().isTurkeyServer()) {
            mNickGrayWords.setVisibility(View.VISIBLE);
        } else {
            mNickGrayWords.setVisibility(View.GONE);
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit_nick_name);
        initViews();
        initEvents();
    }

    private void initEvents() {
        mNewNickNameText.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (TextUtils.isEmpty(s)) {
                    mDeleteEmailIcon.setVisibility(View.GONE);
                } else {
                    mDeleteEmailIcon.setVisibility(View.VISIBLE);
                }
            }
        });
        mDeleteEmailIcon.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mNewNickNameText.setText("");
            }
        });
    }

    private void updateNickName() {
        String nickName = mNewNickNameText.getNameText().toString().trim();
        if (TextUtil.isEmpty(nickName)) {
            ToastUtil.show(ResUtil.getStr(R.string.edit_nickname_empty_tips));
        } else {
            UserInfoHttpHelper.updateNickName(this, nickName, new UserInfoHttpHelper.UpdateCallback() {
                @Override
                public void onUpdated() {
                    finish();
                    EventDispatcher.postUserInfoUpdate(LoginHelper.getLoginUser());
                }
            });
        }
    }

}
