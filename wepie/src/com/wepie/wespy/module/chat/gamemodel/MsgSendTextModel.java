package com.wepie.wespy.module.chat.gamemodel;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.TouchEffectUtil;
import com.huiwan.configservice.constentity.propextra.ChatBubbleItem;
import com.huiwan.store.database.WPModel;
import com.huiwan.store.database.WPStore;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.emoji.view.EmojiHelper;
import com.wepie.emoji.view.resource.EmojiItem;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.view.SimpleTextView;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.module.abtest.AbTestManager;
import com.wepie.wespy.module.chat.manager.ChatManager;
import com.wepie.wespy.module.game.game.activity.AdapterHolderUtil;
import com.wepie.wespy.module.game.game.activity.GameConstant;
import com.wepie.wespy.module.game.game.activity.TextSpanUtil;
import com.wepie.wespy.module.game.util.GameDialogUtil;
import com.wepie.wespy.module.game.util.GameDialogUtil.DialogClickCallback;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import java.util.List;

/**
 * 发送文本的model
 *
 * <AUTHOR>
 */
public class MsgSendTextModel extends LinearLayout {
    private final Context mContext;
    private ImageView failImage;
    private ImageView progressImage;
    private SimpleTextView sendStv;
    private TextView sendTx;
    private ViewGroup msgLay;
    private AnimationDrawable animation;
    private GiftTextViewHolder giftViewHolder;
    private MsgCommonTextViewHolder commonViewHolder;

    public MsgSendTextModel(Context context) {
        this(context, null);
    }

    public MsgSendTextModel(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.msg_send_text_view, this);
        // 初始化基本UI元素
        failImage = findViewById(R.id.msg_send_text_fail);
        progressImage = findViewById(R.id.msg_send_text_progress);
        sendTx = findViewById(R.id.msg_send_text_content);
        sendStv = findViewById(R.id.text_stv);
        msgLay = findViewById(R.id.msg_send_text_lay);

        // 初始化ViewHolder
        giftViewHolder = new GiftTextViewHolder(this);
        commonViewHolder = new MsgCommonTextViewHolder(this);

        progressImage.setImageResource(R.drawable.anim_sending);
        animation = (AnimationDrawable) progressImage.getDrawable();
        if (AbTestManager.getInstance().useNewText()) {
            sendTx.setVisibility(GONE);
        } else {
            sendStv.setVisibility(GONE);
        }

        TouchEffectUtil.addTouchEffect(this);
    }

    public void updateView(WPModel model, int modelType, View.OnLongClickListener listener) {
        failImage.setVisibility(View.GONE);
        progressImage.setVisibility(View.GONE);
        sendTx.setVisibility(VISIBLE);
        sendStv.setVisibility(VISIBLE);
        if (AbTestManager.getInstance().useNewText()) {
            sendTx.setVisibility(GONE);
        } else {
            sendStv.setVisibility(GONE);
        }
        giftViewHolder.hideGift();
        if (modelType == AdapterHolderUtil.TYPE_CHAT_MSG) {
            WPMessage chatMsg = (WPMessage) model;
            updateChatView(chatMsg, listener);
            TextSpanUtil.checkTextSpan(mContext, commonViewHolder.getBubbleItem().getDeepLinkColor(), chatMsg.getSend_uid(), -1, msgLay, sendStv, chatMsg.getContent(), listener);
            TextSpanUtil.checkTextSpan(mContext, commonViewHolder.getBubbleItem().getDeepLinkColor(), chatMsg.getSend_uid(), -1, msgLay, sendTx, chatMsg.getContent(), listener);
        }
    }

    private void updateChatView(final WPMessage msg, OnLongClickListener listener) {
        setOnClickListener(null);
        updateStatus(msg);
        if (!TextUtils.isEmpty(msg.getRefMid())) {
            msgLay.setVisibility(GONE);
            commonViewHolder.hideEmojiLayout();
            commonViewHolder.showBubbleItem(false);
            commonViewHolder.showQuoteItem();
            giftViewHolder.hideGift();
            ChatManager.getInstance().findQuoteMsg(msg.getRefMid(), ILifeUtil.toLife(this),
                    refMsg -> commonViewHolder.getQuoteItem().bind(msg, refMsg, listener));
            checkMeasureSelf();
            return;
        }
        commonViewHolder.hideQuoteItem();
        msgLay.setVisibility(VISIBLE);
        commonViewHolder.hideEmojiLayout();
        String text = msg.getContent();

        commonViewHolder.showBubble(msg.getBubbleId(), true);
        ChatBubbleItem chatBubbleItem = commonViewHolder.getBubbleItem();
        SpannableStringBuilder ssb = new SpannableStringBuilder();

        sendStv.setTextColor(chatBubbleItem.getTextColor());
        sendTx.setTextColor(chatBubbleItem.getTextColor());
        boolean filter = giftViewHolder.showGift(msg, chatBubbleItem, true);
        if (filter) {
            sendStv.setVisibility(GONE);
            sendTx.setVisibility(GONE);
        } else {
            List<EmojiItem> emojiItems = EmojiHelper.getContinuousEmoji(text);
            if (!emojiItems.isEmpty()) {
                msgLay.setVisibility(GONE);
                commonViewHolder.showBubbleItem(false);
                commonViewHolder.showBigEmojis(emojiItems, listener);
            } else {
                sendStv.setTextColor(commonViewHolder.getTextColor());
                sendTx.setTextColor(commonViewHolder.getTextColor());
                EmojiHelper.parseEmoji2Ssb(getContext(), ssb, text, 16);
                sendStv.setText(ssb);
                sendTx.setText(ssb);
            }
        }
        checkMeasureSelf();
    }

    private void updateStatus(WPMessage msg) {
        int status = msg.getStatus();
        switch (status) {
            case WPMessage.STATUS_SENDING:
                long curTime = TimeUtil.getServerTime();
                long sendTime = msg.getTime();
                if (curTime - sendTime > GameConstant.SEND_TIME_OUT) {
                    msg.setStatus(WPMessage.STATUS_FAIL);
                    setChatFailStatus(msg);
                    WPStore.saveAsync(msg);
                    break;
                }
                progressImage.setVisibility(View.VISIBLE);
                failImage.setVisibility(View.GONE);
                animation.start();
                break;
            case WPMessage.STATUS_OK:
                progressImage.setVisibility(View.GONE);
                failImage.setVisibility(View.GONE);

                break;
            case WPMessage.STATUS_FAIL:
                setChatFailStatus(msg);
                break;
            default:
                break;
        }
    }

    private void checkMeasureSelf() {
        sendTx.setGravity(sendTx.getText().length() > 3 ? Gravity.START | Gravity.CENTER_VERTICAL : Gravity.CENTER);
        sendTx.requestLayout();

        sendStv.setGravity(sendStv.getText().length() > 3 ? SimpleTextView.ALIGN_NORMAL : SimpleTextView.ALIGN_CENTER);
        sendStv.requestLayout();
    }

    private void setChatFailStatus(final WPMessage msg) {
        progressImage.setVisibility(View.GONE);
        failImage.setVisibility(View.VISIBLE);

        this.setOnClickListener(v -> GameDialogUtil.showResendEnsureDialog(mContext, new DialogClickCallback() {

            @Override
            public void onClickSure(String content) {
                EventDispatcher.postSingleChatResendMsg(msg);
            }

            @Override
            public void onClickCancel() {
            }
        }));
    }
}
