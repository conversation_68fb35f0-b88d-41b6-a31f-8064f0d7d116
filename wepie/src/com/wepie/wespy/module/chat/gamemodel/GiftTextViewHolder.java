package com.wepie.wespy.module.chat.gamemodel;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.ChatBubbleItem;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.model.gift.Gift;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.module.chat.ui.item.GiftCardUtil;


/**
 * 礼物消息的ViewHolder，用于MsgReceiveTextModel和MsgSendTextModel的共同功能
 *
 * <AUTHOR>
 */
public class GiftTextViewHolder {

    // 礼品相关UI元素
    private final TextView giftTitleTv;
    private final TextView giftDescTv;
    private final View giftDivider;
    private final TextView giftExtTv;
    private final View giftExtraDivider;
    private final TextView giftExtraReturnTv;
    private final ImageView giftIcon;

    private final ViewGroup mainLayout; // 主要的消息布局

    public GiftTextViewHolder(ViewGroup rootView) {
        this.mainLayout = rootView.findViewById(R.id.new_gift_lay);

        giftIcon = mainLayout.findViewById(R.id.gift_icon);
        giftTitleTv = mainLayout.findViewById(R.id.gift_title_tv);
        giftDescTv = mainLayout.findViewById(R.id.gift_desc_tv);
        giftDivider = mainLayout.findViewById(R.id.gift_div_view);
        giftExtTv = mainLayout.findViewById(R.id.gift_ext_tv);
        giftExtraDivider = mainLayout.findViewById(R.id.gift_extra_div_view);
        giftExtraReturnTv = mainLayout.findViewById(R.id.gift_extra_return_tv);
    }

    private void showIcon(WPMessage msg, boolean isGift) {
        if (giftIcon == null) return;
        String url;
        ImageLoadInfo info = ImageLoadInfo.newInfo();
        if (isGift) {
            Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(msg.getGiftIdFromExtension());
            if (gift == null) return;
            url = gift.getMedia_url();
            info = ImageLoadInfo.newGiftInfo();
        } else {
            PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(msg.getPropIdFromExtension());
            if (propItem == null) return;
            url = propItem.getMediaUrl();
        }

        WpImageLoader.load(url, giftIcon, info);
    }

    /**
     * 隐藏礼品图标
     */
    public void hideGift() {
        mainLayout.setVisibility(View.GONE);
    }

    /**
     * 更新礼品扩展信息的显示
     */
    private void updateGiftExtension(WPMessage msg, boolean isSend, ChatBubbleItem bubbleItem) {
        if (bubbleItem == null) return;

        String giftRecCoin = msg.getGiftRecCoinFromExtension();
        boolean showGiftExtension = !TextUtils.isEmpty(giftRecCoin);
        if (!showGiftExtension) {
            if (giftDivider != null) {
                giftDivider.setVisibility(View.GONE);
            }
            if (giftExtTv != null) {
                giftExtTv.setVisibility(View.GONE);
            }
            if (giftExtraDivider != null) {
                giftExtraDivider.setVisibility(View.GONE);
            }
            if (giftExtraReturnTv != null) {
                giftExtraReturnTv.setVisibility(View.GONE);
            }
            return;
        }

        // 设置分隔线颜色
        if (giftDivider != null) {
            giftDivider.setVisibility(View.VISIBLE);
            giftDivider.setBackgroundColor(GiftCardUtil.getGiftDividerColor(bubbleItem, msg.getBubbleId(), isSend));
        }

        // 设置礼品收益文本
        if (giftExtTv != null) {
            giftExtTv.setVisibility(View.VISIBLE);
            giftExtTv.setTextColor(isSend ? bubbleItem.getGiftDescColor() : bubbleItem.getGiftTipColor());
            giftExtTv.setText(MsgTextHelper.getGiftCoinText(giftRecCoin, bubbleItem));
        }

        // 处理额外返还金币
        int extraReturnCoin = msg.getGiftExtraReturnCoinFromExtension();
        if (extraReturnCoin > 0) {
            if (giftExtraDivider != null) {
                giftExtraDivider.setVisibility(View.VISIBLE);
                giftExtraDivider.setBackgroundColor(GiftCardUtil.getGiftDividerColor(bubbleItem, msg.getBubbleId(), isSend));
            }

            if (giftExtraReturnTv != null) {
                giftExtraReturnTv.setVisibility(View.VISIBLE);
                giftExtraReturnTv.setTextColor(bubbleItem.getGiftTipColor());
                MsgTextHelper.updateGiftExtraReturnCoin(giftExtraReturnTv, extraReturnCoin, bubbleItem.getGiftDescColor());
            }
        } else {
            if (giftExtraDivider != null) {
                giftExtraDivider.setVisibility(View.GONE);
            }
            if (giftExtraReturnTv != null) {
                giftExtraReturnTv.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 更新新礼品样式的标题和描述
     */
    public void updateNewGiftInfo(String title, String desc, ChatBubbleItem bubbleItem) {
        if (bubbleItem == null) return;
        if (giftTitleTv != null) {
            giftTitleTv.setTextColor(bubbleItem.getTextColor());
            giftTitleTv.setVisibility(View.VISIBLE);
            giftTitleTv.setText(title);
        }

        if (giftDescTv != null) {
            giftDescTv.setTextColor(bubbleItem.getGiftTipColor());
            giftDescTv.setVisibility(View.VISIBLE);
            giftDescTv.setText(desc);
        }
    }

    public boolean showGift(WPMessage msg, ChatBubbleItem bubbleItem, boolean isSelf) {
        int type = 0;
        if (!TextUtils.isEmpty(msg.getExtension())) {
            type = msg.getExtensionType();
        }
        boolean isGift = false;

        MsgTextHelper.NewChatGift chatGift = null;
        if (msg.isExtensionTypeGift(type)) {
            chatGift = MsgTextHelper.NewChatGift.build(msg.getContent());
            isGift = true;
        } else if (msg.isExtensionTypeGameSkin(type)) {
            chatGift = MsgTextHelper.NewChatGift.build("");
            chatGift.useNewGiftStyle = true;
            chatGift.title = ResUtil.getStr(R.string.send_you_one_prop);
            chatGift.desc = msg.getPropInfoDescFromExtension();
        }
        if (chatGift == null || !chatGift.useNewGiftStyle) {
            hideGift();
            return false;
        }
        if (mainLayout != null) {
            mainLayout.setVisibility(View.VISIBLE);
        }
        showIcon(msg, isGift);
        updateNewGiftInfo(chatGift.title, chatGift.desc, bubbleItem);
        updateGiftExtension(msg, isSelf, bubbleItem);

        return true;
    }
}
