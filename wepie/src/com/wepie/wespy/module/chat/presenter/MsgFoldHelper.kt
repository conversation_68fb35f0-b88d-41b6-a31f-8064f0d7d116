package com.wepie.wespy.module.chat.presenter

import com.wepie.wespy.model.entity.ChatMsg

class MsgFoldHelper(val msgArray: MutableList<ChatMsg>, val condition: ICondition) {

    abstract class ICondition {
        abstract fun canFold(preChatMsg: ChatMsg, chatMsg: ChatMsg): Boolean

        object NoneCondition : ICondition() {
            override fun canFold(preChatMsg: ChatMsg, chatMsg: ChatMsg): Boolean = false
        }
    }

    fun add(chatMsg: ChatMsg) {
        add(listOf(chatMsg), false)
    }

    /**
     * @return 返回实际增加的条数
     * 目前用于下拉刷新加载数据，因为有折叠的存在，需要知道实际load的数量
     */
    fun add(chatMsgList: List<ChatMsg>, head: Boolean): Int {
        val convert2FoldMsg = convert2FoldMsg(chatMsgList)
        var addSize = convert2FoldMsg.size
        val mergeFoldMsg = if (head) {
            mergeFoldMsg(convert2FoldMsg, msgArray)
        } else {
            mergeFoldMsg(msgArray, convert2FoldMsg)
        }
        msgArray.clear()
        msgArray.addAll(mergeFoldMsg.second)
        if (mergeFoldMsg.first) {
            addSize -= 1
        }
        return addSize
    }

    fun remove(chatMsg: ChatMsg?) {
        chatMsg ?: return
        val iterator = msgArray.iterator()
        while (iterator.hasNext()) {
            val foldMsg = iterator.next()
            // 理论上消息只会删除非折叠消息， 非折叠消息删除后不进行任何操作
            if (chatMsg.mid == foldMsg.mid) {
                iterator.remove()
                break
            }
            val msgIterator = foldMsg.combinedMsgs.iterator()
            while (msgIterator.hasNext()) {
                if (chatMsg.mid == msgIterator.next().mid) {
                    msgIterator.remove()
                    break
                }
            }
        }
    }

    fun set(chatMsg: ChatMsg?) {
        chatMsg ?: return
        for (i in 0 until msgArray.size) {
            val msg = msgArray[i]
            if (chatMsg.mid == msg.mid) {
                msgArray[i] = chatMsg
                break
            }
            for (i in 0 until msg.combinedMsgs.size) {
                val foldMsg = msg.combinedMsgs[i]
                if (chatMsg.mid == foldMsg.mid) {
                    msg.combinedMsgs[i] = chatMsg
                    break
                }
            }
        }
    }

    fun expandSingleMsg(chatMsg: ChatMsg, position: Int) {
        chatMsg.handOperatedExpand = true
        val combinedMsgs = chatMsg.combinedMsgs.toList()
        chatMsg.combinedMsgs.clear()
        combinedMsgs.forEach {
            it.handOperatedExpand = true
        }
        if (position == msgArray.size - 1) {
            msgArray.addAll(combinedMsgs)
        } else {
            msgArray.addAll(position + 1, combinedMsgs)
        }
    }

    fun expandAll() {
        val expandMsg = mutableListOf<ChatMsg>()
        msgArray.forEach { chatMsg ->
            expandMsg.add(chatMsg)
            chatMsg.handOperatedExpand = false
            expandMsg.addAll(chatMsg.combinedMsgs.toList())
            chatMsg.combinedMsgs.forEach {
                it.handOperatedExpand = false
            }
            chatMsg.combinedMsgs.clear()
        }
        msgArray.clear()
        msgArray.addAll(expandMsg)
    }

    fun foldAll() {
        expandAll()
        val convert2FoldMsg = convert2FoldMsg(msgArray)
        msgArray.clear()
        msgArray.addAll(convert2FoldMsg)
    }

    private fun mergeFoldMsg(
        firstList: List<ChatMsg>,
        lastList: MutableList<ChatMsg>
    ): Pair<Boolean, List<ChatMsg>> {
        val foldMsgList = mutableListOf<ChatMsg>()
        var folded = false
        when {
            firstList.isEmpty() -> {
                foldMsgList.addAll(lastList)
            }
            lastList.isEmpty() -> {
                foldMsgList.addAll(firstList)
            }
            else -> {
                foldMsgList.addAll(firstList)
                val foldMsgPre = firstList.last()
                val foldMsgNext = lastList.first()
                if (!foldMsgPre.handOperatedExpand && !foldMsgNext.handOperatedExpand) {
                    val canFoldPreAndNext = foldMsgPre.combinedMsgs.isEmpty()
                            && condition.canFold(foldMsgPre, foldMsgNext)
                    val canFoldPreCombineAndNext = foldMsgPre.combinedMsgs.isNotEmpty()
                            && condition.canFold(foldMsgPre.combinedMsgs.last(), foldMsgNext)
                    if (canFoldPreAndNext || canFoldPreCombineAndNext) {
                        folded = true
                        foldMsgPre.combinedMsgs.add(foldMsgNext)
                        foldMsgPre.combinedMsgs.addAll(foldMsgNext.combinedMsgs)
                        foldMsgNext.combinedMsgs.clear()
                        lastList.removeFirst()
                    }
                }
                foldMsgList.addAll(lastList)
            }
        }
        return Pair(folded, foldMsgList)
    }

    private fun convert2FoldMsg(chatMsgList: List<ChatMsg>): MutableList<ChatMsg> {
        val chatMsgs = mutableListOf<ChatMsg>()
        var preMsg: ChatMsg? = null
        chatMsgList.forEach { chatMsg ->
            if (preMsg == null || !condition.canFold(preMsg!!, chatMsg)) {
                chatMsgs.add(chatMsg)
            } else {
                chatMsgs.last().combinedMsgs.add(chatMsg)
            }
            preMsg = chatMsg
        }
        return chatMsgs
    }
}