package com.wepie.wespy.module.chat.ui.item;

import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_BOXING_AGENT_INVITE;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_COCOS_TEAM_INVITE_GROUP;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_SAY_GUESS_INVITE_GROUP;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_SOCIAL_ROOM_INVITE_FAMILY_GROUP;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_SQUID_INVITE;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_VOICE_ROOM_INVITE;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_VOICE_ROOM_INVITE_WEDDING;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_XROOM_INVITE;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TouchEffectUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.component.prop.ChatMsgBubbleItem;
import com.huiwan.configservice.constentity.propextra.ChatBubbleItem;
import com.wepie.emoji.view.EmojiHelper;
import com.wepie.emoji.view.resource.EmojiItem;
import com.wepie.emoji.view.resource.MultipleEmojiHelper;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.GroupChatMsg;
import com.wepie.wespy.model.entity.InviteCardInfo;
import com.wepie.wespy.module.chat.gamemodel.MsgInviteHelper;
import com.wepie.wespy.module.game.game.activity.TextSpanUtil;

import java.util.List;


/**
 * Created by geeksammao on 22/08/2017.
 */

public class TextItem extends FrameLayout {
    private boolean isSelf;
    private ViewGroup contentLay;
    private TextView chatContentTv;
    private Context mContext;
    private ChatMsgBubbleItem bubbleItem;
    private static final String TAG = "TextItem";
    private View inviteLayout;
    private LinearLayout emojiLayout;
    private View receiveContainer;

    public TextItem(@NonNull Context context, boolean isSelf) {
        super(context);
        this.mContext = context;
        this.isSelf = isSelf;
        init();
    }

    private void init() {
        if (isSelf) {
            inflate(getContext(), R.layout.chat_self_text_item, this);
        } else {
            inflate(getContext(), R.layout.chat_friend_text_item, this);
        }
        initView(isSelf);
        TouchEffectUtil.addTouchEffect(this);
    }

    private boolean isSubTypeOk(int subType) {
        return subType == SUBTYPE_VOICE_ROOM_INVITE ||
                subType == SUBTYPE_VOICE_ROOM_INVITE_WEDDING ||
                subType == SUBTYPE_SOCIAL_ROOM_INVITE_FAMILY_GROUP ||
                subType == SUBTYPE_COCOS_TEAM_INVITE_GROUP ||
                subType == SUBTYPE_SAY_GUESS_INVITE_GROUP ||
                subType == SUBTYPE_XROOM_INVITE ||
                subType == SUBTYPE_BOXING_AGENT_INVITE ||
                subType == SUBTYPE_SQUID_INVITE;
    }

    public void bind(final ChatMsg msg, OnLongClickListener longClickListener) {
        bubbleItem.showBubble(msg.getBubbleId(), isSelf);
        String msgContent = msg.getContent();
        int subType = msg.getSubType();
        boolean subTypeOk = isSubTypeOk(subType);
        if (subTypeOk && msg.getMediaType() == ChatMsg.MEDIA_TYPE_TEXT && msg instanceof GroupChatMsg) {
            //语言房，婚房邀请
            chatContentTv.setTextColor(bubbleItem.getTextColor());
            bindVoiceInvite(msg.getExt(), msg.getBubbleId(), subType, longClickListener);
        } else {
            List<EmojiItem> emojiItems = EmojiHelper.getContinuousEmoji(msgContent);
            boolean showBigEmoji = !emojiItems.isEmpty();
            updateLayoutVisibility(!showBigEmoji, false, showBigEmoji, !showBigEmoji, !showBigEmoji);
            if (showBigEmoji) {
                MultipleEmojiHelper.showEmojis(emojiLayout, emojiItems, longClickListener);
            } else {
                EmojiHelper.parseEmojis(getContext(), chatContentTv, msgContent);
                TextSpanUtil.checkTextSpan(getContext(), bubbleItem.getDeepLinkColor(), msg.getSend_uid(), -1, this, chatContentTv, msgContent, longClickListener);
                chatContentTv.setTextColor(bubbleItem.getTextColor());
            }
        }
    }

    private void updateLayoutVisibility(boolean showBubble, boolean showInvite, boolean showBigEmoji, boolean showChatContent, boolean showChatContainer) {
        if (showBigEmoji) {
            emojiLayout.setVisibility(VISIBLE);
        } else {
            emojiLayout.setVisibility(GONE);
        }
        if (showInvite) {
            inviteLayout.setVisibility(VISIBLE);
            inviteLayout.setLayoutDirection(ScreenUtil.isRtl() ? LAYOUT_DIRECTION_RTL : LAYOUT_DIRECTION_LTR);
        } else {
            inviteLayout.setVisibility(GONE);
        }
        if (showChatContent) {
            chatContentTv.setVisibility(VISIBLE);
        } else {
            chatContentTv.setVisibility(GONE);
        }
        if (showChatContainer) {
            receiveContainer.setVisibility(VISIBLE);
        } else {
            receiveContainer.setVisibility(GONE);
        }

        if (showBubble) {
            bubbleItem.setVisibility(VISIBLE);
        } else {
            bubbleItem.setVisibility(GONE);
        }
    }

    private void bindVoiceInvite(String ext, int bubbleID, final int subType, OnLongClickListener longClickListener) {
        try {
            InviteCardInfo cardInfo = InviteCardInfo.build(ext);
            View.OnClickListener onClickListener = new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    MsgInviteHelper.handleCardJump(getContext(), cardInfo, subType);
                }
            };
            if (cardInfo.isNewInvite) {
                updateLayoutVisibility(true, true, false, false, true);
                bubbleItem.showBubble(bubbleID, false);
                ChatBubbleItem buddleItem = MsgInviteHelper.getInviteBuddleItem(bubbleID, bubbleItem.getBubbleItem());
                MsgInviteHelper.updateNewInvite(inviteLayout, cardInfo.msgContent, cardInfo, buddleItem, onClickListener, longClickListener);
            } else {
                updateLayoutVisibility(true, false, false, true, true);
                TextSpanUtil.setCustomSpan(mContext, bubbleItem.getDeepLinkColor(), chatContentTv, contentLay, cardInfo.msgContent, ResUtil.getStr(R.string.common_click_enter), onClickListener,
                        longClickListener);
            }
        } catch (Exception e) {
            TimeLogger.err("error parse ext");
        }
    }

    private void initView(boolean isSelf) {
        if (isSelf) {
            chatContentTv = findViewById(R.id.chat_self_txt_tv);
            contentLay = findViewById(R.id.self_txt_lay);
        } else {
            chatContentTv = findViewById(R.id.chat_friend_txt_tv);
            contentLay = findViewById(R.id.friend_txt_lay);
        }
        bubbleItem = findViewById(R.id.bubble_item);
        inviteLayout = findViewById(R.id.invite_layout);
        emojiLayout = findViewById(R.id.emoji_layout);
        receiveContainer = findViewById(R.id.msg_receive_text_lay);
    }
}
