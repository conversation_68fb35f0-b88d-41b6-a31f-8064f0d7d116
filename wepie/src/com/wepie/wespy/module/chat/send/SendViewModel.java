package com.wepie.wespy.module.chat.send;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.common.MediaChooseLimit;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.IMMHelper;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.constants.ActivityResultCode;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.store.PrefConfig;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.User;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.scripple.KeyboardListenerView;
import com.wejoy.weplay.ex.cancellable.EventBusExKt;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.emoji.view.EmojiHelper;
import com.wepie.emoji.view.EmojiView;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.liblog.main.HLog;
import com.wepie.libphoto.PhotoCallback;
import com.wepie.libphoto.PhotoUtil;
import com.wepie.libphoto.WPChoosePhotoDialog;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.RedPacket;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.entity.emoticon.EmoticonItem;
import com.wepie.wespy.model.entity.group.GroupInfo;
import com.wepie.wespy.model.event.DeleteMsgEvent;
import com.wepie.wespy.model.event.chat.RecallMsgEvent;
import com.wepie.wespy.model.event.chat.SingleChatDeleteMsgEvent;
import com.wepie.wespy.model.event.chat.SingleChatRecallMsgEvent;
import com.wepie.wespy.module.care.main.CareUtil;
import com.wepie.wespy.module.chat.dataservice.group.GroupService;
import com.wepie.wespy.module.chat.gamemodel.MsgQuoteContentWithDeleteItem;
import com.wepie.wespy.module.chat.manager.RedPacketManager;
import com.wepie.wespy.module.chat.presenter.CurrentChatHelper;
import com.wepie.wespy.module.chat.send.face.SendEmoticonView;
import com.wepie.wespy.module.common.SimpleTextWatcher;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.common.jump.UserGameStatusUtil;
import com.wepie.wespy.module.game.game.util.RedPacketUtil;
import com.wepie.wespy.module.game.room.roomcreate.InviteFriendActivity;
import com.wepie.wespy.module.gift.GiftShowConfigHelper;
import com.wepie.wespy.module.scribble.ScribbleActivity;
import com.wepie.wespy.net.tcp.sender.ChatPacketSenderNew;
import com.wepie.wespy.utils.ShellPackageUtil;
import com.zhihu.matisse.internal.entity.Item;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

/**
 * Created by three on 15/10/13.
 */
public class SendViewModel extends KeyboardListenerView implements EmojiView.DiceListener {

    public static final int TYPE_SINGLE_CHAT = 0;//单聊
    public static final int TYPE_GROUP_CHAT = 1;//群聊
    public static final int TYPE_GAME_ROOM = 2;//桌游房间
    public static final int TYPE_MARRY_ROOM = 3;//婚礼房间
    public static final int TYPE_FIX_ROOM = 4;//常驻房
    private int type = TYPE_SINGLE_CHAT;

    private static final String TAG = "SendViewModel";
    private final Context mContext;
    private ImageView vocIcon;
    private ImageView keyboardIcon;
    private ImageView faceIcon;
    private ImageView moreIcon;
    public EditText editText;
    private ChatRecorderView recorderView;
    private TextView sendBt;
    private View keyboardCoverView;
    private MoreViewAdapter viewAdapter;
    private ImageView inviteGameOrSendGiftIv;
    private SendEmoticonView emoticonView;
    private int keyboardHeight;
    private int defaultHeight;
    private RecyclerView moreLay;
    private String lastText = "";
    private MsgQuoteContentWithDeleteItem quoteLay;

    //外部参数
    private String rp_id = "";
    private int rpSkinId = -1;
    private String password = "";
    private TextWatcher textWatcher;

    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private int gid;
    private boolean isFamily = false;
    private String quoteMid = "";
    private String quoteType = "";

    private int receiverUid = 0;

    public SendViewModel(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public SendViewModel(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventBusExKt.registerAutoCancel(EventBus.getDefault(), ViewExKt.toLife(this), this);
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.send_view_model, this);
        vocIcon = findViewById(R.id.send_voc_icon);
        keyboardIcon = findViewById(R.id.send_keyboard_icon);
        faceIcon = findViewById(R.id.send_face_icon);
        moreIcon = findViewById(R.id.send_more_icon);
        editText = findViewById(R.id.send_edit_tx);
        recorderView = findViewById(R.id.send_record_view);
        sendBt = findViewById(R.id.send_tx_bt);
        keyboardCoverView = findViewById(R.id.send_keyboard_cover_view);
        inviteGameOrSendGiftIv = findViewById(R.id.send_invite_or_gift_icon);
        moreLay = findViewById(R.id.recycle_view);
        viewAdapter = new MoreViewAdapter(mContext);
        moreLay.setLayoutManager(new GridLayoutManager(mContext, 4));
        moreLay.setAdapter(viewAdapter);
        quoteLay = findViewById(R.id.quote_lay);
        quoteLay.setOnDelete(() -> {
            quoteMid = "";
            quoteType = "";
            if (msgSendListener != null) msgSendListener.onQuote(quoteMid);
            return null;
        });

        defaultHeight = (int) getResources().getDimension(R.dimen.keyboard_height);
        keyboardHeight = PrefUtil.getInstance().getInt(PrefConfig.KEY_BOARD_HEIGHT, defaultHeight);
        initEmotionView();

        editText.setOnClickListener(clickListener);
        vocIcon.setOnClickListener(clickListener);
        keyboardIcon.setOnClickListener(clickListener);
        faceIcon.setOnClickListener(clickListener);
        moreIcon.setOnClickListener(clickListener);
        sendBt.setOnClickListener(clickListener);
        inviteGameOrSendGiftIv.setOnClickListener(clickListener);
        moreLay.setOnClickListener(clickListener);
        viewAdapter.setCallback(itemId -> {
            if (itemId == SendViewConst.ITEM_PHOTO) {
                onPhotoClick();
                if (msgSendListener != null) msgSendListener.onScrollToBottom();
            } else if (itemId == SendViewConst.ITEM_CAMERA) {
                onCameraClick();
            } else if (itemId == SendViewConst.ITEM_RED_POCKET) {
                if (msgSendListener != null) msgSendListener.onSendRedPacketMsg();
                hideAll();
                if (msgSendListener != null) msgSendListener.onScrollToBottom();
            } else if (itemId == SendViewConst.ITEM_SECRET_PHOTO) {
                onSecretPhotoClick();
            } else if (itemId == SendViewConst.ITEM_ID_CARD) {
                if (msgSendListener != null) msgSendListener.onSendIdCard();
                hideAll();
                if (msgSendListener != null) msgSendListener.onScrollToBottom();
            } else if (itemId == SendViewConst.ITEM_SEND_GIFT) {
                onSendGiftClick();
                if (msgSendListener != null) msgSendListener.onScrollToBottom();
            } else if (itemId == SendViewConst.ITEM_FAMILY_BOX) {
                if (msgSendListener != null) msgSendListener.onSendFamilyBox();
                hideAll();
                if (msgSendListener != null) msgSendListener.onScrollToBottom();
            } else if (itemId == SendViewConst.ITEM_ALL_GIFT) {
                GiftAnimUtil.showGiftView(getContext(), GiftShowConfigHelper.allGroupGift(gid, isFamily),
                        info -> ChatPacketSenderNew.getInstance().sendGroupMultiGift(info, CareUtil.getGiftSendText(info.giftId, info.giftNum), new LifeSeqCallback(this) {
                            @Override
                            public void onSuccess(RspHeadInfo head) {
                            }

                            @Override
                            public void onFail(RspHeadInfo head) {
                                ToastUtil.show(head.desc);
                            }
                        }));
            } else if (itemId == SendViewConst.ITEM_VOICE_LINK) {
                if (msgSendListener != null) msgSendListener.onVoiceLink();
            } else if (itemId == SendViewConst.ITEM_INVITE_GAME) {
                onInviteGameClick();
            } else {
                ToastUtil.show(R.string.common_stay_tuned);
            }
        });

        PressUtil.addPressEffect(inviteGameOrSendGiftIv);
        textWatcher = new SimpleTextWatcher() {

            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i2, int i3) {
                lastText = charSequence.toString();
                EmojiHelper.addEmojiDeleteFlag(editText, charSequence, i, i2, i3);
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int start, int before, int count) {
                String text = charSequence.toString();
                checkShowSendBtn(!"".equals(text));

                if (text.equals(password)) RedPacketUtil.hidePasswordPop();

                if (type == TYPE_GAME_ROOM) {
                    // 房间中at
                    if (text.length() != 0 && lastText.length() < text.length()) {
                        if ("@".equals(text.substring(text.length() - 1))) {
                            if (msgSendListener != null) msgSendListener.onAtUser();
                        }
                    }
                } else if (type == TYPE_GROUP_CHAT) {
                    // 群聊中输入at
                    if (text.length() != 0 && lastText.length() < text.length()) {
                        // 只处理用户输入一个@的情况，如果复制的多个字符包含@则不处理
                        if ("@".equals(text.substring(start, start + 1)) && count == 1) {
                            if (msgSendListener != null) msgSendListener.onAtUser();
                        }
                    }
                    // 群聊中从at后开始删除
                    if (lastText.length() > text.length() && start > 0) {
                        int deleteCount = lastText.length() - text.length();
                        int startDeleteIndex = -1;
                        if ("\u2005".equals(lastText.substring(start, start + 1)) && deleteCount == 1) {
                            for (int i = start - 1; i >= 0; i--) {
                                char characeter = text.charAt(i);
                                if (characeter == '@') {
                                    startDeleteIndex = i;
                                    break;
                                }
                            }
                            if (startDeleteIndex >= 0) {
                                Editable editable = editText.getText();
                                editable.delete(startDeleteIndex, start);
                            }
                        }
                    }
                } else if (type == TYPE_FIX_ROOM) {
                    // 常驻房中输入at
                    if (text.length() != 0 && lastText.length() < text.length()) {
                        // 只处理用户输入一个@的情况，如果复制的多个字符包含@则不处理
                        if ("@".equals(text.substring(start, start + 1)) && count == 1) {
                            if (msgSendListener != null) msgSendListener.onAtUser();
                        }
                    }
                    // 常驻房中从at后开始删除
                    if (lastText.length() > text.length() && start > 0) {
                        int deleteCount = lastText.length() - text.length();
                        int startDeleteIndex = -1;
                        if ("\u2005".equals(lastText.substring(start, start + 1)) && deleteCount == 1) {
                            for (int i = start - 1; i >= 0; i--) {
                                char characeter = text.charAt(i);
                                if (characeter == '@') {
                                    startDeleteIndex = i;
                                    break;
                                }
                            }
                            if (startDeleteIndex >= 0) {
                                Editable editable = editText.getText();
                                editable.delete(startDeleteIndex, start);
                            }
                        }
                    }
                }
                EmojiHelper.deleteEmoji(editText, start);
            }

            @Override
            public void afterTextChanged(Editable editable) {
                super.afterTextChanged(editable);
                if (msgSendListener != null) {
                    msgSendListener.onDraft(editable.toString());
                }
            }
        };
        editText.addTextChangedListener(textWatcher);

        recorderView.setRecorderCallback(new ChatRecorderView.RecorderCallback() {
            @Override
            public void onRecordSuccess(String audioPath, int duration) {
                ChatRecorder.log("---MsgSendView onRecordSuccess---");
                if (msgSendListener != null)
                    msgSendListener.onSendAudioMsg(audioPath, Math.round(duration / 1000f));
            }

            @Override
            public void onRecordStateChanged(boolean begin) {
                if (msgSendListener != null) msgSendListener.onRecordStateChanged(begin);
            }
        });

        //初始状态
        hideAll();
        vocIcon.setVisibility(VISIBLE);
        keyboardIcon.setVisibility(GONE);
        sendBt.setVisibility(GONE);
        moreIcon.setVisibility(VISIBLE);
        recorderView.setVisibility(GONE);
        moreLay.setVisibility(GONE);
        emoticonView.setVisibility(GONE);
        if (ShellPackageUtil.hideInviteGame()) {
            inviteGameOrSendGiftIv.setVisibility(GONE);
        } else {
            inviteGameOrSendGiftIv.setVisibility(VISIBLE);
        }
        showSingleChatItem();
    }

    public void setNeedDice(boolean needDice) {
        emoticonView.setNeedDice(needDice);
    }

    public void initDraft(CharSequence draft) {
        // 草稿输入过来不需要@操作
        editText.removeTextChangedListener(textWatcher);
        editText.setText(draft);
        editText.addTextChangedListener(textWatcher);
    }

    private void initEmotionView() {
        emoticonView = findViewById(R.id.emotion_lay);
        emoticonView.update(editText, this, (item, index) -> {
            if (item.isEmoticon()) {
                if (msgSendListener != null) {
                    msgSendListener.onSendEmoticon(item);
                }
            } else if (item.isAdd()) {
                JumpUtil.gotoEmoticonFavoriteActivity(getContext());
            }
        });
        updateCoverHeight(false);
    }

    private void updateCoverHeight(boolean isKeyboardShow) {
        int h = keyboardHeight;
        ViewGroup.LayoutParams emojiParams = emoticonView.getLayoutParams();
        emojiParams.height = h;
        emoticonView.setLayoutParams(emojiParams);

        ViewGroup.LayoutParams moreParam = moreLay.getLayoutParams();
        moreParam.height = h;
        moreLay.setLayoutParams(moreParam);

        ViewGroup.LayoutParams coverParam = keyboardCoverView.getLayoutParams();
        coverParam.height = h;
        keyboardCoverView.setLayoutParams(coverParam);
    }

    private void showEmoticon() {
        RedPacketUtil.hidePasswordPop();
        if (msgSendListener != null) msgSendListener.onShowEmoji(true);
        emoticonView.setVisibility(View.VISIBLE);
        emoticonView.updateLastEmoji();
        faceIcon.setImageResource(R.drawable.ic_send_keyboard);
        hideMoreLay();
        onClickKeyboardIcon();
    }

    public void hideEmoticon() {
        emoticonView.setVisibility(View.GONE);
        faceIcon.setImageResource(R.drawable.ic_send_face);
        if (msgSendListener != null) msgSendListener.onShowEmoji(false);
    }

    /**
     * 设置显示类型
     *
     * @param type 聊天类型
     */
    public void setType(int type) {
        this.type = type;
        if (type == SendViewModel.TYPE_SINGLE_CHAT && !UserService.get().isSystemUser(receiverUid)) {
            inviteGameOrSendGiftIv.setImageResource(R.drawable.ic_send_gift);
        } else {
            inviteGameOrSendGiftIv.setImageResource(R.drawable.ic_send_game);
        }
    }

    /**
     * 设置接收人,用户判断是否是特定人群
     *
     * @param receiverUid 群聊不需要接收人,直接传0即可
     */
    public void setReceiverUid(int receiverUid) {
        this.receiverUid = receiverUid;
    }

    private boolean isGroupChat() {
        return type == SendViewModel.TYPE_GROUP_CHAT;
    }

    public void showGroupChatItem() {
        setType(SendViewModel.TYPE_GROUP_CHAT);
        viewAdapter.setDataList(SendViewConst.groupChatList);
    }

    public void showGameRoomItem() {
        setType(SendViewModel.TYPE_GAME_ROOM);
        inviteGameOrSendGiftIv.setVisibility(View.GONE);
        viewAdapter.setDataList(SendViewConst.gameRoomList);
        emoticonView.onlyShowEmoji();
    }

    public void showFixRoomItem() {
        setType(SendViewModel.TYPE_FIX_ROOM);
        inviteGameOrSendGiftIv.setVisibility(View.GONE);
        viewAdapter.setDataList(SendViewConst.fixRoomList);
        //emoticonView.onlyShowEmoji();
    }

    public void showSystemUserSingleChatItem() {
        setType(SendViewModel.TYPE_SINGLE_CHAT);
        viewAdapter.setDataList(SendViewConst.singleChatSystemUserList);
    }

    public void showSingleChatItem() {
        setType(SendViewModel.TYPE_SINGLE_CHAT);
        viewAdapter.setDataList(SendViewConst.singleChatList);
    }

    public void showMyselfSingleChatItem() {
        setType(SendViewModel.TYPE_SINGLE_CHAT);
        viewAdapter.setDataList(SendViewConst.singleChatMyselfList);
    }

    public void showChattingHuaweiSingleChatItem() {
        setType(SendViewModel.TYPE_SINGLE_CHAT);
        viewAdapter.setDataList(SendViewConst.singleChatChattingHuawei);
    }

    public void showFamilyGroupChatItem() {
        setType(SendViewModel.TYPE_GROUP_CHAT);
        viewAdapter.setDataList(SendViewConst.familyGroupChatList);
    }

    public void setIsFamily(boolean isFamily) {
        this.isFamily = isFamily;
    }

    @Override
    public void keyBoardShow(boolean firstTimeShow, boolean isShowCoverLay) {
        if (firstTimeShow) {
            setSendViewExtraHeight(getKeyBoardHeight());
        }
        updateCoverHeight(true);

        hideMoreLay();
        hideEmoticon();
        if (isShowCoverLay) {
            showCoverView();
        }
        if (msgSendListener != null) msgSendListener.onScrollToBottom();
        RedPacketUtil.update(this, true);
    }

    @Override
    public void keyBoardHide() {
        if (hideConver) {
            hideCoverView();
        }
        updateCoverHeight(false);
        hideConver = true;
        RedPacketUtil.update(this, false);
    }

    @Override
    public void keyBoardHeightChange(int height) {
        super.keyBoardHeightChange(height);
        setSendViewExtraHeight(height);
    }

    //设置与键盘等高的一个view高度
    private void setSendViewExtraHeight(int height) {
        keyboardHeight = height;
        PrefUtil.getInstance().setInt(PrefConfig.KEY_BOARD_HEIGHT, keyboardHeight);
    }

    private long clickDiceTime = 0;

    @Override
    public void onClickDice() {
        long curTime = System.currentTimeMillis();
        if (curTime - clickDiceTime < 1000) {
            ToastUtil.show(R.string.send_view_too_frequent);
            return;
        }
        clickDiceTime = curTime;
        if (msgSendListener != null) msgSendListener.onSendDiceMsg();
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == ActivityResultCode.AT_ROOM_MSG_RESULT_CODE) {
            int uid = data.getIntExtra(InviteFriendActivity.INVITE_ACTIVITY_AT_UID, -1);
            if (uid <= 0) return;
            UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
                @Override
                public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                    addAtUser(userInfo);
                }

                @Override
                public void onUserInfoFailed(String description) {

                }
            });
        } else if (requestCode == ActivityResultCode.HEAD_IMG_BY_PHOTO_SCRIBBLE && resultCode == Activity.RESULT_OK) {//私密图片，涂鸦之后
            String path = data.getStringExtra(ScribbleActivity.SCRIBBLE_IMAGE_PATH);
            //int age = data.getIntExtra(ScribbleActivity.SCRIBBLE_IMAGE_AGE, 10);
            if (path == null || !FileUtil.fileExists(path)) {
                ToastUtil.show(R.string.common_image_fail);
            } else {
                int age = data.getIntExtra(ScribbleActivity.SCRIBBLE_IMAGE_AGE, 10);
                if (msgSendListener != null) {
                    msgSendListener.onSendPhotoMsg(path, age);
                }
            }
        }
    }

    public void showCoverView() {
        keyboardCoverView.setVisibility(VISIBLE);
    }

    public void hideCoverView() {
        keyboardCoverView.setVisibility(GONE);
    }

    public void showSoftInput() {
        editText.setFocusable(true);
        editText.requestFocus();
        IMMHelper.showSoftInput(editText, mContext);
    }

    public void hideAll() {
        hideEmoticon();
        hideMoreLay();
        hideSoftInput();
        hideCoverView();
    }

    public boolean isEmoticonShowing() {
        return emoticonView.isShown();
    }

    public boolean isMoreShowing() {
        return moreLay.isShown();
    }

    public void hideMoreLay() {
        moreLay.setVisibility(View.GONE);

        if (moreIcon != null) {
            moreIcon.setImageResource(R.drawable.ic_send_more);
        }
    }

    private void hideMoreDelay() {
        ViewExKt.postAutoCancel(this, 500, () -> {
            hideMoreLay();
            if (msgSendListener != null) msgSendListener.onScrollToBottom();
        });
    }

    public void showMoreLay() {
        moreLay.setVisibility(VISIBLE);

        if (moreIcon != null) {
            moreIcon.setImageResource(R.drawable.ic_send_more_close);
        }
    }

    public void hideSoftInput() {
        IMMHelper.hideSoftInput(mContext, getWindowToken());
    }

    private boolean hideConver = true;

    //用于键盘,emoji, 更多切换
    public void hideSoftShowConver() {
        hideConver = false;
        hideSoftInput();
    }

    private final Handler sendBtnHandler = new Handler(Looper.getMainLooper());

    private void checkShowSendBtn(boolean isShow) {
        if (type == TYPE_GAME_ROOM) {
            resetSendView(isShow);
        } else {
            sendBtnHandler.removeCallbacksAndMessages(null);
            sendBtnHandler.post(() -> showSendBt(isShow));
        }
    }

    private ValueAnimator sendAnimator;

    private void showSendBt(boolean isShow) {
        if (isShow == sendBt.isShown()) {
            return;
        }
        if (sendAnimator != null) {
            sendAnimator.cancel();
        }
        if (sendBt.isShown()) {
            moreIcon.setAlpha(0f);
        } else {
            sendBt.setAlpha(0f);
            sendBt.getLayoutParams().width = ScreenUtil.dip2px(30);
        }
        moreIcon.setVisibility(VISIBLE);
        sendBt.setVisibility(VISIBLE);
        sendAnimator = ValueAnimator.ofFloat(0, 1);
        sendAnimator.setDuration(200);
        sendAnimator.addUpdateListener((animation) -> {
            float fraction = animation.getAnimatedFraction();
            int width;
            if (isShow) {
                sendBt.setAlpha(fraction);
                moreIcon.setAlpha(1 - fraction);
                width = ScreenUtil.dip2px(30 + 26 * fraction);
            } else {
                sendBt.setAlpha(1 - fraction);
                moreIcon.setAlpha(fraction);
                width = ScreenUtil.dip2px(56 - 26 * fraction);
            }
            sendBt.getLayoutParams().width = width;
            sendBt.requestLayout();
        });
        sendAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                resetSendView(isShow);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                resetSendView(isShow);
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        sendAnimator.start();
    }

    private void resetSendView(boolean isShow) {
        sendBt.setVisibility(isShow ? View.VISIBLE : View.GONE);
        moreIcon.setVisibility(isShow ? View.GONE : View.VISIBLE);
        sendBt.setAlpha(1.0f);
        moreIcon.setAlpha(1.0f);
        if (isShow) {
            sendBt.getLayoutParams().width = ScreenUtil.dip2px(56);
        }
    }

    private void onClickKeyboardIcon() {
        vocIcon.setImageResource(R.drawable.ic_send_voice);
        vocIcon.setVisibility(View.VISIBLE);
        editText.setVisibility(View.VISIBLE);
        keyboardIcon.setVisibility(View.GONE);
        recorderView.setVisibility(View.GONE);
    }

    public void doAtUser(User user) {
        showSoftInput();
        hideEmoticon();
        hideMoreLay();
        editText.setText("@" + user.getNickname() + " ");
        editText.setSelection(editText.getText().length());
    }

    public void addAtUser(UserSimpleInfo user) {
        showSoftInput();
        hideEmoticon();
        hideMoreLay();
        String text = editText.getText().toString();
        editText.setText(text + user.getNickname() + " ");
        editText.setSelection(editText.getText().length());
    }

    public void addAtGroupUser(int uid, String nickName) {
        int index = editText.getSelectionStart();
        Editable editable = editText.getText();
        editable.insert(index, nickName + "\u2005");

        mainHandler.postDelayed(() -> {
            onClickKeyboardIcon();
            showSoftInput();
        }, 200);
        CurrentChatHelper.addAtMsg(uid, nickName);
    }

    public void addAtGroupUserWithAt(int uid, String nickName) {
        int index = editText.getSelectionStart();
        Editable editable = editText.getText();
        editable.insert(index, "@" + nickName + "\u2005");

        onClickKeyboardIcon();
        CurrentChatHelper.addAtMsg(uid, nickName);
    }

    public void clearEditFocus() {
        editText.clearFocus();
    }

    public void setSilentMode(String msg) {
        vocIcon.setVisibility(View.VISIBLE);
        editText.setVisibility(View.VISIBLE);
        keyboardIcon.setVisibility(View.GONE);
        recorderView.setVisibility(View.GONE);

        hideAll();

        editText.setText("");
        editText.setHint(msg);
        editText.setCursorVisible(false);
        editText.setGravity(Gravity.CENTER);
        editText.setEnabled(false);
        vocIcon.setEnabled(false);
        faceIcon.setEnabled(false);
        moreIcon.setEnabled(false);
    }

    public void refreshDefaultHint(boolean inputAnswer) {
        if (inputAnswer) {
            editText.setHint(ResUtil.getStr(R.string.send_view_input_answer));
        } else {
            editText.setHint(ResUtil.getStr(R.string.send_view_input_chat_content));
        }
    }

    private final OnClickListener clickListener = new OnClickListener() {
        @Override
        public void onClick(View v) {
            if (v == vocIcon) {
                hideAll();
                vocIcon.setVisibility(View.GONE);
                editText.setVisibility(View.GONE);
                keyboardIcon.setVisibility(View.VISIBLE);
                recorderView.setVisibility(View.VISIBLE);
            } else if (v == keyboardIcon) {
                onClickKeyboardIcon();
                showSoftInput();
            } else if (v == faceIcon) {
                if (isEmoticonShowing()) {
                    showSoftInput();
                    hideEmoticon();
                    hideMoreLay();
                } else {
                    if (isKeyBoardShow()) {
                        hideSoftShowConver();
                        //直接show会被顶到顶上，延迟缓解一下闪动
                        ViewExKt.postAutoCancel(SendViewModel.this, 200, () -> {
                            showEmoticon();
                            if (msgSendListener != null) msgSendListener.onScrollToBottom();
                        });
                    } else {
                        showEmoticon();
                        if (msgSendListener != null) msgSendListener.onScrollToBottom();
                    }
                }
            } else if (v == moreIcon) {
                RedPacketUtil.hidePasswordPop();
                if (moreLay.isShown()) {
                    hideAll();
                } else {
                    if (isKeyBoardShow()) {
                        hideSoftShowConver();
                        ViewExKt.postAutoCancel(SendViewModel.this, 200, () -> showMoreLay());
                    } else {
                        showMoreLay();
                    }
                }
                onClickKeyboardIcon();
                hideEmoticon();
                if (msgSendListener != null) msgSendListener.onScrollToBottom();
            } else if (v == editText) {
                showSoftInput();
                hideEmoticon();
                hideMoreLay();
            } else if (v == sendBt) {
                String chatContent = editText.getText().toString();
                if (isGroupChat()) {
                    if (chatContent.length() > 1000) {
                        ToastUtil.show(R.string.send_view_message_too_long);
                        return;
                    }
                }
                editText.setText("");//清空输入框
                if (msgSendListener != null)
                    msgSendListener.onSendTextMsg(chatContent, quoteMid, quoteType);
                quoteLay.clearQuote();
                if (msgSendListener != null) {
                    if (TextUtil.isEmpty(rp_id)) {
                        RedPacket ungrabPacketByPassword = RedPacketManager.getInstance().getUngrabPacketByPassword(chatContent);
                        rp_id = ungrabPacketByPassword.getRp_id();
                        rpSkinId = ungrabPacketByPassword.getRp_skin_id();
                    }
                    if (TextUtil.isEmpty(rp_id)) {
                        return;
                    }
                    msgSendListener.onSendPasswordPacketMsg(chatContent, rp_id, rpSkinId);
                }
                if (chatContent.equals(password)) RedPacketUtil.hidePasswordPop();
                rp_id = "";
            } else if (v == inviteGameOrSendGiftIv) {
                // 如果是单聊，走送礼物逻辑，否则走原来的游戏邀请逻辑
                if (type == SendViewModel.TYPE_SINGLE_CHAT && !UserService.get().isSystemUser(receiverUid)) {
                    onSendGiftClick();
                } else {
                    onInviteGameClick();
                }
            }
        }
    };

    public void unsetDrawGuessSilentMode(boolean selfIsInGame) {
        refreshDefaultHint(selfIsInGame);
        editText.setCursorVisible(true);
        editText.setGravity(Gravity.START);
        editText.setEnabled(true);
        vocIcon.setEnabled(true);
        faceIcon.setEnabled(true);
        moreIcon.setEnabled(true);
        sendBt.setEnabled(true);
        sendBt.setEnabled(true);
    }

    public void showEditText() {
        vocIcon.setVisibility(View.VISIBLE);
        editText.setVisibility(View.VISIBLE);
        keyboardIcon.setVisibility(View.GONE);
        recorderView.setVisibility(View.GONE);
    }

    public void setRp_id(String rp_id) {
        this.rp_id = rp_id;
    }

    public void setRpSkinId(int rpSkinId) {
        this.rpSkinId = rpSkinId;
    }


    public void setPassword(String password) {
        this.password = password;
    }

    public void setPasswordText(String password, String rp_id) {
        this.rp_id = rp_id;
        this.password = password;
        vocIcon.setVisibility(View.VISIBLE);
        editText.setVisibility(View.VISIBLE);
        keyboardIcon.setVisibility(View.GONE);
        recorderView.setVisibility(View.GONE);

        editText.setText(password);
        editText.setGravity(Gravity.START);
        editText.setSelection(password.length());
    }

    public void setGroupId(int gid) {
        this.gid = gid;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        RedPacketUtil.hidePasswordPop();
    }

    private MessageSendListener msgSendListener;

    public void registerMessageSendListener(MessageSendListener listener) {
        this.msgSendListener = listener;
    }

    public void doQuote(ChatMsg chatMsg) {
        this.quoteMid = chatMsg.getQuoteId();
        quoteLay.showQuoteContent(chatMsg);
        if (!TextUtil.isEmpty(quoteMid)) {
            showSoftInput();
        }
    }

    public void doQuote(WPMessage wpMessage) {
        this.quoteMid = wpMessage.getMid();
        quoteLay.showQuoteContent(wpMessage);
        if (!TextUtil.isEmpty(quoteMid)) {
            showSoftInput();
        }
    }

    public interface MessageSendListener {
        void onSendTextMsg(String content, String quoteId, String quoteType);

        void onSendPasswordPacketMsg(String content, String rp_id, int rpSkinId);

        void onSendAudioMsg(String audioPath, int duration);

        void onSendPhotoMsg(String photoPath, int age);

        void onSendNormalPhotoMsg(List<Item> items, boolean origin, boolean isCamera);

        void onReSendNormalPhotoMsg(String photoPath);

        void onSendDiceMsg();

        void onScrollToBottom();

        void onSendRedPacketMsg();

        void onShowEmoji(boolean isShow);

        void onSendIdCard();

        void onSendGift();

        void onSendGameInvite();

        void onAtUser();

        void onSendFamilyBox();

        void onDraft(String draft);

        default void onVoiceLink() {
        }

        default void onSendEmoticon(EmoticonItem item) {
        }

        default void onRecordStateChanged(boolean begin) {
        }

        default void onSendVideoMsg(String content) {
        }

        void onQuote(String quoteMid);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onDeleteMsg(DeleteMsgEvent event) {
        handleQuote(event.msg.getMid());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onDeleteMsg(SingleChatDeleteMsgEvent event) {
        handleQuote(event.msg.getMid());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRecallMsg(RecallMsgEvent event) {
        boolean canHandle = handleQuote(event.msg.getMid());
        if (canHandle && event.msg.getSend_uid() != LoginHelper.getLoginUid()) { // 对方撤回
            QuoteMsgDeleteDialog.Companion.show(mContext, QuoteMsgDeleteDialog.Type.RECALL.INSTANCE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRecallMsg(SingleChatRecallMsgEvent event) {
        boolean canHandle = handleQuote(event.msg.getMid());
        if (canHandle && event.msg.getSend_uid() != LoginHelper.getLoginUid()) { // 对方撤回
            QuoteMsgDeleteDialog.Companion.show(mContext, QuoteMsgDeleteDialog.Type.RECALL.INSTANCE);
        }
    }

    /**
     * 处理消息撤回及删除事件
     *
     * @param mid
     * @return 是否应该处理
     */
    private boolean handleQuote(String mid) {
        if (!TextUtils.equals(mid, quoteLay.getMid())) {
            return false;
        }
        quoteLay.clearQuote();
        return true;
    }

    /**
     * 获取选择照片的时候的场景,scene = “单聊”、“群聊”、“家族群聊”
     *
     * @return
     */
    public String getPhotoSceneName() {
        String scene = "";
        if (gid > 0) {
            GroupInfo groupInfo = GroupService.getInstance().getGroupInfo(gid);
            scene = groupInfo.isFamilyGroup() ? TrackSource.FAMILY_GROUP_CHAT_2 : TrackSource.GROUP_CHAT;
        } else {
            scene = TrackSource.SINGLE_CHAT;
        }
        return scene;
    }

    private boolean captureVideoImages() {
        return type == TYPE_SINGLE_CHAT || type == TYPE_GROUP_CHAT;
    }

    private void onPhotoClick() {
        MediaChooseLimit mediaChooseLimit = ConfigHelper.getInstance().getConstConfig().getMsgFileUploadLimit();
        mediaChooseLimit.setEnableOrigin(true);
        mediaChooseLimit.setSupportAllMedias(captureVideoImages());
        HLog.d(TAG, HLog.USR, "onPhotoClick!");
        PhotoUtil.showPhotoChooseDialog(mContext, mediaChooseLimit, getPhotoSceneName(), new WPChoosePhotoDialog.Callback() {
            @Override
            public void onFinish(List<Item> items, boolean origin) {
                if (items != null && msgSendListener != null && !items.isEmpty()) {
                    msgSendListener.onSendNormalPhotoMsg(items, origin, false);
                }
            }

            @Override
            public boolean onCameraClick() {
                SendViewModel.this.onCameraClick();
                return true;
            }
        });
    }

    private void onSecretPhotoClick() {
        MediaChooseLimit mediaChooseLimit = ConfigHelper.getInstance().getConstConfig().getMsgFileUploadLimit();
        mediaChooseLimit.setMaxSelect(1);
        HLog.d(TAG, HLog.USR, "onSecretPhotoClick!");
        PhotoUtil.showPhotoChooseDialog(mContext, mediaChooseLimit, getPhotoSceneName(), new WPChoosePhotoDialog.Callback() {
            @Override
            public void onFinish(List<Item> items, boolean origin) {
                if (items == null || items.isEmpty()) return;
                String path = items.get(0).path;
                ScribbleActivity.enterScribbleActivity((Activity) mContext, path);
            }
        });
        hideAll();
    }

    private void onCameraClick() {
        if (UserGameStatusUtil.notUseCamera((Activity) mContext)) {
            PhotoUtil.launchCamera((Activity) mContext, -1, new PhotoCallback() {
                @Override
                public void onFinish(List<Item> items) {
                    if (items != null && msgSendListener != null) {
                        msgSendListener.onSendNormalPhotoMsg(items, true, true);
                    }
                }
            }, new PhotoUtil.CameraConfig(PhotoUtil.CameraConfig.CAPTURE_PICTURE));
        }
        hideAll();
        if (msgSendListener != null) msgSendListener.onScrollToBottom();
    }

    private void onSendGiftClick() {
        if (msgSendListener != null) msgSendListener.onSendGift();
        hideAll();
    }

    private void onInviteGameClick() {
        if (UserGameStatusUtil.checkUserStatus(mContext, UserGameStatusUtil.TYPE_LITTLE_GAME_INVITE)) {
            return;
        }
        if (msgSendListener != null) msgSendListener.onSendGameInvite();
        hideAll();
    }
}
