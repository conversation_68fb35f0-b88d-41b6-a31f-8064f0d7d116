package com.wepie.wespy.base;

import android.os.SystemClock;
import android.text.format.DateUtils;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.common.GlobalConfig;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.VoiceRoomConfig;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.huiwan.voiceservice.VoiceManager;
import com.wejoy.weplay.ex.GlobalLife;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.voice.BaseVoiceManager;
import com.wepie.lib.api.plugins.voice.Config;
import com.wepie.lib.api.plugins.voice.IVoiceApi;
import com.wepie.lib.api.plugins.voice.VoiceConfig;
import com.wepie.liblog.main.HLog;
import com.wepie.service.voice.trt.TRTManager;
import com.wepie.service.voice.zego.ZegoManager;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.JoinChannelEvent;
import com.wepie.wespy.module.media.VoiceConfigHelper;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.music.VoiceMusicManager;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import java.util.HashMap;
import java.util.Map;

public class VoiceApiOsImpl implements IVoiceApi {
    private static final String TAG = "VoiceApiOsImpl";

    @Override
    public BaseVoiceManager getVoiceManagerByType(int serviceType, int mediaType) {
        HLog.d(TAG, HLog.USR, "getVoiceManagerByType! serviceType={},mediaType={}", serviceType, mediaType);
        if (serviceType == VoiceConfig.SERVICE_ZEGO) {
            return ZegoManager.get(mediaType);
        } else if (serviceType == VoiceConfig.SERVICE_TRT) {
            return TRTManager.get(mediaType);
        }
        return ZegoManager.get(mediaType);
    }

    @Override
    public BaseVoiceManager getDefaultVoiceManager() {
        HLog.d(TAG, HLog.USR, "getDefaultVoiceManager!");
        return ZegoManager.get(VoiceConfig.MEDIA_AUDIO);
    }

    @Override
    public int getDefaultVoiceType() {
        return VoiceConfig.SERVICE_ZEGO;
    }

    @Override
    public Config getDefaultConfig() {
        return ConfigHelper.getInstance().getVoiceConfigs().getDefaultConfig();
    }

    @Override
    public boolean joinChannel(int voiceType, boolean highQuality, String rid, boolean openMic, boolean isBroadcaster) {
        if (UrlConfig.isDebug()) {
            rid = VoiceManager.VOICE_DEV + rid;
        }
        VoiceConfig config = VoiceConfigHelper.buildVoiceConfig(voiceType, highQuality, rid, openMic, isBroadcaster);
        HLog.d(TAG, HLog.USR, "joinChannel! voiceType=" + voiceType + ", highQuality=" + highQuality + ", rid=" + rid + ", openMic=" + openMic + ", isBroadcaster=" + isBroadcaster);
        return VoiceManager.getInstance().joinChannelRes(config) == BaseVoiceManager.JOIN_RES_SUCCESS;
    }

    @Override
    public GlobalConfig.VoiceKInfo getVoiceKInfo() {
        return GlobalConfigManager.getInstance().getVoiceConfig();
    }

    @Override
    public boolean isSupportVoiceType(int type) {
        return type == VoiceConfig.SERVICE_ZEGO || type == VoiceConfig.SERVICE_TRT;
    }

    private long lastSpeakTime = 0;
    /**
     * 此次事件首次说话的时间
     */
    private long trackFirstSpeakTime = 0;
    private long trackLastSpeakTime = 0;


    /**
     * 最后说话超过5分钟时，打点记录
     */
    @Override
    public void checkTrackHangDuration() {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        if (roomInfo.closeVoiceChannel) {
            // 这里只处理离开频道的情况。
            // 在频道里的时候，由音浪回调处理
            long first = trackFirstSpeakTime;
            long last = trackLastSpeakTime;
            if (first != 0 && last != 0) {
                long now = TimeUtil.getElapsedServerTime();
                long duration = now - last;
                if (duration > DateUtils.SECOND_IN_MILLIS * 5) {
                    // 在自己触发请求的时候，这里的时间
                    trackTime(first, duration, duration, "check");
                }
            }
            trackFirstSpeakTime = 0;
            trackLastSpeakTime = 0;
        }
    }

    /**
     * 处理长时间无人讲话埋点
     */
    private void checkTrackLongTimeNoUserSpeak(VoiceRoomInfo roomInfo, int minCloseVolume, long gapTime, int volume) {
        if (roomInfo.rid > 0 && roomInfo.isSupportVoiceRoom()) {
            if (roomInfo.closeVoiceChannel) {
                trackFirstSpeakTime = 0;
                trackLastSpeakTime = 0;
                HLog.d(TAG, "on close channel");
            } else {
                if (volume > minCloseVolume) {
                    trackLastSpeakTime = TimeUtil.getElapsedServerTime();
                    trackFirstSpeakTime = trackLastSpeakTime;
                    HLog.d(TAG, "speak time update");
                } else {
                    if (trackLastSpeakTime == 0) {
                        // 未说过话，按照进房时间进行计时
                        trackLastSpeakTime = VoiceRoomService.getInstance().getEnterRoomTime();
                        trackFirstSpeakTime = trackLastSpeakTime;
                    }
                    long lastOpenTime = VoiceRoomService.getInstance().getVoiceChannelOpenTime();
                    if (trackLastSpeakTime < lastOpenTime) {
                        trackLastSpeakTime = lastOpenTime;
                    }
                    long now = TimeUtil.getElapsedServerTime();
                    long duration = now - trackLastSpeakTime;
                    if (duration < gapTime) {
                        return;
                    }
                    trackTime(trackFirstSpeakTime, duration, gapTime, "NoSpeak");
                    trackLastSpeakTime = TimeUtil.getElapsedServerTime();
                }
            }
        } else {
            trackLastSpeakTime = 0;
            trackFirstSpeakTime = 0;
        }
    }

    private void trackTime(long startTime, long durationMs, long trackTime, String logTag) {
        if (trackTime > 0 && durationMs / trackTime > 2) {
            HLog.aliLog(AliNetLogUtil.PORT.base, AliNetLogUtil.TYPE.warning, "voice_track_time warning "
                    + startTime + " " + durationMs + " " + trackTime + " " + logTag
            );
            return;
        }
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        VoiceMusicInfo.PlayInfo playInfo = VoiceMusicManager.get().getCurPlayInfo();
        Map<String, Object> map = new HashMap<>();
        map.put("game_type", roomInfo.game_type);
        map.put("rid", roomInfo.rid);
        map.put("event_duration", durationMs / 1000);
        map.put("start_time", startTime);
        map.put("voice_status", roomInfo.inMusic && playInfo.isPlaying ? 1 : 0);
        HLog.d(TAG, HLog.USR, "track time {}, {}, {}", logTag, durationMs, map);
        TrackUtil.trackEvent("HangDuration", map);
    }

    /**
     * 长时间无人讲话时，尝试退出频道
     */
    private void checkHandleLongTimeNoUserSpeak(VoiceRoomInfo roomInfo, int minCloseVolume, long gapTime, int volume) {
        if (roomInfo.bgMusicId > 0) {
            return;
        }
        if (roomInfo.isVideoRoom()) {
            return;
        }
        if (roomInfo.isWeddingRoom()) {
            return;
        }
        if (roomInfo.isVoiceGameRoom()) {
            return;
        }
        //TODO 阿语服没有ktv ,但是未来上了ktv的话，需要在这里加上
        if (roomInfo.inMusic) {
            VoiceMusicInfo.PlayInfo cur = VoiceMusicManager.get().getCurPlayInfo();
            if (cur.isPlaying) {
                return;
            }
        }
        if (!roomInfo.isSelfAdminOrOwner()) {
            return;
        }
        if (roomInfo.isSelfAdmin() && roomInfo.isOwnerOnline()) {
            return;
        }
        if (roomInfo.rid > 0 && roomInfo.isSupportVoiceRoom()) {
            if (minCloseVolume <= 0) {
                return;
            }
            if (volume > minCloseVolume) {
                // 这里的值是测试的经验值。
                // 超过改值，则认为有人在说话
                // 正常可以走到改分支，但在里面会被屏蔽掉
                lastSpeakTime = TimeUtil.getElapsedServerTime();
                checkReqToChangeChannelStatus(roomInfo, false);
            } else {
                if (lastSpeakTime == 0) {
                    // 未说过话，按照进房时间进行计时
                    lastSpeakTime = VoiceRoomService.getInstance().getEnterRoomTime();
                }
                long lastOpenTime = VoiceRoomService.getInstance().getVoiceChannelOpenTime();
                if (lastSpeakTime < lastOpenTime) {
                    lastSpeakTime = lastOpenTime;
                }
                long now = TimeUtil.getElapsedServerTime();
                long duration = now - lastSpeakTime;
                if (duration < gapTime) {
                    return;
                }
                checkReqToChangeChannelStatus(roomInfo, true);
            }
        } else {
            lastSpeakTime = 0;
        }
    }

    /**
     * 上次请求时间。
     * 处理由于语音频道加入退出的延时
     * 导致检测异常，请求频繁
     */
    private long lastCheckChangeTime = SystemClock.elapsedRealtime();

    /**
     * 最快请求一次的时间间隔
     * 处理由于语音频道加入退出的延时或是灰度时不支持开启
     * 导致检测异常，请求频繁
     */
    private static final long REQ_CHANNEL_INTERVAL = DateUtils.MINUTE_IN_MILLIS;

    /**
     * 请求调整语音频道开关状态
     *
     * @param close 关闭语音频道
     */
    private void checkReqToChangeChannelStatus(VoiceRoomInfo roomInfo, boolean close) {
        if (roomInfo.closeVoiceChannel == close) {
            return;
        }
        long now = SystemClock.elapsedRealtime();
        long diff = now - lastCheckChangeTime;
        if (diff < REQ_CHANNEL_INTERVAL) {
            HLog.d(TAG, "req change need wait: {}", REQ_CHANNEL_INTERVAL - diff);
            return;
        }
        lastCheckChangeTime = now;
        HLog.d(TAG, HLog.USR, "req change voice channel status to close:" + close);
        VoiceRoomPacketSender.changeVoiceChannelReq(roomInfo.rid, close, new LifeSeqCallback(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                HLog.d(TAG, HLog.USR, "req change voice channel status to close:" + close + ", success");
            }

            @Override
            public void onFail(RspHeadInfo head) {
                HLog.d(TAG, HLog.USR, "req change voice channel status to close:" + close + ", failed " + head.code + " " + head.desc);
            }
        });
    }


    @Override
    public void onUserSpeakVolume(String tag, int volume) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        VoiceRoomConfig roomConfig = ConfigHelper.getInstance().getVoiceRoomConfig();
        if (LibBaseUtil.buildDebug()) {
            HLog.d(TAG, "volume: {}, rid:{}, close: {}", volume, roomInfo.rid, roomInfo.closeVoiceChannel);
        }
        if (roomInfo.rid > 0) {
            int gapVolume = roomConfig.minCloseVolume;
            if (roomInfo.closeVoiceChannel) {
                if (volume > gapVolume) {
                    VoiceRoomInfo.SeatInfo seatInfo = roomInfo.getSeatInfoByUid(LoginHelper.getLoginUid());
                    if (seatInfo != null && seatInfo.can_speak && VoiceManager.getInstance().isLocalMicOn()) {
                        HLog.d(TAG, HLog.USR, "volume check reqOpen {}", volume);
                        VoiceCheckerKt.reqOpen();
                    } else {
                        HLog.d(TAG, HLog.USR, "volume check no open {}", volume);
                    }
                }
            } else {
                long gapTime = ((long) roomConfig.closeVoiceChannelTimeoutSec * 1000);
                checkHandleLongTimeNoUserSpeak(roomInfo, gapVolume, gapTime, volume);
                checkTrackLongTimeNoUserSpeak(roomInfo, gapVolume, gapTime, volume);
            }
        } else {
            VoiceManager.getInstance().stopRecordLocalAudio();
        }
    }

    /**
     * 用户手动开麦时，直接进入频道
     */
    @Override
    public void onMicOpen() {
        VoiceCheckerKt.leaveCheck();
        lastSpeakTime = TimeUtil.getElapsedServerTime();
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        if (roomInfo.closeVoiceChannel) {
            HLog.d(TAG, HLog.USR, "reqOpen onMicOpen");
            VoiceCheckerKt.reqOpen();
            EventDispatcher.postJoinChannel(roomInfo, true, JoinChannelEvent.SRC_FROM_CLOSE_CHANNEL, false);
        }
    }
}
