package com.wepie.wespy.base.call

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.util.FileUtil
import com.wejoy.weplay.helper.life.base.LifeCallBack
import com.wejoy.weplay.helper.life.base.LifeRegister
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.ShareIconUtil
import com.wepie.libimageloader.WpImageLoaderUtil.url2LocalPath
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by lsy on 2022/8/25.
 */

class DownLoadImg(life: LifeRegister, val url: String, val info: ImageLoadInfo) :
    LifeCallBack(life) {
    val TAG = "PreDownLoadImg"
    var result: Bitmap? = null

    private fun getBitmap(path: String): Bitmap? {
        val file = File(path)
        if (FileUtil.isValid(file)) {
            val decodeFile = BitmapFactory.decodeFile(file.absolutePath) ?: return null
            if (decodeFile.width > 0 && decodeFile.height > 0) {
                return decodeFile
            }
        }
        return null
    }

    override fun getData(callback: ILifeCallBack) {
        super.getData(callback)
        CoroutineScope(Dispatchers.IO).launch {
            //历史custom cache file 存在，则还是使用customCacheFile 逻辑
            val path = getCustomCacheFile(url)
            val img = getBitmap(path)
            withContext(Dispatchers.Main) {
                // 回掉的代码都得在主线程
                if (img != null) {
                    result = img
                    success()
                } else {
                    loadFromShareIcon()
                }
            }
        }
    }

    private fun loadFromShareIcon() {
        ShareIconUtil.getIconBitmap(url, info, object : ShareIconUtil.ShareIconCallback {
            override fun onSuccess(bitmap: Bitmap) {
                try {
                    result = bitmap.copy(bitmap.config, false)
                    success()
                } catch (e: Exception) {
                    HLog.e(TAG, HLog.USR, "error-> $e")
                    fail()
                }
            }

            override fun onFail() {
                inMainThread {
                    fail()
                }
            }
        })
    }

    //历史遗留文件
    @Deprecated("history cache file")
    private fun getCustomCacheFile(url: String?): String {
        return LibBaseUtil.getApplication().cacheDir.absolutePath + File.separator + url2LocalPath(
            url,
            "cache_image"
        )
    }
}