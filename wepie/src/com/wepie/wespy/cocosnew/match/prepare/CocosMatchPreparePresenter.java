package com.wepie.wespy.cocosnew.match.prepare;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.component.gift.show.GiftSendScene;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.littlegame.event.GiftCommonSendEvent;
import com.huiwan.littlegame.event.IceGameCommandEvent;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.entity.User;
import com.huiwan.voiceservice.VoiceManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.gamematch.net.GameMatchSender;
import com.wejoy.littlegame.ILittleGameApi;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.littlegame.LittleGameKt;
import com.wejoy.littlegame.LittleGameSimpleInfo;
import com.wepie.lib.api.plugins.voice.SpeakCallback;
import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.MatchPacketPresenter;
import com.wepie.wespy.cocosnew.match.TeamVoiceManager;
import com.wepie.wespy.cocosnew.match.main.ar285.LittleGameTrackUtilsKt;
import com.wepie.wespy.cocosnew.match.matching.CocosMatchUtil;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.helper.shence.ShenceGameTypeSource;
import com.wepie.wespy.helper.shence.util.ShenceGiftUtil;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.event.iceball.IceStartMatchEvent;
import com.wepie.wespy.model.event.match.FinishMatchPrepareEvent;
import com.wepie.wespy.model.event.match.FinishTeamViewEvent;
import com.wepie.wespy.model.event.match.SyncTeamRspEvent;
import com.wepie.wespy.model.event.match.TeamKickOutEvent;
import com.wepie.wespy.net.http.api.GameApi;
import com.wepie.wespy.net.tcp.packet.GameMatchPackets;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;

/**
 * <AUTHOR> Yuu
 * email <EMAIL>
 * date 2017/11/9.
 * <p>
 * 冰球准备界面
 */
public class CocosMatchPreparePresenter {

    private final ICocosMatchPrepareView prepareView;
    private int gameType;
    private final int tid;
    private final int voiceType;

    public CocosMatchPreparePresenter(ICocosMatchPrepareView prepareView) {
        this.prepareView = prepareView;
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        tid = teamInfo.getTid();
        voiceType = teamInfo.getVoiceType();
    }

    public void init() {
        updateViews();
    }

    public void startMatch() {
        ShenceEvent.startMatch(getGameType());
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        onStartMatch(getGameType());
        MatchPacketPresenter.quickMatchReq2(0, teamInfo.getTid(), teamInfo.getMode(), teamInfo.getBetLevel(), gameType, teamInfo.gameMode, teamInfo.currencyType,
                new LifeSeqCallback(prepareView.getLife()) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        CocosMatchUtil.saveMatchInfo(head);
                        if (teamInfo.isOnePerson()) {
                            /* 单人进行 2v2 匹配时无 start match 推送 */
                            prepareView.jumpToMatch();
                        }
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        LittleGameSimpleInfo info = new LittleGameSimpleInfo(gameType,
                                teamInfo.getMode(), teamInfo.getGameMode(), teamInfo.getBetLevel(),
                                teamInfo.getCurrencyType());
                        CocosMatchUtil.handleFailMatch(prepareView.getViewContext(), LittleGameKt.GAME_SCENE_LITTLE_GAME_MATCH, info, head, LittleGameTrackUtilsKt.SCENE_LITTLE_GAME_MAIN, null);
                    }
                });
    }

    private void onStartMatch(int matchGameType) {
        PrefUserUtil.getInstance().setInt(PrefUserUtil.KEY_LAST_MATCH_GAME_TYPE, matchGameType);
    }

    /**
     * 推送，开始匹配
     *
     * @param startMatchEvent 开始匹配事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void pushStartMatch(IceStartMatchEvent startMatchEvent) {
        LittleGame.setTeamMatchExpectedTime(startMatchEvent.exceptTime);
        prepareView.jumpToMatch();
    }

    /**
     * @param event 同步事件 Response
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleSyncTeamResponse(SyncTeamRspEvent event) {
        if (event.teamInfo.tid == LittleGame.getTeamInfo().getTid()
                && !event.teamInfo.selfIsInTeam()) {//sync 发现自己不在队伍里
            prepareView.leave();
            ToastUtil.show(ResUtil.getStr(R.string.cocos_match_prepare_fail_not_in_team));
            return;
        }
        TeamInfo teamInfo = event.teamInfo;
        LittleGame.updateTeamInfo(teamInfo);
        updateViews();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleKickOut(TeamKickOutEvent event) {
        prepareView.leave();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerFinishEvent(FinishMatchPrepareEvent event) {
        prepareView.leave();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleFinishTeamView(FinishTeamViewEvent event) {
        prepareView.leave();
    }

    private void updateViews() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        GameConfig.MatchInfo matchInfo = ConfigHelper.getInstance().getGameConfig().getLittleGameMatchInfo(
                getGameType(), teamInfo.betLevel, teamInfo.mode, teamInfo.gameMode, teamInfo.currencyType);
        String text;
        if (matchInfo != null) {
            text = matchInfo.getTitle();
        } else {
            text = teamInfo.getModeName();
        }
        prepareView.updateTitle(text);
        prepareView.updateViews(teamInfo);
    }


    public void changeReadyState() {
        if (LittleGame.getTeamInfo().selfIsReady()) {
            unReady();
        } else {
            ready();
        }
    }

    private void ready() {
        GameMatchSender.readyReq(LittleGame.getTeamInfo().getTid(), new LifeSeqCallback(prepareView.getLife()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void unReady() {
        GameMatchSender.unreadyReq(LittleGame.getTeamInfo().getTid(), new LifeSeqCallback(prepareView.getLife()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }


    public void showInviteFriend() {
        prepareView.showInviteFriendDialog(LittleGame.getTeamInfo().getTid(), getGameType());
    }

    public void leave() {
        prepareView.leave();
        GameMatchSender.leaveTeamReq(LittleGame.getTeamInfo().getTid(), new LifeSeqCallback(prepareView.getLife()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void registerEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    public void unRegisterEventBus() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public int getGameType() {
        return gameType;
    }

    public void rematch() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (teamInfo.getTid() <= 0) return;
        final int level = teamInfo.getBetLevel();
        int mode = teamInfo.getMode();
        prepareView.showLoading();
        MatchPacketPresenter.createTeamReq2(mode, level, teamInfo.getGameMode(), getGameType(), teamInfo.getCurrencyType(), new LifeSeqCallback(prepareView.getLife()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                prepareView.hideLoading();
                GameMatchPackets.CreateTeamRsp2 createTeamRsp = (GameMatchPackets.CreateTeamRsp2) head.message;
                LittleGame.updateTeamInfo(TeamInfo.parse(createTeamRsp));
                updateViews();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                prepareView.hideLoading();
                LittleGameSimpleInfo info = new LittleGameSimpleInfo(gameType,
                        teamInfo.getMode(), teamInfo.getGameMode(), teamInfo.getBetLevel(),
                        teamInfo.getCurrencyType());
                boolean ret = ApiService.of(ILittleGameApi.class).handleEnterError(prepareView.getViewContext(), head.code,
                        LittleGameKt.GAME_SCENE_LITTLE_GAME_MATCH | LittleGameKt.EXCHANGE_TYPE_ENTER, info,
                        () -> {
                            rematch();
                            return Unit.INSTANCE;
                        });
                if (ret) {
                    return;
                }

                ToastUtil.show(head.desc);
            }
        });
    }

    public void sendGift(final GiftSendInfo sendInfo) {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (teamInfo != null) {
            for (int i = 0; i < teamInfo.players.size(); i++) {
                sendInfo.notifyUidList.add(teamInfo.players.get(i).uid);
            }
            GameApi.sendGiftCommon(sendInfo, new LifeDataCallback<>(prepareView.getLife()) {
                @Override
                public void onSuccess(Result<Object> result) {
                    Map<String, Object> map = new HashMap<>();
                    teamInfo.addTrackInfo(map);
                    ShenceGiftUtil.reportSendGift(sendInfo, ShenceGameTypeSource.getGameTypeShortSource(gameType), "", map);
                }

                @Override
                public void onFail(int code, String msg) {
                    ToastUtil.show(msg);
                }
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleCommonGift(GiftCommonSendEvent event) {
        final GiftShowInfo showInfo = event.showInfo;
        if (showInfo.sendScene == GiftSendScene.SceneCocosTeam.value) {
            GiftAnimUtil.getGiftApi().getBarrageContent(showInfo, s -> {
                showInfo.barrageAnimStr = s;
                prepareView.showGiftAnim(showInfo);
                return Unit.INSTANCE;
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onIceGameCommand(IceGameCommandEvent event) {
        if (event.command == IceGameCommandEvent.COMMAND_GAME_RETURN && GameConfig.isJackarooVip(LittleGame.getTeamInfo().game_type, LittleGame.getTeamInfo().gameMode)) {
            User user = LoginHelper.getLoginUser();
            if (user == null || !user.isVip())
                prepareView.leave();
        } else if (event.command == IceGameCommandEvent.COMMAND_CANCEL_MATCH_INVITE) {
            prepareView.leave();
        }
    }

    public void onResume() {
        if (TeamVoiceManager.getInstance().onActivityResume(tid, voiceType)) {
            registerSpeakCallback();
        }
    }

    public void clickSpeaker() {
        if (TeamVoiceManager.getInstance().clickSpeaker(tid, voiceType)) {
            registerSpeakCallback();
        }
    }

    public void registerSpeakCallback() {
        VoiceManager.getInstance().observeSpeak(prepareView.getLife(), new SpeakCallback() {
            @Override
            public void onSpeak(List<SpeakerInfo> speakerInfos) {
                prepareView.onSpeak(speakerInfos);
            }

            @Override
            public void uploadMemberId(int member) {

            }
        });
    }
}
