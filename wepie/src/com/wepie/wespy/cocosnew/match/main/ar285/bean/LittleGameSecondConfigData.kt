package com.wepie.wespy.cocosnew.match.main.ar285.bean

import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig
import com.wepie.wespy.cocosnew.match.main.ar285.LittleGameViewState


data class LittleGameSecondConfigData(
    val title: String = "",
    val itemInfos: List<LittleGameViewState.GameItemInfo> = emptyList(),
    val specifyHelpUrls: List<String> = emptyList()
)

fun VsCenterLittleGameConfig.PopupEntryLayout.toLittleGameSecondConfigData(): LittleGameSecondConfigData {
    val itemInfos = layoutList.map {
        LittleGameViewState.GameItemInfo(
            iconUrl = it.entryImg,
            jumpType = it.jumpType,
            jumpDetails = it.jumpDetails,
            catTwo = !it.isFullLine,
            reportComment = it.reportComment
        )
    }
    return LittleGameSecondConfigData(
        title = gameModeText,
        itemInfos = itemInfos
    )
}

fun VsCenterLittleGameConfig.JumpPop.totoLittleGameSecondConfigData(): LittleGameSecondConfigData {
    val itemInfos = list.map {
        LittleGameViewState.GameItemInfo(
            iconUrl = it.entryImg,
            jumpType = it.jumpType,
            jumpDetails = it.jumpDetails,
            catTwo = !it.isFullLine,
            reportComment = it.reportComment
        )
    }
    return LittleGameSecondConfigData(
        title = title,
        itemInfos = itemInfos
    )
}
