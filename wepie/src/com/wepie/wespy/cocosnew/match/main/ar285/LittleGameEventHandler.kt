package com.wepie.wespy.cocosnew.match.main.ar285

import android.app.Activity
import android.content.Context
import android.util.ArrayMap
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.lifecycleScope
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.Utils.getActivity
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.JKGameConfig
import com.huiwan.configservice.editionentity.GameConfig.MatchInfo
import com.huiwan.configservice.editionentity.ILittleGameMatchInfo
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_COCOS_GUIDE
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_CREATE
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_DEEPLINK
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_GUIDE_DIALOG
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_MATCH
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_QUICK_START
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_RANK
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_SECOND_DIALOG
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JumpDetails
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JumpMatchItem
import com.huiwan.configservice.editionentity.instance
import com.huiwan.constants.GameType
import com.huiwan.lib.api.impl
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.platform.ThreadUtil
import com.huiwan.store.PrefUtil
import com.huiwan.user.LoginHelper
import com.wejoy.gamematch.net.GameMatchSender
import com.wejoy.littlegame.ILittleGameApi
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.wejoy.weplay.ex.context.toLifecycleOwner
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackEvent
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.liblog.main.HLog
import com.wepie.liblog.main.HLog.USR
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.match.main.ar285.loader.LittleGameResUtil.checkLoadRes
import com.wepie.wespy.cocosnew.update.CocosResDebugVersionHelper
import com.wepie.wespy.cocosnew.util.LittleGameGuideDialogUtils
import com.wepie.wespy.helper.shence.ShenceEvent
import com.wepie.wespy.model.entity.EnterRoomInfo
import com.wepie.wespy.model.entity.match.TeamInfo
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.chat.invitegame.Cocos243Helper
import com.wepie.wespy.module.common.jump.JumpCommon
import com.wepie.wespy.module.common.jump.JumpRoomUtil
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.main.MainActivity
import com.wepie.wespy.module.makefriend.dataservice.VoiceRoomLastDataManager
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.RoomTypeConfig
import com.wepie.wespy.net.tcp.packet.GameMatchPackets
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSenderKt
import kotlinx.coroutines.launch

class LittleGameEventHandler {
    var gameType = 0

    fun canJumpCocosGuide(details: JumpDetails): Boolean {
        return details.matchItem.hasGuide && PrefUtil.getInstance()
            .getBoolean(PrefUtil.COCOS_IS_NEW_USER + gameType, true)
    }

    fun onClick(v: View, jumpType: Int, details: JumpDetails) {
        HLog.d(TAG, USR, "onClick: $jumpType, $details")
        when (jumpType) {
            JUMP_TYPE_MATCH, JUMP_TYPE_QUICK_START -> {
                checkShowsDialogs(v.context, gameType, details.matchItem) {
                    if (matchInCocos(gameType)) {
                        Cocos243Helper.gotoMainInCocos(
                            v.context,
                            gameType,
                            details.matchItem.gameMode
                        )
                    } else if (canJumpCocosGuide(details)) {
                        gotoCocosGuide(v.context)
                    } else {
                        reqMatch(v, details.matchItem)
                    }
                }
            }

            JUMP_TYPE_CREATE -> {
                if (matchInCocos(gameType)) {
                    checkLoadRes(v.context, gameType, false, SCENE_LITTLE_GAME_MAIN) {
                        Cocos243Helper.gotoMainInCocos(
                            v.context,
                            gameType,
                            details.matchItem.gameMode
                        )
                    }
                } else if ((details.matchItem?.betLevel ?: 0) > 0) {
                    onReqQuickCreate(v, details.matchItem)
                } else {
                    LittleGameCreateRoomDialog().showDialog(
                        v.context,
                        gameType,
                        details.matchItem.hasGuide
                    )
                }
            }

            JUMP_TYPE_DEEPLINK -> {
                val i = details.deeplink.indexOf('?')
                val deeplink = if (i < 0) {
                    details.deeplink + "?game_type=${gameType}"
                } else {
                    details.deeplink + "&game_type=${gameType}"
                }
                JumpCommon.gotoOtherPager(
                    v.context, deeplink, TrackUtil.getGameTypeSource(gameType), null
                )
            }

            JUMP_TYPE_GUIDE_DIALOG -> {
                showGuideDialog(v.context, details.matchItem, null)
            }

            JUMP_TYPE_SECOND_DIALOG -> {
                val pop = details.popItem
                if (pop != null) {
                    LittleGameSecondDialog().showDialog(
                        v.context, gameType, details.matchItem.gameMode, details.popItem
                    )
                } else {
                    HLog.e(TAG, USR, "server data error")
                }
            }

            JUMP_TYPE_RANK -> JumpUtil.gotoGameRankActivity(v.context, gameType)
            JUMP_TYPE_COCOS_GUIDE -> gotoCocosGuide(v.context)
            else -> {
                HLog.e(TAG, USR, "unsupported jump type: $jumpType")
            }
        }
    }

    private fun gotoCocosGuide(context: Context) {
        val builder = GameMatchPackets.StartGameReq.newBuilder()
            .setBetLevel(BET_LEVEL)
            .setMode(GAME_MODE)
            .setGameType(gameType)
        val localSpecifyGameVersion = CocosResDebugVersionHelper.loadVersion(gameType)
        if (localSpecifyGameVersion > 0) {
            builder.setForceGameType(gameType)
            builder.setForceGameVersion(localSpecifyGameVersion)
        }
        GameMatchSender.startGameReq(
            builder,
            object : LifeSeqCallback(ContextUtil.getLife(context)) {
                override fun onSuccess(head: RspHeadInfo) {
                    gotoCocosDirect(context)
                }

                override fun onFail(head: RspHeadInfo) {
                    if (head.code == RspHeadInfo.ERROR_CODE_VERSION_TOO_LOW) {
                        checkLoadRes(
                            context,
                            gameType,
                            true,
                            SCENE_LITTLE_GAME_MAIN
                        ) {
                            if (it) {
                                gotoCocosDirect(context)
                            }
                        }
                    }
                }
            })
    }

    private fun gotoCocosDirect(context: Context) {
        JumpUtil.gotoLittleGameActivity(
            context,
            false,
            0,
            VoiceRoomInfo().apply {
                game_type = gameType
            },
            "",
            false,
            true
        )
        trackUserGuide()
    }

    private fun vmByView(v: View): LittleGameMainViewModel {
        val owner = ContextUtil.getFragmentActivityFromContext(v.context) as ViewModelStoreOwner
        return ViewModelProvider(owner)[LittleGameMainViewModel::class.java]
    }

    fun onReqCreate(v: View, matchInfo: MatchInfo, hasCocosGuide: Boolean) {
        HLog.d(TAG, USR, "onReqCreate: ${matchInfo.descStr}")
        if (ConfigHelper.getInstance().voiceRoomConfig.isHomeSelfBuildLittleGame(gameType)) {
            createVoiceGameRoom(v.context, matchInfo, gameType)
            return
        }
        checkShowsDialogs(v.context, gameType, matchInfo) {
            if (hasCocosGuide && PrefUtil.getInstance()
                    .getBoolean(PrefUtil.COCOS_IS_NEW_USER + gameType, true)
            ) {
                gotoCocosGuide(v.context)
            } else {
                val vm = vmByView(v)
                vm.reqCreate(matchInfo)
            }
        }
    }

    private fun onReqQuickCreate(v: View, jumpMatchItem: JumpMatchItem) {
        if (ConfigHelper.getInstance().voiceRoomConfig.isHomeSelfBuildLittleGame(gameType)) {
            val userName = LoginHelper.getLoginUser()?.nickname ?: ""
            val roomName = ResUtil.getResource().getString(
                R.string.default_game_voice_room_title,
                StringUtil.subName(userName, 5)
            )
            val tmpGameBasicInfo = TmpRoomPackets.TmpGameBasicInfo.newBuilder()
                .setLittleGameType(gameType)
                .setBetLevel(jumpMatchItem.betLevel)
                .setMode(jumpMatchItem.roomMode)
                .setGameMode(jumpMatchItem.gameMode)
                .setCurrencyType(jumpMatchItem.currencyType)
                .build()
            v.context.toLifecycleOwner()?.lifecycleScope?.launch {
                requestCreateRoom(
                    v.context,
                    roomName,
                    tmpGameBasicInfo,
                    TmpRoomPackets.CreateRoomScene.CreateRoomSceneFromGame
                )
            }
            return
        }
        checkShowsDialogs(v.context, gameType, jumpMatchItem) {
            if (jumpMatchItem.hasGuide && PrefUtil.getInstance()
                    .getBoolean(PrefUtil.COCOS_IS_NEW_USER + gameType, true)
            ) {
                gotoCocosGuide(v.context)
            } else {
                val vm = vmByView(v)
                vm.reqCreate(jumpMatchItem)
            }
        }
    }

    private fun reqMatch(v: View, jumpMatchItem: JumpMatchItem) {
        val vm = vmByView(v)
        vm.reqMatch(jumpMatchItem)
    }

    fun showGuideDialog(context: Context, matchItem: JumpMatchItem, next: (() -> Unit)?) {
        val info = LittleGameSimpleInfo(
            gameType, matchItem.matchMode, matchItem.gameMode, matchItem.betLevel,
            matchItem.currencyType
        )
        ILittleGameApi::class.impl().showGameRuleDialog(context, info, next)
    }

    private fun trackUserGuide() {
        val map: MutableMap<String, Any> = ArrayMap()
        map["game_type"] = gameType
        map["step"] = GUIDE_STEP
        val isFirst = PrefUtil.getInstance().getBoolean(PrefUtil.COCOS_IS_NEW_USER + gameType, true)
        map["is_first"] = if (isFirst) "true" else "false"
        TrackUtil.trackEvent(TrackEvent.USER_GUIDE, map)
    }

    companion object {
        private const val BET_LEVEL = 1
        private const val GAME_MODE = 50
        private const val TAG = "LittleGameEventHandler"

        fun checkShowResOrPermissionDialog(
            context: Context,
            gameType: Int,
            info: ILittleGameMatchInfo,
            next: Runnable?
        ) {
            checkShowsDialogs(context, gameType, info, next)
        }

        private fun checkShowsDialogs(
            context: Context,
            gameType: Int,
            info: ILittleGameMatchInfo,
            next: Runnable?
        ) {
//            val activity = ContextUtil.getActivityFromContext(context) ?: return

            val nextTask = {
                checkLoadRes(context, gameType, true, SCENE_LITTLE_GAME_MAIN) {
                    val simpleInfo = LittleGameSimpleInfo(
                        gameType, info.matchMode, info.gameMode, info.betLevel, info.currencyType
                    )
                    if (info.hasCocosGuide()) {
                        next?.run()
                        return@checkLoadRes
                    }
                    val res =
                        LittleGameGuideDialogUtils.showGuideDialogByGameMode(
                            context,
                            simpleInfo,
                            next
                        )
                    if (!res) {
                        next?.run()
                    }
                }
            }
            nextTask.invoke()

//            CocosMatchUtil.requirePermission(activity, object : PermissionCallback {
//                override fun hasPermission(
//                    granted: List<String>,
//                    isAll: Boolean,
//                    alreadyHas: Boolean
//                ) {
//                    nextTask.invoke()
//                }
//
//                override fun noPermission(denied: List<String>, quick: Boolean) {
//                    CocosMatchUtil.showRefusedAudioTip(quick)
//                    nextTask.invoke()
//                }
//            })
        }

        /**
         * 支持进高级房和创建临时房
         */
        @JvmStatic
        fun createVoiceGameRoom(context: Context, matchInfo: MatchInfo, littleGameType: Int) {
            createVoiceGameRoom(context, matchInfo, littleGameType) { defaultFinishAction(it) }
        }

        /**
         * 支持进高级房和创建临时房
         */
        @JvmStatic
        fun createVoiceGameRoom(
            context: Context,
            matchInfo: MatchInfo,
            littleGameType: Int,
            finish: (activity: Activity?) -> Unit
        ) {
            context.toLifecycleOwner()?.lifecycleScope?.launch {
                val room = VoiceRoomPacketSenderKt.getUserAdvancedRoom()
                    ?: return@launch createTmpVoiceGameRoom(
                        context,
                        matchInfo,
                        littleGameType,
                        finish
                    )
                val advancedRoomsList =
                    room.advancedRoomsList.filter { it.roomType == VoiceRoomInfo.ROOM_TYPE_ADVANCED }
                //有高级房且没有开启，则进高级房然后切换游戏模式
                if (advancedRoomsList.isNotEmpty() && !advancedRoomsList.first().isOpen) {
                    val map: MutableMap<String, Any> = androidx.collection.ArrayMap()
                    map["sub_game_type"] = littleGameType
                    map["match_mode"] =
                        TeamInfo.trackMatchMode(matchInfo.gameMode, matchInfo.roomMode)
                    map["mode_type"] = TeamInfo.trackModeType(littleGameType, matchInfo.gameMode)
                    map["bet_level"] = matchInfo.betLevel
                    val enterRoomInfo = EnterRoomInfo
                        .buildEnterRoom(
                            context,
                            advancedRoomsList.first().rid,
                            GameType.GAME_TYPE_VOICE_GAME_ROOM
                        )
                        .setPreMatchInfo(matchInfo)
                        .setMatchInfoLittleGame(littleGameType)
                        .setSource(TrackSource.LITTLE_GAME)
                        .addTrackData(map)
                    JumpRoomUtil.getInstance().enterRoom(enterRoomInfo)
                    finish.invoke(ContextUtil.getActivityFromContext(context))
                } else {
                    if (advancedRoomsList.isNotEmpty()) {
                        ToastUtil.show(R.string.voice_game_adv_room_occupied)
                    }
                    createTmpVoiceGameRoom(context, matchInfo, littleGameType, finish)
                }
            }
        }

        @JvmStatic
        suspend fun createTmpVoiceGameRoom(
            context: Context,
            matchInfo: MatchInfo,
            littleGameType: Int,
            finish: (activity: Activity?) -> Unit
        ) {
            val userName = LoginHelper.getLoginUser()?.nickname ?: ""
            val roomName = ResUtil.getResource().getString(
                R.string.default_game_voice_room_title,
                StringUtil.subName(userName, 5)
            )
            val tmpGameBasicInfo = TmpRoomPackets.TmpGameBasicInfo.newBuilder()
                .setLittleGameType(littleGameType)
                .setBetLevel(matchInfo.betLevel)
                .setMode(matchInfo.roomMode)
                .setGameMode(matchInfo.gameMode)
                .setCurrencyType(matchInfo.currencyType)
                .build()
            requestCreateRoom(
                context,
                roomName,
                tmpGameBasicInfo,
                TmpRoomPackets.CreateRoomScene.CreateRoomSceneFromGame,
                finish
            )
        }

        @JvmStatic
        suspend fun requestCreateRoom(
            context: Context,
            roomName: String,
            tmpGameBasicInfo: TmpRoomPackets.TmpGameBasicInfo,
            scene: TmpRoomPackets.CreateRoomScene = TmpRoomPackets.CreateRoomScene.CreateRoomSceneUndefined,
            finish: (activity: Activity?) -> Unit = { defaultFinishAction(it) }
        ) {
            val activity = context.getActivity() as? BaseActivity
            activity?.getProDialogUtil()?.showLoadingDelay(context)
            val roomBgId = VoiceRoomLastDataManager.getInstance().voiceRoomBg
            val roomNote = VoiceRoomLastDataManager.getInstance().voiceRoomNote
            val littleGameType = tmpGameBasicInfo.littleGameType
            val label = JKGameConfig::class.instance().getLabelByGameType(littleGameType)
            val result = VoiceRoomPacketSenderKt.createVoiceRoom(
                rid = 0,
                roomName = roomName,
                pwd = "",
                gameType = GameType.GAME_TYPE_VOICE_GAME_ROOM,
                labelType = label,
                bgThemeId = roomBgId,
                note = roomNote,
                roomType = RoomTypeConfig.ROOM_TYPE_NORMAL,
                tmpGameBasicInfo = tmpGameBasicInfo,
                scene = scene
            )

            if (!result.codeOk()) {
                activity?.getProDialogUtil()?.hideLoading()
                ToastUtil.show(result.desc)
                return
            }


            activity?.getProDialogUtil()?.hideLoading()
            val voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo(result.rid)
            JumpRoomUtil.getInstance().jumpNewActivityAfterEnterRoom(
                context, voiceRoomInfo, 0, false, null, false
            )
            val map: MutableMap<String, Any> = androidx.collection.ArrayMap()
            map["sub_game_type"] = littleGameType
            map["match_mode"] =
                TeamInfo.trackMatchMode(tmpGameBasicInfo.gameMode, tmpGameBasicInfo.mode)
            map["mode_type"] =
                TeamInfo.trackModeType(tmpGameBasicInfo.littleGameType, tmpGameBasicInfo.gameMode)
            map["bet_level"] = tmpGameBasicInfo.betLevel
            ShenceEvent.enterVoiceRoom(TrackSource.LITTLE_GAME, voiceRoomInfo, map)
            VoiceRoomLastDataManager.getInstance()
                .setVoiceRoomName(voiceRoomInfo.name, voiceRoomInfo.game_type)
            VoiceRoomLastDataManager.getInstance()
                .setVoiceRoomLabel(voiceRoomInfo.labelType, voiceRoomInfo.game_type)
            VoiceRoomLastDataManager.getInstance().voiceRoomGameType = voiceRoomInfo.game_type
            finish.invoke(activity)
        }

        @JvmStatic
        private fun defaultFinishAction(activity: Activity?) {
            if (activity !is MainActivity && activity != null) {
                HLog.d(TAG, USR, "to finish activity: {}", activity.javaClass.simpleName)
                ThreadUtil.runOnUiThreadDelay(2000) {
                    activity.finish()
                }
            }
        }
    }

    private fun matchInCocos(gameType: Int): Boolean {
        return ConfigHelper.getInstance().getGameConfig(gameType).isSupportCocosMatch
    }
}