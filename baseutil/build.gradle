apply from: "../base_lib.gradle"

android {
    buildFeatures {
        buildConfig true
    }

    defaultConfig {

        buildConfigField "String", "BUILD_AREA", "\"${rootProject.ext.buildArea}\""
        buildConfigField "String", "AREA_CHINA", "\"${rootProject.ext.china}\""
        buildConfigField "String", "AREA_OVERSEAS", "\"${rootProject.ext.overseas}\""

        buildConfigField "String", "SERVER_CONFIG", "\"$SERVER_CONFIG\""
        buildConfigField "String", "CH_SERVER_CONFIG", "\"$CH_SERVER_CONFIG\""
        buildConfigField "String", "US_SERVER_CONFIG", "\"$US_SERVER_CONFIG\""
        buildConfigField "String", "JA_SERVER_CONFIG", "\"$JA_SERVER_CONFIG\""
        buildConfigField "String", "AR_SERVER_CONFIG", "\"$AR_SERVER_CONFIG\""
        buildConfigField "String[]", "LANGUAGE_ORDER", "${rootProject.ext.appConfig.LANGUAGE_ORDER}"
        buildConfigField "String", "BASE_FILE_DIR", "\"${rootProject.ext.appConfig.BASE_FILE_DIR}\""
        buildConfigField "String", "PHOTO_FILE_DIR", "\"${rootProject.ext.appConfig.PHOTO_FILE_DIR}\""
    }

}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    api project(":lib:thread-util")
    api project(":lib:startup")
    implementation project(path: ':lib:libproto')

    api libs.ex
    api libs.gson
    api libs.eventBus
    implementation(rootProject.ext.USE_DEV_SEC_LIB ? libs.security.dev : libs.security.release)
    implementation libs.exifinterface
    implementation libs.glide
}