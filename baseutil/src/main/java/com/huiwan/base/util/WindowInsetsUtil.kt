package com.huiwan.base.util

import android.graphics.Rect
import android.util.Log
import android.view.View
import androidx.annotation.Size
import androidx.collection.ArrayMap
import androidx.core.view.OnApplyWindowInsetsListener
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.huiwan.base.R

object WindowInsetsUtil {
    @JvmStatic
    fun setOnApplyWindowInsetsListener(view: View?, listener: OnApplyWindowInsetsListener) {
        view ?: return
        val activity = ContextUtil.getActivityFromContext(view.context)
        if (activity == null) {
            ViewCompat.setOnApplyWindowInsetsListener(view, listener)
            return
        }
        val rootView = activity.findViewById<View>(android.R.id.content)
        if (rootView == null) {
            ViewCompat.setOnApplyWindowInsetsListener(view, listener)
            return
        }
        Log.e("jhk", "setOnApplyWindowInsetsListener===$view")
        val windowInsetsListener: RootViewApplyWindowInsetsListener
        val tag = rootView.getTag(R.id.view_window_inset_listener_tag)
        if (tag is RootViewApplyWindowInsetsListener) {
            windowInsetsListener = tag
        } else {
            windowInsetsListener = RootViewApplyWindowInsetsListener(rootView)
            rootView.setTag(R.id.view_window_inset_listener_tag, windowInsetsListener)
            ViewCompat.setOnApplyWindowInsetsListener(rootView, windowInsetsListener)
        }
        windowInsetsListener.register(view, listener)
    }

    @JvmStatic
    fun fitRecyclerView(rv: RecyclerView) {
        setOnApplyWindowInsetsListener(rv, object : OnApplyWindowInsetsListener {

            private var decoration: ItemDecoration? = null
            private var bottom = 0

            override fun onApplyWindowInsets(
                v: View, insets: WindowInsetsCompat
            ): WindowInsetsCompat {
                val systemWindow = insets.getInsets(
                    WindowInsetsCompat.Type.systemBars() or WindowInsetsCompat.Type.displayCutout()
                )
                if (bottom != systemWindow.bottom) {
                    bottom = systemWindow.bottom
                    decoration?.let { rv.removeItemDecoration(it) }
                    addBottomItemDecoration()
                }
                return insets
            }

            private fun addBottomItemDecoration() {
                if (bottom <= 0) {
                    return
                }
                val decoration = object : ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect, view: View, parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        val adapter = parent.adapter ?: return
                        val manager = parent.layoutManager ?: return
                        val count: Int = adapter.itemCount
                        val index = parent.getChildAdapterPosition(view)
                        var flag = false
                        if (manager is GridLayoutManager) {
                            flag = setGridOffset(
                                manager.orientation, manager.spanCount,
                                manager.spanSizeLookup, index, count
                            )
                        } else if (manager is LinearLayoutManager) {
                            flag = setLinearOffset(manager.orientation, index, count)
                        }
                        if (flag) {
                            outRect.bottom = bottom
                        }
                    }

                    @Size(4)
                    private fun setLinearOffset(
                        orientation: Int,
                        childPosition: Int,
                        itemCount: Int
                    ): Boolean {
                        if (childPosition == itemCount - 1) {
                            return orientation == LinearLayoutManager.VERTICAL
                        }
                        return false
                    }

                    @Size(4)
                    private fun setGridOffset(
                        orientation: Int,
                        spanCount: Int,
                        lookup: GridLayoutManager.SpanSizeLookup,
                        childPosition: Int,
                        itemCount: Int
                    ): Boolean {
                        val spanIndex = lookup.getSpanIndex(childPosition, spanCount)
                        val spanSize = lookup.getSpanSize(childPosition)
                        val group = lookup.getSpanGroupIndex(childPosition, spanCount)
                        val totalGroup = lookup.getSpanGroupIndex(itemCount - 1, spanCount)
                        return if (orientation == GridLayoutManager.VERTICAL) {
                            group == totalGroup
                        } else {
                            (spanIndex + spanSize) == spanCount
                        }
                    }
                }
                rv.addItemDecoration(decoration)
                this.decoration = decoration
            }
        })
    }

    class RootViewApplyWindowInsetsListener(rootView: View) : OnApplyWindowInsetsListener {
        private var current: WindowInsetsCompat? = null
        private val listenerMap: MutableMap<View, OnApplyWindowInsetsListener> = ArrayMap()

        init {
            rootView.rootWindowInsets?.let {
                current = WindowInsetsCompat.toWindowInsetsCompat(it)
            }
        }

        override fun onApplyWindowInsets(v: View, insets: WindowInsetsCompat): WindowInsetsCompat {
            Log.e("jhk", "RootViewApplyWindowInsetsListener =onApplyWindowInsets===$insets")
            listenerMap.forEach { (view, listener) ->
                listener.onApplyWindowInsets(view, insets)
            }
            current = WindowInsetsCompat(insets)
            return insets
        }

        fun addListener(view: View, listener: OnApplyWindowInsetsListener) {
            listenerMap.put(view, listener)
            current?.let {
                listener.onApplyWindowInsets(view, it)
            }
        }

        fun removeListener(view: View) {
            listenerMap.remove(view)
        }

        fun register(v: View, listener: OnApplyWindowInsetsListener) {
            if (v.isAttachedToWindow) {
                addListener(v, listener)
            }
            v.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
                override fun onViewAttachedToWindow(v: View) {
                    addListener(v, listener)
                }

                override fun onViewDetachedFromWindow(v: View) {
                    removeListener(v)
                }
            })
        }
    }
}