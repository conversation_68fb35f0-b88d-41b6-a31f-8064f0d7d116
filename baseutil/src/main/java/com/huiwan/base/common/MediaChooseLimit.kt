package com.huiwan.base.common

import com.google.gson.annotations.SerializedName

/*
    "msg_file_upload_limit": {
    "video_duration": 60, // 视频时长 单位s
    "video_raw_size_limit": 500, // 视频压缩前大小限制 单位mb
    "video_zipped_size_limit": 300, // 视频压缩后大小限制 单位mb
    "origin_pic_size_limit": 50 // 原图大小限制 单位mb
  }
 */
class MediaChooseLimit(var maxSelect: Int = 9, var enableOrigin: Boolean = false) :
    java.io.Serializable {
    @SerializedName("video_duration")
    var videoDuration = 60

    @SerializedName("video_raw_size_limit")
    var videoRawSizeLimit = 500

    @SerializedName("video_zipped_size_limit")
    var videoZippedSizeLimit = 300

    @SerializedName("origin_pic_size_limit")
    var originPicSizeLimit = 50

    @SerializedName("finishText")
    var finishText = ""
    @SerializedName("isSupportAllMedias")
    var isSupportAllMedias = false
    var haveCapture = false
    var isSelectHead = false
    var clipPhotoRate: Float = -1f

    companion object {
        fun build(maxSelect: Int, enableOrigin: Boolean): MediaChooseLimit {
            return MediaChooseLimit(maxSelect, enableOrigin)
        }
    }

    fun init() {
        finishText = ""
        isSupportAllMedias = false
        maxSelect = 9
        enableOrigin = false
        haveCapture = false
        isSelectHead = false
        clipPhotoRate = -1f
    }

    fun isSingleChat(): Boolean {
        return maxSelect == 1
    }

    override fun toString(): String {
        return "MediaChooseLimit(maxSelect=$maxSelect, enableOrigin=$enableOrigin, videoDuration=$videoDuration, videoRawSizeLimit=$videoRawSizeLimit, videoZippedSizeLimit=$videoZippedSizeLimit, originPicSizeLimit=$originPicSizeLimit, finishText='$finishText', isSupportAllMedias=$isSupportAllMedias)"
    }

}