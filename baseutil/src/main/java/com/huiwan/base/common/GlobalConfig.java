package com.huiwan.base.common;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import com.huiwan.base.util.AUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.TextUtil;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;

import java.io.IOException;
import java.io.Serializable;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class GlobalConfig {

    public static final String KEY_REGION = "region";
    public static final String KEY_CLIENT_KEY_INFO = "client_key_info";
    private static final String KEY_HOST = "host";
    private static final String KEY_PORT = "port";
    private static final String KEY_BACKUP_ADDRESS = "backup_address";
    public static final String KEY_CDN_BACKUP = "cdn_backup_all";
    public static final String KEY_TWITTER_LOGIN = "twitter_login_config";

    public String region = "";

    public volatile String clientKeyInfo = "";
    private volatile VoiceKInfo voiceConfig = null;

    public Map<String, ConfigItem> configMap = new ArrayMap<>();

    public Map<String, List<String>> cdnMap = Collections.emptyMap();
    public Map<String, String> twitterConfig = new ArrayMap<>(4);

    public GlobalConfig() {
        twitterConfig.put("client_id", "Njd3OUVNVm0zX3RSTzlfR3owWmc6MTpjaQ");
        twitterConfig.put("secret", "5Vyha8t3XJE5Ys4BsPj4Htxbnch3inTd7JYuOf5Sl6Vj35nUsU");
        twitterConfig.put("key", "*************************");
        twitterConfig.put("redirect_uri", "twitterkit-*************************://");
    }

    public void update(GlobalConfig config) {
        this.region = config.region;
        this.clientKeyInfo = config.clientKeyInfo;
        if (configMap == null) {
            configMap = new ArrayMap<>();
        } else {
            configMap.clear();
        }
        configMap.putAll(config.configMap);
    }

    @NonNull
    public VoiceKInfo getVoiceConfig() {
        if (voiceConfig != null && TextUtils.equals(clientKeyInfo, voiceConfig.clientKeyInfo)) {
            return voiceConfig;
        }
        String json = AUtil.aOutTextClientKey20211(clientKeyInfo);
        VoiceKInfo voiceKInfo = VoiceKInfo.parse(json);
        if (voiceKInfo == null) {
            voiceKInfo = new VoiceKInfo();
        } else {
            voiceKInfo.clientKeyInfo = clientKeyInfo;
        }
        this.voiceConfig = voiceKInfo;
        return voiceKInfo;
    }

    public ConfigItem getItem(String serverName) {
        if (configMap == null) {
            return null;
        }
        return configMap.get(serverName);
    }

    public String toJson() {
        try (StringWriter writer = new StringWriter();
             JsonWriter jsonWriter = new JsonWriter(writer)) {
            jsonWriter.beginObject();
            jsonWriter.name(KEY_REGION);
            jsonWriter.value(region);
            jsonWriter.name(KEY_CLIENT_KEY_INFO);
            jsonWriter.value(clientKeyInfo);
            if (configMap != null) {
                for (Map.Entry<String, ConfigItem> entry : configMap.entrySet()) {
                    jsonWriter.name(entry.getKey());
                    writeConfigItem(jsonWriter, entry.getValue());
                }
            }
            if (cdnMap != null) {
                jsonWriter.name(KEY_CDN_BACKUP);
                jsonWriter.beginObject();
                for (Map.Entry<String, List<String>> entry : cdnMap.entrySet()) {
                    jsonWriter.name(entry.getKey());
                    jsonWriter.beginArray();
                    for (String s : entry.getValue()) {
                        jsonWriter.value(s);
                    }
                    jsonWriter.endArray();
                }
                jsonWriter.endObject();
            }
            if (twitterConfig != null) {
                jsonWriter.name(KEY_TWITTER_LOGIN);
                jsonWriter.beginObject();
                for (Map.Entry<String, String> entry : twitterConfig.entrySet()) {
                    jsonWriter.name(entry.getKey());
                    jsonWriter.value(entry.getValue());
                }
                jsonWriter.endObject();
            }
            jsonWriter.endObject();
            return writer.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private void writeConfigItem(JsonWriter writer, GlobalConfig.ConfigItem item) throws IOException {
        writer.beginObject();
        writer.name(KEY_HOST);
        writer.value(item.host);
        writer.name(KEY_PORT);
        writer.value(item.port);
        List<ConfigItem> list = item.configItems;
        if (list != null && !list.isEmpty()) {
            writer.name(KEY_BACKUP_ADDRESS);
            writer.beginArray();
            for (ConfigItem item1 : list) {
                writeConfigItem(writer, item1);
            }
            writer.endArray();
        }
        writer.endObject();
    }

    private GlobalConfig.ConfigItem readConfigItem(JsonReader reader) throws IOException {
        GlobalConfig.ConfigItem item = new GlobalConfig.ConfigItem();
        reader.beginObject();
        try {
            while (reader.hasNext()) {
                String childName = reader.nextName();
                if (reader.peek() == JsonToken.NULL) {
                    reader.skipValue();
                    continue;
                }
                switch (childName) {
                    case GlobalConfig.KEY_HOST:
                        item.host = reader.nextString();
                        break;
                    case GlobalConfig.KEY_PORT:
                        item.port = reader.nextInt();
                        break;
                    case GlobalConfig.KEY_BACKUP_ADDRESS:
                        reader.beginArray();
                        while (reader.hasNext()) {
                            item.configItems.add(readConfigItem(reader));
                        }
                        reader.endArray();
                        item.totalConfigItems.addAll(item.configItems);
                        item.totalConfigItems.add(item);
                        break;
                    default:
                        reader.skipValue();
                }
            }
        } finally {
            reader.endObject();
        }
        return item;
    }

    private static Map<String, List<String>> readCdnConfig(JsonReader reader) throws IOException {
        Map<String, List<String>> map = new ArrayMap<>();
        reader.beginObject();
        while (reader.hasNext()) {
            String name = reader.nextName();
            if (reader.peek() != JsonToken.BEGIN_ARRAY) {
                reader.skipValue();
                continue;
            }
            reader.beginArray();
            List<String> list = new ArrayList<>();
            while (reader.hasNext()) {
                String s = reader.nextString();
                if (!TextUtils.isEmpty(s)) {
                    list.add(s);
                }
            }
            reader.endArray();
            map.put(name, list);
        }
        reader.endObject();
        return map;
    }

    private static Map<String, String> readTwitterConfig(JsonReader reader) throws IOException {
        Map<String, String> map = new ArrayMap<>();
        reader.beginObject();
        while (reader.hasNext()) {
            String name = reader.nextName();
            if (reader.peek() == JsonToken.NULL) {
                reader.skipValue();
                continue;
            }
            map.put(name, reader.nextString());
        }
        reader.endObject();
        return map;
    }

    public static GlobalConfig parse(String json) {
        StringReader stringReader = new StringReader(json);
        JsonReader reader = new JsonReader(stringReader);
        return parse(reader);
    }

    public static GlobalConfig parse(JsonReader reader) {
        GlobalConfig config = new GlobalConfig();
        try {
            reader.beginObject();
            while (reader.hasNext()) {
                String name = reader.nextName();
                if (reader.peek() == JsonToken.NULL) {
                    reader.skipValue();
                    continue;
                }
                switch (name) {
                    case GlobalConfig.KEY_REGION:
                        config.region = reader.nextString();
                        break;
                    case GlobalConfig.KEY_CLIENT_KEY_INFO:
                        config.clientKeyInfo = reader.nextString();
                        break;
                    case GlobalConfig.KEY_CDN_BACKUP:
                        config.cdnMap = readCdnConfig(reader);
                        break;
                    case GlobalConfig.KEY_TWITTER_LOGIN:
                        config.twitterConfig = readTwitterConfig(reader);
                        break;
                    default:
                        GlobalConfig.ConfigItem item = config.readConfigItem(reader);
                        if (!TextUtil.isEmpty(item.host)) {
                            config.configMap.put(name, item);
                        }
                }
            }
            reader.endObject();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return config;
    }

    public static class ConfigItem {
        @SerializedName(KEY_HOST)
        public String host;

        public transient String ip;

        @SerializedName(KEY_PORT)
        public int port;
        @SerializedName(KEY_BACKUP_ADDRESS)
        public List<ConfigItem> configItems = new ArrayList<>();
        public transient List<ConfigItem> totalConfigItems = new ArrayList<>();

        public ConfigItem() {
        }

        public ConfigItem(ConfigItem item) {
            this.host = item.host;
            this.ip = item.ip;
            this.port = item.port;
            this.configItems.addAll(item.configItems);
            this.totalConfigItems.addAll(item.totalConfigItems);
        }

        // get backup config if has
        public GlobalConfig.ConfigItem getBackUpConfig() {
            GlobalConfig.ConfigItem configItem = null;
            for (int i = 0; i < totalConfigItems.size(); i++) {
                if (this.equals(totalConfigItems.get(i))) {
                    int nextIndex = i + 1;
                    if (nextIndex >= totalConfigItems.size()) {
                        nextIndex = 0;
                    }
                    configItem = totalConfigItems.get(nextIndex);
                }
            }
            if (null == configItem) {
                configItem = this;
            }
            if (configItem.totalConfigItems.isEmpty()) {
                configItem.totalConfigItems.addAll(totalConfigItems);
            }
            return configItem;
        }

        @Override
        public boolean equals(@Nullable Object obj) {
            if (obj instanceof ConfigItem) {
                ConfigItem item = (ConfigItem) obj;
                return host.equals(item.host) && port == item.port;
            }
            return false;
        }

        @Override
        @NonNull
        public String toString() {
            return "ConfigItem{" +
                    "host='" + host + '\'' +
                    ", port=" + port +
                    '}';
        }
    }

    public static class VoiceKInfo implements Serializable {
        public transient String clientKeyInfo = "";

        @SerializedName("zego_info")
        private ZegoInfoBean zegoInfo = new ZegoInfoBean();
        @SerializedName("tx_lite_app_id")
        private String txLiteAppId = "";

        @SerializedName("tx_video_app_id")
        private String txVideoAppId = "";

        public static class ZegoInfoBean implements Serializable {
            @SerializedName("audio_app_id")
            private String audioAppId = "";
            @SerializedName("audio_app_key")
            private String audioAppKey = "";

            @SerializedName("msg_battle_app_id")
            private String msgBattleAppId = "";
            @SerializedName("msg_battle_app_key")
            private String msgBattleAppKey = "";
        }

        public int getTxLiteAppId(int mediaType) {
            try {
                if (mediaType == TmpRoomPackets.MediaType.MEDIA_TYPE_VIDEO_VALUE) {
                    return Integer.parseInt(txVideoAppId);
                } else {
                    return Integer.parseInt(txLiteAppId);
                }
            } catch (Exception e) {
                e.printStackTrace();
                return 0;
            }
        }

        public long getZegoAudioAppID() {
            try {
                return Long.parseLong(zegoInfo.audioAppId);
            } catch (Exception e) {
                e.printStackTrace();
                return 0;
            }
        }

        public String getZegoAudioAppKey() {
            return zegoInfo.audioAppKey;
        }

        public long getZegoMsgBattleAppID() {
            try {
                return Long.parseLong(zegoInfo.msgBattleAppId);
            } catch (Exception e) {
                e.printStackTrace();
                return 0;
            }
        }

        public String getZegoMsgBattleAppKey() {
            return zegoInfo.msgBattleAppKey;
        }

        @Nullable
        public static VoiceKInfo parse(String json) {
            return JsonUtil.parseJson(json, VoiceKInfo.class);
        }
    }
}