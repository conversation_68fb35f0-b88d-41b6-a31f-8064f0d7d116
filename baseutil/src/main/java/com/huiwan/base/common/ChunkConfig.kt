package com.huiwan.base.common

import com.google.gson.annotations.SerializedName

class ChunkConfig {
    @SerializedName("open")
    var open = true

    @SerializedName("max_fragment_count")
    var maxChunkNumber = 5

    @SerializedName("min_fragment_size")
    var minChunkSize = (1024 * 1024).toLong()

    @Transient
    var chunkReadSize = 1024 * 4

    @SerializedName("temp_file_expired_hours")
    var chunksSaveTime: Long = 3 * 24

    @SerializedName("free_disk_size_threshold")
    var freeDiskSizeThreshhold = (200 * 1024 * 1024).toLong()
}